{"name": "@jest/core", "description": "Delightful JavaScript Testing.", "version": "27.5.1", "main": "./build/jest.js", "types": "./build/jest.d.ts", "exports": {".": {"types": "./build/jest.d.ts", "default": "./build/jest.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/console": "^27.5.1", "@jest/reporters": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/transform": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.8.1", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^27.5.1", "jest-config": "^27.5.1", "jest-haste-map": "^27.5.1", "jest-message-util": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-resolve-dependencies": "^27.5.1", "jest-runner": "^27.5.1", "jest-runtime": "^27.5.1", "jest-snapshot": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "jest-watcher": "^27.5.1", "micromatch": "^4.0.4", "rimraf": "^3.0.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "devDependencies": {"@jest/test-sequencer": "^27.5.1", "@jest/test-utils": "^27.5.1", "@types/exit": "^0.1.30", "@types/graceful-fs": "^4.1.2", "@types/micromatch": "^4.0.1", "@types/rimraf": "^3.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "repository": {"type": "git", "url": "https://github.com/facebook/jest", "directory": "packages/jest-core"}, "bugs": {"url": "https://github.com/facebook/jest/issues"}, "homepage": "https://jestjs.io/", "license": "MIT", "keywords": ["ava", "babel", "coverage", "easy", "expect", "facebook", "immersive", "instant", "jasmine", "jest", "jsdom", "mocha", "mocking", "painless", "qunit", "runner", "sandboxed", "snapshot", "tap", "tape", "test", "testing", "typescript", "watch"], "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}