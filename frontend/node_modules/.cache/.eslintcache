[{"/home/<USER>/itai/frontend/src/index.tsx": "1", "/home/<USER>/itai/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx": "3", "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx": "4", "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx": "5", "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx": "6", "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx": "7", "/home/<USER>/itai/frontend/src/services/api.ts": "8", "/home/<USER>/itai/frontend/src/pages/TestPage.tsx": "9", "/home/<USER>/itai/frontend/src/App.tsx": "10"}, {"size": 554, "mtime": 1751425251890, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1751425251890, "results": "13", "hashOfConfig": "12"}, {"size": 4643, "mtime": 1751438726439, "results": "14", "hashOfConfig": "12"}, {"size": 16211, "mtime": 1751437933384, "results": "15", "hashOfConfig": "12"}, {"size": 14048, "mtime": 1751438074547, "results": "16", "hashOfConfig": "12"}, {"size": 17117, "mtime": 1751438403794, "results": "17", "hashOfConfig": "12"}, {"size": 14673, "mtime": 1751438196836, "results": "18", "hashOfConfig": "12"}, {"size": 12728, "mtime": 1751439515631, "results": "19", "hashOfConfig": "12"}, {"size": 5827, "mtime": 1751439544299, "results": "20", "hashOfConfig": "12"}, {"size": 359, "mtime": 1751439356758, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1plfo14", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/itai/frontend/src/index.tsx", [], [], "/home/<USER>/itai/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx", ["52", "53"], [], "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx", ["54", "55", "56", "57", "58"], [], "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx", ["59", "60"], [], "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx", ["61", "62", "63", "64"], [], "/home/<USER>/itai/frontend/src/services/api.ts", [], [], "/home/<USER>/itai/frontend/src/pages/TestPage.tsx", ["65", "66", "67"], [], "/home/<USER>/itai/frontend/src/App.tsx", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 18, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 18, "endColumn": 7}, {"ruleId": "68", "severity": 1, "message": "72", "line": 19, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 19, "endColumn": 8}, {"ruleId": "68", "severity": 1, "message": "73", "line": 9, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 9, "endColumn": 7}, {"ruleId": "68", "severity": 1, "message": "74", "line": 33, "column": 9, "nodeType": "70", "messageId": "71", "endLine": 33, "endColumn": 15}, {"ruleId": "68", "severity": 1, "message": "75", "line": 52, "column": 11, "nodeType": "70", "messageId": "71", "endLine": 52, "endColumn": 25}, {"ruleId": "76", "severity": 1, "message": "77", "line": 118, "column": 6, "nodeType": "78", "endLine": 118, "endColumn": 8, "suggestions": "79"}, {"ruleId": "76", "severity": 1, "message": "80", "line": 122, "column": 6, "nodeType": "78", "endLine": 122, "endColumn": 22, "suggestions": "81"}, {"ruleId": "68", "severity": 1, "message": "82", "line": 73, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 73, "endColumn": 17}, {"ruleId": "76", "severity": 1, "message": "83", "line": 190, "column": 6, "nodeType": "78", "endLine": 190, "endColumn": 22, "suggestions": "84"}, {"ruleId": "68", "severity": 1, "message": "85", "line": 17, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 17, "endColumn": 9}, {"ruleId": "68", "severity": 1, "message": "86", "line": 27, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 27, "endColumn": 19}, {"ruleId": "68", "severity": 1, "message": "87", "line": 29, "column": 3, "nodeType": "70", "messageId": "71", "endLine": 29, "endColumn": 15}, {"ruleId": "68", "severity": 1, "message": "88", "line": 64, "column": 10, "nodeType": "70", "messageId": "71", "endLine": 64, "endColumn": 17}, {"ruleId": "68", "severity": 1, "message": "89", "line": 1, "column": 27, "nodeType": "70", "messageId": "71", "endLine": 1, "endColumn": 36}, {"ruleId": "68", "severity": 1, "message": "69", "line": 2, "column": 40, "nodeType": "70", "messageId": "71", "endLine": 2, "endColumn": 44}, {"ruleId": "68", "severity": 1, "message": "90", "line": 3, "column": 21, "nodeType": "70", "messageId": "71", "endLine": 3, "endColumn": 30}, "@typescript-eslint/no-unused-vars", "'Spin' is defined but never used.", "Identifier", "unusedVar", "'Alert' is defined but never used.", "'Form' is defined but never used.", "'Option' is assigned a value but never used.", "'ContainerStats' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadServers'. Either include it or remove the dependency array.", "ArrayExpression", ["91"], "React Hook useEffect has a missing dependency: 'loadDockerData'. Either include it or remove the dependency array.", ["92"], "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadMonitoringData'. Either include it or remove the dependency array.", ["93"], "'Upload' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'CodeOutlined' is defined but never used.", "'servers' is assigned a value but never used.", "'useEffect' is defined but never used.", "'dockerApi' is defined but never used.", {"desc": "94", "fix": "95"}, {"desc": "96", "fix": "97"}, {"desc": "98", "fix": "99"}, "Update the dependencies array to be: [loadServers]", {"range": "100", "text": "101"}, "Update the dependencies array to be: [loadDockerData, selectedServer]", {"range": "102", "text": "103"}, "Update the dependencies array to be: [loadMonitoringData, selectedServer]", {"range": "104", "text": "105"}, [2527, 2529], "[loadServers]", [2580, 2596], "[loadDockerData, selectedServer]", [4388, 4404], "[loadMonitoringData, selectedServer]"]