[{"/home/<USER>/itai/frontend/src/index.tsx": "1", "/home/<USER>/itai/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/itai/frontend/src/App.tsx": "3", "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx": "4", "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx": "5", "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx": "6", "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx": "7", "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx": "8"}, {"size": 554, "mtime": 1751425251890, "results": "9", "hashOfConfig": "10"}, {"size": 425, "mtime": 1751425251890, "results": "11", "hashOfConfig": "10"}, {"size": 367, "mtime": 1751425726064, "results": "12", "hashOfConfig": "10"}, {"size": 4444, "mtime": 1751427643689, "results": "13", "hashOfConfig": "10"}, {"size": 12065, "mtime": 1751425904900, "results": "14", "hashOfConfig": "10"}, {"size": 12498, "mtime": 1751426667945, "results": "15", "hashOfConfig": "10"}, {"size": 12604, "mtime": 1751426771081, "results": "16", "hashOfConfig": "10"}, {"size": 15353, "mtime": 1751426722553, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1plfo14", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/itai/frontend/src/index.tsx", [], [], "/home/<USER>/itai/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/itai/frontend/src/App.tsx", ["42"], [], "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx", ["43", "44", "45", "46"], [], "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx", ["47", "48", "49", "50"], [], {"ruleId": "51", "severity": 1, "message": "52", "line": 2, "column": 26, "nodeType": "53", "messageId": "54", "endLine": 2, "endColumn": 32}, {"ruleId": "51", "severity": 1, "message": "55", "line": 32, "column": 9, "nodeType": "53", "messageId": "54", "endLine": 32, "endColumn": 15}, {"ruleId": "51", "severity": 1, "message": "56", "line": 51, "column": 11, "nodeType": "53", "messageId": "54", "endLine": 51, "endColumn": 25}, {"ruleId": "51", "severity": 1, "message": "57", "line": 74, "column": 10, "nodeType": "53", "messageId": "54", "endLine": 74, "endColumn": 24}, {"ruleId": "51", "severity": 1, "message": "58", "line": 74, "column": 26, "nodeType": "53", "messageId": "54", "endLine": 74, "endColumn": 43}, {"ruleId": "51", "severity": 1, "message": "59", "line": 17, "column": 3, "nodeType": "53", "messageId": "54", "endLine": 17, "endColumn": 9}, {"ruleId": "51", "severity": 1, "message": "60", "line": 26, "column": 3, "nodeType": "53", "messageId": "54", "endLine": 26, "endColumn": 19}, {"ruleId": "51", "severity": 1, "message": "61", "line": 28, "column": 3, "nodeType": "53", "messageId": "54", "endLine": 28, "endColumn": 15}, {"ruleId": "51", "severity": 1, "message": "62", "line": 63, "column": 19, "nodeType": "53", "messageId": "54", "endLine": 63, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'Layout' is defined but never used.", "Identifier", "unusedVar", "'Option' is assigned a value but never used.", "'ContainerStats' is defined but never used.", "'selectedServer' is assigned a value but never used.", "'setSelectedServer' is assigned a value but never used.", "'Upload' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'CodeOutlined' is defined but never used.", "'setLoading' is assigned a value but never used."]