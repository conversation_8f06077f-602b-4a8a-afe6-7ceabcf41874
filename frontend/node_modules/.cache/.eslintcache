[{"/home/<USER>/itai/frontend/src/index.tsx": "1", "/home/<USER>/itai/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/itai/frontend/src/App.tsx": "3", "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx": "4", "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx": "5", "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx": "6", "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx": "7", "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx": "8", "/home/<USER>/itai/frontend/src/services/api.ts": "9"}, {"size": 554, "mtime": 1751425251890, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1751425251890, "results": "12", "hashOfConfig": "11"}, {"size": 359, "mtime": 1751430845699, "results": "13", "hashOfConfig": "11"}, {"size": 4511, "mtime": 1751438479900, "results": "14", "hashOfConfig": "11"}, {"size": 16211, "mtime": 1751437933384, "results": "15", "hashOfConfig": "11"}, {"size": 14048, "mtime": 1751438074547, "results": "16", "hashOfConfig": "11"}, {"size": 17117, "mtime": 1751438403794, "results": "17", "hashOfConfig": "11"}, {"size": 14673, "mtime": 1751438196836, "results": "18", "hashOfConfig": "11"}, {"size": 8455, "mtime": 1751438223863, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1plfo14", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/itai/frontend/src/index.tsx", [], [], "/home/<USER>/itai/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/itai/frontend/src/App.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx", ["47", "48"], [], "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx", ["49", "50"], [], "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx", ["51", "52", "53", "54", "55"], [], "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx", ["56", "57"], [], "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx", ["58", "59", "60", "61"], [], "/home/<USER>/itai/frontend/src/services/api.ts", [], [], {"ruleId": "62", "severity": 1, "message": "63", "line": 23, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 23, "endColumn": 21}, {"ruleId": "62", "severity": 1, "message": "66", "line": 30, "column": 8, "nodeType": "64", "messageId": "65", "endLine": 30, "endColumn": 16}, {"ruleId": "62", "severity": 1, "message": "67", "line": 18, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 18, "endColumn": 7}, {"ruleId": "62", "severity": 1, "message": "68", "line": 19, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 19, "endColumn": 8}, {"ruleId": "62", "severity": 1, "message": "69", "line": 9, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 9, "endColumn": 7}, {"ruleId": "62", "severity": 1, "message": "70", "line": 33, "column": 9, "nodeType": "64", "messageId": "65", "endLine": 33, "endColumn": 15}, {"ruleId": "62", "severity": 1, "message": "71", "line": 52, "column": 11, "nodeType": "64", "messageId": "65", "endLine": 52, "endColumn": 25}, {"ruleId": "72", "severity": 1, "message": "73", "line": 118, "column": 6, "nodeType": "74", "endLine": 118, "endColumn": 8, "suggestions": "75"}, {"ruleId": "72", "severity": 1, "message": "76", "line": 122, "column": 6, "nodeType": "74", "endLine": 122, "endColumn": 22, "suggestions": "77"}, {"ruleId": "62", "severity": 1, "message": "78", "line": 73, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 73, "endColumn": 17}, {"ruleId": "72", "severity": 1, "message": "79", "line": 190, "column": 6, "nodeType": "74", "endLine": 190, "endColumn": 22, "suggestions": "80"}, {"ruleId": "62", "severity": 1, "message": "81", "line": 17, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 17, "endColumn": 9}, {"ruleId": "62", "severity": 1, "message": "82", "line": 27, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 27, "endColumn": 19}, {"ruleId": "62", "severity": 1, "message": "83", "line": 29, "column": 3, "nodeType": "64", "messageId": "65", "endLine": 29, "endColumn": 15}, {"ruleId": "62", "severity": 1, "message": "84", "line": 64, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 64, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'ExperimentOutlined' is defined but never used.", "Identifier", "unusedVar", "'TestPage' is defined but never used.", "'Spin' is defined but never used.", "'Alert' is defined but never used.", "'Form' is defined but never used.", "'Option' is assigned a value but never used.", "'ContainerStats' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadServers'. Either include it or remove the dependency array.", "ArrayExpression", ["85"], "React Hook useEffect has a missing dependency: 'loadDockerData'. Either include it or remove the dependency array.", ["86"], "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadMonitoringData'. Either include it or remove the dependency array.", ["87"], "'Upload' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'CodeOutlined' is defined but never used.", "'servers' is assigned a value but never used.", {"desc": "88", "fix": "89"}, {"desc": "90", "fix": "91"}, {"desc": "92", "fix": "93"}, "Update the dependencies array to be: [loadServers]", {"range": "94", "text": "95"}, "Update the dependencies array to be: [loadDockerData, selectedServer]", {"range": "96", "text": "97"}, "Update the dependencies array to be: [loadMonitoringData, selectedServer]", {"range": "98", "text": "99"}, [2527, 2529], "[loadServers]", [2580, 2596], "[loadDockerData, selectedServer]", [4388, 4404], "[loadMonitoringData, selectedServer]"]