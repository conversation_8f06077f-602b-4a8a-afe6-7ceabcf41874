[{"/home/<USER>/itai/frontend/src/index.tsx": "1", "/home/<USER>/itai/frontend/src/reportWebVitals.ts": "2", "/home/<USER>/itai/frontend/src/App.tsx": "3", "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx": "4", "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx": "5", "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx": "6", "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx": "7", "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx": "8", "/home/<USER>/itai/frontend/src/services/api.ts": "9"}, {"size": 554, "mtime": 1751425251890, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1751425251890, "results": "12", "hashOfConfig": "11"}, {"size": 359, "mtime": 1751430845699, "results": "13", "hashOfConfig": "11"}, {"size": 4444, "mtime": 1751427643689, "results": "14", "hashOfConfig": "11"}, {"size": 12752, "mtime": 1751430971772, "results": "15", "hashOfConfig": "11"}, {"size": 12498, "mtime": 1751426667945, "results": "16", "hashOfConfig": "11"}, {"size": 12728, "mtime": 1751430500282, "results": "17", "hashOfConfig": "11"}, {"size": 15415, "mtime": 1751430516346, "results": "18", "hashOfConfig": "11"}, {"size": 6574, "mtime": 1751430789310, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1plfo14", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/itai/frontend/src/index.tsx", [], [], "/home/<USER>/itai/frontend/src/reportWebVitals.ts", [], [], "/home/<USER>/itai/frontend/src/App.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Layout/MainLayout.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx", ["47", "48"], [], "/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx", ["49", "50", "51", "52"], [], "/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx", [], [], "/home/<USER>/itai/frontend/src/components/Script/ScriptManagement.tsx", ["53", "54", "55", "56"], [], "/home/<USER>/itai/frontend/src/services/api.ts", [], [], {"ruleId": "57", "severity": 1, "message": "58", "line": 18, "column": 3, "nodeType": "59", "messageId": "60", "endLine": 18, "endColumn": 7}, {"ruleId": "57", "severity": 1, "message": "61", "line": 19, "column": 3, "nodeType": "59", "messageId": "60", "endLine": 19, "endColumn": 8}, {"ruleId": "57", "severity": 1, "message": "62", "line": 32, "column": 9, "nodeType": "59", "messageId": "60", "endLine": 32, "endColumn": 15}, {"ruleId": "57", "severity": 1, "message": "63", "line": 51, "column": 11, "nodeType": "59", "messageId": "60", "endLine": 51, "endColumn": 25}, {"ruleId": "57", "severity": 1, "message": "64", "line": 74, "column": 10, "nodeType": "59", "messageId": "60", "endLine": 74, "endColumn": 24}, {"ruleId": "57", "severity": 1, "message": "65", "line": 74, "column": 26, "nodeType": "59", "messageId": "60", "endLine": 74, "endColumn": 43}, {"ruleId": "57", "severity": 1, "message": "66", "line": 17, "column": 3, "nodeType": "59", "messageId": "60", "endLine": 17, "endColumn": 9}, {"ruleId": "57", "severity": 1, "message": "67", "line": 26, "column": 3, "nodeType": "59", "messageId": "60", "endLine": 26, "endColumn": 19}, {"ruleId": "57", "severity": 1, "message": "68", "line": 28, "column": 3, "nodeType": "59", "messageId": "60", "endLine": 28, "endColumn": 15}, {"ruleId": "57", "severity": 1, "message": "69", "line": 63, "column": 19, "nodeType": "59", "messageId": "60", "endLine": 63, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'Spin' is defined but never used.", "Identifier", "unusedVar", "'Alert' is defined but never used.", "'Option' is assigned a value but never used.", "'ContainerStats' is defined but never used.", "'selectedServer' is assigned a value but never used.", "'setSelectedServer' is assigned a value but never used.", "'Upload' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'CodeOutlined' is defined but never used.", "'setLoading' is assigned a value but never used."]