{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"data\", \"index\", \"className\", \"rowKey\", \"style\", \"extra\", \"getHeight\"];\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useRowInfo from \"../hooks/useRowInfo\";\nimport VirtualCell from \"./VirtualCell\";\nimport { StaticContext } from \"./context\";\nimport { computedExpandedClassName } from \"../utils/expandUtil\";\nvar BodyLine = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var data = props.data,\n    index = props.index,\n    className = props.className,\n    rowKey = props.rowKey,\n    style = props.style,\n    extra = props.extra,\n    getHeight = props.getHeight,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var record = data.record,\n    indent = data.indent,\n    renderIndex = data.index;\n  var _useContext = useContext(TableContext, ['prefixCls', 'flattenColumns', 'fixColumn', 'componentWidth', 'scrollX']),\n    scrollX = _useContext.scrollX,\n    flattenColumns = _useContext.flattenColumns,\n    prefixCls = _useContext.prefixCls,\n    fixColumn = _useContext.fixColumn,\n    componentWidth = _useContext.componentWidth;\n  var _useContext2 = useContext(StaticContext, ['getComponent']),\n    getComponent = _useContext2.getComponent;\n  var rowInfo = useRowInfo(record, rowKey, index, indent);\n  var RowComponent = getComponent(['body', 'row'], 'div');\n  var cellComponent = getComponent(['body', 'cell'], 'div');\n\n  // ========================== Expand ==========================\n  var rowSupportExpand = rowInfo.rowSupportExpand,\n    expanded = rowInfo.expanded,\n    rowProps = rowInfo.rowProps,\n    expandedRowRender = rowInfo.expandedRowRender,\n    expandedRowClassName = rowInfo.expandedRowClassName;\n  var expandRowNode;\n  if (rowSupportExpand && expanded) {\n    var expandContent = expandedRowRender(record, index, indent + 1, expanded);\n    var expandedClsName = computedExpandedClassName(expandedRowClassName, record, index, indent);\n    var additionalProps = {};\n    if (fixColumn) {\n      additionalProps = {\n        style: _defineProperty({}, '--virtual-width', \"\".concat(componentWidth, \"px\"))\n      };\n    }\n    var rowCellCls = \"\".concat(prefixCls, \"-expanded-row-cell\");\n    expandRowNode = /*#__PURE__*/React.createElement(RowComponent, {\n      className: classNames(\"\".concat(prefixCls, \"-expanded-row\"), \"\".concat(prefixCls, \"-expanded-row-level-\").concat(indent + 1), expandedClsName)\n    }, /*#__PURE__*/React.createElement(Cell, {\n      component: cellComponent,\n      prefixCls: prefixCls,\n      className: classNames(rowCellCls, _defineProperty({}, \"\".concat(rowCellCls, \"-fixed\"), fixColumn)),\n      additionalProps: additionalProps\n    }, expandContent));\n  }\n\n  // ========================== Render ==========================\n  var rowStyle = _objectSpread(_objectSpread({}, style), {}, {\n    width: scrollX\n  });\n  if (extra) {\n    rowStyle.position = 'absolute';\n    rowStyle.pointerEvents = 'none';\n  }\n  var rowNode = /*#__PURE__*/React.createElement(RowComponent, _extends({}, rowProps, restProps, {\n    \"data-row-key\": rowKey,\n    ref: rowSupportExpand ? null : ref,\n    className: classNames(className, \"\".concat(prefixCls, \"-row\"), rowProps === null || rowProps === void 0 ? void 0 : rowProps.className, _defineProperty({}, \"\".concat(prefixCls, \"-row-extra\"), extra)),\n    style: _objectSpread(_objectSpread({}, rowStyle), rowProps === null || rowProps === void 0 ? void 0 : rowProps.style)\n  }), flattenColumns.map(function (column, colIndex) {\n    return /*#__PURE__*/React.createElement(VirtualCell, {\n      key: colIndex,\n      component: cellComponent,\n      rowInfo: rowInfo,\n      column: column,\n      colIndex: colIndex,\n      indent: indent,\n      index: index,\n      renderIndex: renderIndex,\n      record: record,\n      inverse: extra,\n      getHeight: getHeight\n    });\n  }));\n  if (rowSupportExpand) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: ref\n    }, rowNode, expandRowNode);\n  }\n  return rowNode;\n});\nvar ResponseBodyLine = responseImmutable(BodyLine);\nif (process.env.NODE_ENV !== 'production') {\n  ResponseBodyLine.displayName = 'BodyLine';\n}\nexport default ResponseBodyLine;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_objectWithoutProperties", "_excluded", "useContext", "classNames", "React", "Cell", "TableContext", "responseImmutable", "useRowInfo", "VirtualCell", "StaticContext", "computedExpandedClassName", "BodyLine", "forwardRef", "props", "ref", "data", "index", "className", "<PERSON><PERSON><PERSON>", "style", "extra", "getHeight", "restProps", "record", "indent", "renderIndex", "_useContext", "scrollX", "flattenColumns", "prefixCls", "fixColumn", "componentWidth", "_useContext2", "getComponent", "rowInfo", "RowComponent", "cellComponent", "rowSupportExpand", "expanded", "rowProps", "expandedRowRender", "expandedRowClassName", "expandRowNode", "expandContent", "expandedClsName", "additionalProps", "concat", "rowCellCls", "createElement", "component", "rowStyle", "width", "position", "pointerEvents", "rowNode", "map", "column", "colIndex", "key", "inverse", "ResponseBodyLine", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-table/es/VirtualTable/BodyLine.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"data\", \"index\", \"className\", \"rowKey\", \"style\", \"extra\", \"getHeight\"];\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useRowInfo from \"../hooks/useRowInfo\";\nimport VirtualCell from \"./VirtualCell\";\nimport { StaticContext } from \"./context\";\nimport { computedExpandedClassName } from \"../utils/expandUtil\";\nvar BodyLine = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var data = props.data,\n    index = props.index,\n    className = props.className,\n    rowKey = props.rowKey,\n    style = props.style,\n    extra = props.extra,\n    getHeight = props.getHeight,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var record = data.record,\n    indent = data.indent,\n    renderIndex = data.index;\n  var _useContext = useContext(TableContext, ['prefixCls', 'flattenColumns', 'fixColumn', 'componentWidth', 'scrollX']),\n    scrollX = _useContext.scrollX,\n    flattenColumns = _useContext.flattenColumns,\n    prefixCls = _useContext.prefixCls,\n    fixColumn = _useContext.fixColumn,\n    componentWidth = _useContext.componentWidth;\n  var _useContext2 = useContext(StaticContext, ['getComponent']),\n    getComponent = _useContext2.getComponent;\n  var rowInfo = useRowInfo(record, rowKey, index, indent);\n  var RowComponent = getComponent(['body', 'row'], 'div');\n  var cellComponent = getComponent(['body', 'cell'], 'div');\n\n  // ========================== Expand ==========================\n  var rowSupportExpand = rowInfo.rowSupportExpand,\n    expanded = rowInfo.expanded,\n    rowProps = rowInfo.rowProps,\n    expandedRowRender = rowInfo.expandedRowRender,\n    expandedRowClassName = rowInfo.expandedRowClassName;\n  var expandRowNode;\n  if (rowSupportExpand && expanded) {\n    var expandContent = expandedRowRender(record, index, indent + 1, expanded);\n    var expandedClsName = computedExpandedClassName(expandedRowClassName, record, index, indent);\n    var additionalProps = {};\n    if (fixColumn) {\n      additionalProps = {\n        style: _defineProperty({}, '--virtual-width', \"\".concat(componentWidth, \"px\"))\n      };\n    }\n    var rowCellCls = \"\".concat(prefixCls, \"-expanded-row-cell\");\n    expandRowNode = /*#__PURE__*/React.createElement(RowComponent, {\n      className: classNames(\"\".concat(prefixCls, \"-expanded-row\"), \"\".concat(prefixCls, \"-expanded-row-level-\").concat(indent + 1), expandedClsName)\n    }, /*#__PURE__*/React.createElement(Cell, {\n      component: cellComponent,\n      prefixCls: prefixCls,\n      className: classNames(rowCellCls, _defineProperty({}, \"\".concat(rowCellCls, \"-fixed\"), fixColumn)),\n      additionalProps: additionalProps\n    }, expandContent));\n  }\n\n  // ========================== Render ==========================\n  var rowStyle = _objectSpread(_objectSpread({}, style), {}, {\n    width: scrollX\n  });\n  if (extra) {\n    rowStyle.position = 'absolute';\n    rowStyle.pointerEvents = 'none';\n  }\n  var rowNode = /*#__PURE__*/React.createElement(RowComponent, _extends({}, rowProps, restProps, {\n    \"data-row-key\": rowKey,\n    ref: rowSupportExpand ? null : ref,\n    className: classNames(className, \"\".concat(prefixCls, \"-row\"), rowProps === null || rowProps === void 0 ? void 0 : rowProps.className, _defineProperty({}, \"\".concat(prefixCls, \"-row-extra\"), extra)),\n    style: _objectSpread(_objectSpread({}, rowStyle), rowProps === null || rowProps === void 0 ? void 0 : rowProps.style)\n  }), flattenColumns.map(function (column, colIndex) {\n    return /*#__PURE__*/React.createElement(VirtualCell, {\n      key: colIndex,\n      component: cellComponent,\n      rowInfo: rowInfo,\n      column: column,\n      colIndex: colIndex,\n      indent: indent,\n      index: index,\n      renderIndex: renderIndex,\n      record: record,\n      inverse: extra,\n      getHeight: getHeight\n    });\n  }));\n  if (rowSupportExpand) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: ref\n    }, rowNode, expandRowNode);\n  }\n  return rowNode;\n});\nvar ResponseBodyLine = responseImmutable(BodyLine);\nif (process.env.NODE_ENV !== 'production') {\n  ResponseBodyLine.displayName = 'BodyLine';\n}\nexport default ResponseBodyLine;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,CAAC;AACvF,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,IAAIC,iBAAiB,QAAQ,yBAAyB;AACzE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,QAAQ,WAAW;AACzC,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,IAAIC,QAAQ,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACjE,IAAIC,IAAI,GAAGF,KAAK,CAACE,IAAI;IACnBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,SAAS,GAAGvB,wBAAwB,CAACc,KAAK,EAAEb,SAAS,CAAC;EACxD,IAAIuB,MAAM,GAAGR,IAAI,CAACQ,MAAM;IACtBC,MAAM,GAAGT,IAAI,CAACS,MAAM;IACpBC,WAAW,GAAGV,IAAI,CAACC,KAAK;EAC1B,IAAIU,WAAW,GAAGzB,UAAU,CAACI,YAAY,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC;IACnHsB,OAAO,GAAGD,WAAW,CAACC,OAAO;IAC7BC,cAAc,GAAGF,WAAW,CAACE,cAAc;IAC3CC,SAAS,GAAGH,WAAW,CAACG,SAAS;IACjCC,SAAS,GAAGJ,WAAW,CAACI,SAAS;IACjCC,cAAc,GAAGL,WAAW,CAACK,cAAc;EAC7C,IAAIC,YAAY,GAAG/B,UAAU,CAACQ,aAAa,EAAE,CAAC,cAAc,CAAC,CAAC;IAC5DwB,YAAY,GAAGD,YAAY,CAACC,YAAY;EAC1C,IAAIC,OAAO,GAAG3B,UAAU,CAACgB,MAAM,EAAEL,MAAM,EAAEF,KAAK,EAAEQ,MAAM,CAAC;EACvD,IAAIW,YAAY,GAAGF,YAAY,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;EACvD,IAAIG,aAAa,GAAGH,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC;;EAEzD;EACA,IAAII,gBAAgB,GAAGH,OAAO,CAACG,gBAAgB;IAC7CC,QAAQ,GAAGJ,OAAO,CAACI,QAAQ;IAC3BC,QAAQ,GAAGL,OAAO,CAACK,QAAQ;IAC3BC,iBAAiB,GAAGN,OAAO,CAACM,iBAAiB;IAC7CC,oBAAoB,GAAGP,OAAO,CAACO,oBAAoB;EACrD,IAAIC,aAAa;EACjB,IAAIL,gBAAgB,IAAIC,QAAQ,EAAE;IAChC,IAAIK,aAAa,GAAGH,iBAAiB,CAACjB,MAAM,EAAEP,KAAK,EAAEQ,MAAM,GAAG,CAAC,EAAEc,QAAQ,CAAC;IAC1E,IAAIM,eAAe,GAAGlC,yBAAyB,CAAC+B,oBAAoB,EAAElB,MAAM,EAAEP,KAAK,EAAEQ,MAAM,CAAC;IAC5F,IAAIqB,eAAe,GAAG,CAAC,CAAC;IACxB,IAAIf,SAAS,EAAE;MACbe,eAAe,GAAG;QAChB1B,KAAK,EAAErB,eAAe,CAAC,CAAC,CAAC,EAAE,iBAAiB,EAAE,EAAE,CAACgD,MAAM,CAACf,cAAc,EAAE,IAAI,CAAC;MAC/E,CAAC;IACH;IACA,IAAIgB,UAAU,GAAG,EAAE,CAACD,MAAM,CAACjB,SAAS,EAAE,oBAAoB,CAAC;IAC3Da,aAAa,GAAG,aAAavC,KAAK,CAAC6C,aAAa,CAACb,YAAY,EAAE;MAC7DlB,SAAS,EAAEf,UAAU,CAAC,EAAE,CAAC4C,MAAM,CAACjB,SAAS,EAAE,eAAe,CAAC,EAAE,EAAE,CAACiB,MAAM,CAACjB,SAAS,EAAE,sBAAsB,CAAC,CAACiB,MAAM,CAACtB,MAAM,GAAG,CAAC,CAAC,EAAEoB,eAAe;IAC/I,CAAC,EAAE,aAAazC,KAAK,CAAC6C,aAAa,CAAC5C,IAAI,EAAE;MACxC6C,SAAS,EAAEb,aAAa;MACxBP,SAAS,EAAEA,SAAS;MACpBZ,SAAS,EAAEf,UAAU,CAAC6C,UAAU,EAAEjD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACgD,MAAM,CAACC,UAAU,EAAE,QAAQ,CAAC,EAAEjB,SAAS,CAAC,CAAC;MAClGe,eAAe,EAAEA;IACnB,CAAC,EAAEF,aAAa,CAAC,CAAC;EACpB;;EAEA;EACA,IAAIO,QAAQ,GAAGrD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IACzDgC,KAAK,EAAExB;EACT,CAAC,CAAC;EACF,IAAIP,KAAK,EAAE;IACT8B,QAAQ,CAACE,QAAQ,GAAG,UAAU;IAC9BF,QAAQ,CAACG,aAAa,GAAG,MAAM;EACjC;EACA,IAAIC,OAAO,GAAG,aAAanD,KAAK,CAAC6C,aAAa,CAACb,YAAY,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAE2C,QAAQ,EAAEjB,SAAS,EAAE;IAC7F,cAAc,EAAEJ,MAAM;IACtBJ,GAAG,EAAEuB,gBAAgB,GAAG,IAAI,GAAGvB,GAAG;IAClCG,SAAS,EAAEf,UAAU,CAACe,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAACjB,SAAS,EAAE,MAAM,CAAC,EAAEU,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACtB,SAAS,EAAEnB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACgD,MAAM,CAACjB,SAAS,EAAE,YAAY,CAAC,EAAET,KAAK,CAAC,CAAC;IACtMD,KAAK,EAAEtB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqD,QAAQ,CAAC,EAAEX,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACpB,KAAK;EACtH,CAAC,CAAC,EAAES,cAAc,CAAC2B,GAAG,CAAC,UAAUC,MAAM,EAAEC,QAAQ,EAAE;IACjD,OAAO,aAAatD,KAAK,CAAC6C,aAAa,CAACxC,WAAW,EAAE;MACnDkD,GAAG,EAAED,QAAQ;MACbR,SAAS,EAAEb,aAAa;MACxBF,OAAO,EAAEA,OAAO;MAChBsB,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBjC,MAAM,EAAEA,MAAM;MACdR,KAAK,EAAEA,KAAK;MACZS,WAAW,EAAEA,WAAW;MACxBF,MAAM,EAAEA,MAAM;MACdoC,OAAO,EAAEvC,KAAK;MACdC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;EACH,IAAIgB,gBAAgB,EAAE;IACpB,OAAO,aAAalC,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;MAC7ClC,GAAG,EAAEA;IACP,CAAC,EAAEwC,OAAO,EAAEZ,aAAa,CAAC;EAC5B;EACA,OAAOY,OAAO;AAChB,CAAC,CAAC;AACF,IAAIM,gBAAgB,GAAGtD,iBAAiB,CAACK,QAAQ,CAAC;AAClD,IAAIkD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,gBAAgB,CAACI,WAAW,GAAG,UAAU;AAC3C;AACA,eAAeJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}