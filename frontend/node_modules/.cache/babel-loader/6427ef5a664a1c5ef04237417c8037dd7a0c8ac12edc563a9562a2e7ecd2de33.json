{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst debounce$1 = require('../../function/debounce.js');\nfunction debounce(func, debounceMs = 0, options = {}) {\n  if (typeof options !== 'object') {\n    options = {};\n  }\n  const {\n    leading = false,\n    trailing = true,\n    maxWait\n  } = options;\n  const edges = Array(2);\n  if (leading) {\n    edges[0] = 'leading';\n  }\n  if (trailing) {\n    edges[1] = 'trailing';\n  }\n  let result = undefined;\n  let pendingAt = null;\n  const _debounced = debounce$1.debounce(function (...args) {\n    result = func.apply(this, args);\n    pendingAt = null;\n  }, debounceMs, {\n    edges\n  });\n  const debounced = function (...args) {\n    if (maxWait != null) {\n      if (pendingAt === null) {\n        pendingAt = Date.now();\n      }\n      if (Date.now() - pendingAt >= maxWait) {\n        result = func.apply(this, args);\n        pendingAt = Date.now();\n        _debounced.cancel();\n        _debounced.schedule();\n        return result;\n      }\n    }\n    _debounced.apply(this, args);\n    return result;\n  };\n  const flush = () => {\n    _debounced.flush();\n    return result;\n  };\n  debounced.cancel = _debounced.cancel;\n  debounced.flush = flush;\n  return debounced;\n}\nexports.debounce = debounce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce$1", "require", "debounce", "func", "debounceMs", "options", "leading", "trailing", "max<PERSON><PERSON>", "edges", "Array", "result", "undefined", "pendingAt", "_debounced", "args", "apply", "debounced", "Date", "now", "cancel", "schedule", "flush"], "sources": ["/home/<USER>/itai/frontend/node_modules/es-toolkit/dist/compat/function/debounce.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst debounce$1 = require('../../function/debounce.js');\n\nfunction debounce(func, debounceMs = 0, options = {}) {\n    if (typeof options !== 'object') {\n        options = {};\n    }\n    const { leading = false, trailing = true, maxWait } = options;\n    const edges = Array(2);\n    if (leading) {\n        edges[0] = 'leading';\n    }\n    if (trailing) {\n        edges[1] = 'trailing';\n    }\n    let result = undefined;\n    let pendingAt = null;\n    const _debounced = debounce$1.debounce(function (...args) {\n        result = func.apply(this, args);\n        pendingAt = null;\n    }, debounceMs, { edges });\n    const debounced = function (...args) {\n        if (maxWait != null) {\n            if (pendingAt === null) {\n                pendingAt = Date.now();\n            }\n            if (Date.now() - pendingAt >= maxWait) {\n                result = func.apply(this, args);\n                pendingAt = Date.now();\n                _debounced.cancel();\n                _debounced.schedule();\n                return result;\n            }\n        }\n        _debounced.apply(this, args);\n        return result;\n    };\n    const flush = () => {\n        _debounced.flush();\n        return result;\n    };\n    debounced.cancel = _debounced.cancel;\n    debounced.flush = flush;\n    return debounced;\n}\n\nexports.debounce = debounce;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,UAAU,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAExD,SAASC,QAAQA,CAACC,IAAI,EAAEC,UAAU,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAClD,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC7BA,OAAO,GAAG,CAAC,CAAC;EAChB;EACA,MAAM;IAAEC,OAAO,GAAG,KAAK;IAAEC,QAAQ,GAAG,IAAI;IAAEC;EAAQ,CAAC,GAAGH,OAAO;EAC7D,MAAMI,KAAK,GAAGC,KAAK,CAAC,CAAC,CAAC;EACtB,IAAIJ,OAAO,EAAE;IACTG,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS;EACxB;EACA,IAAIF,QAAQ,EAAE;IACVE,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU;EACzB;EACA,IAAIE,MAAM,GAAGC,SAAS;EACtB,IAAIC,SAAS,GAAG,IAAI;EACpB,MAAMC,UAAU,GAAGd,UAAU,CAACE,QAAQ,CAAC,UAAU,GAAGa,IAAI,EAAE;IACtDJ,MAAM,GAAGR,IAAI,CAACa,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;IAC/BF,SAAS,GAAG,IAAI;EACpB,CAAC,EAAET,UAAU,EAAE;IAAEK;EAAM,CAAC,CAAC;EACzB,MAAMQ,SAAS,GAAG,SAAAA,CAAU,GAAGF,IAAI,EAAE;IACjC,IAAIP,OAAO,IAAI,IAAI,EAAE;MACjB,IAAIK,SAAS,KAAK,IAAI,EAAE;QACpBA,SAAS,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B;MACA,IAAID,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGN,SAAS,IAAIL,OAAO,EAAE;QACnCG,MAAM,GAAGR,IAAI,CAACa,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;QAC/BF,SAAS,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC;QACtBL,UAAU,CAACM,MAAM,CAAC,CAAC;QACnBN,UAAU,CAACO,QAAQ,CAAC,CAAC;QACrB,OAAOV,MAAM;MACjB;IACJ;IACAG,UAAU,CAACE,KAAK,CAAC,IAAI,EAAED,IAAI,CAAC;IAC5B,OAAOJ,MAAM;EACjB,CAAC;EACD,MAAMW,KAAK,GAAGA,CAAA,KAAM;IAChBR,UAAU,CAACQ,KAAK,CAAC,CAAC;IAClB,OAAOX,MAAM;EACjB,CAAC;EACDM,SAAS,CAACG,MAAM,GAAGN,UAAU,CAACM,MAAM;EACpCH,SAAS,CAACK,KAAK,GAAGA,KAAK;EACvB,OAAOL,SAAS;AACpB;AAEArB,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}