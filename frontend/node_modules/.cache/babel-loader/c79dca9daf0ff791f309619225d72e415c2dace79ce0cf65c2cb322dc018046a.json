{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// import canUseDom from 'rc-util/lib/Dom/canUseDom';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\n\n// We need fully clone React function here\n// to avoid webpack warning React 17 do not export `useId`\nvar fullClone = _objectSpread({}, React);\nvar useInsertionEffect = fullClone.useInsertionEffect;\n/**\n * Polyfill `useInsertionEffect` for React < 18\n * @param renderEffect will be executed in `useMemo`, and do not have callback\n * @param effect will be executed in `useLayoutEffect`\n * @param deps\n */\nvar useInsertionEffectPolyfill = function useInsertionEffectPolyfill(renderEffect, effect, deps) {\n  React.useMemo(renderEffect, deps);\n  useLayoutEffect(function () {\n    return effect(true);\n  }, deps);\n};\n\n/**\n * Compatible `useInsertionEffect`\n * will use `useInsertionEffect` if React version >= 18,\n * otherwise use `useInsertionEffectPolyfill`.\n */\nvar useCompatibleInsertionEffect = useInsertionEffect ? function (renderEffect, effect, deps) {\n  return useInsertionEffect(function () {\n    renderEffect();\n    return effect();\n  }, deps);\n} : useInsertionEffectPolyfill;\nexport default useCompatibleInsertionEffect;", "map": {"version": 3, "names": ["_objectSpread", "useLayoutEffect", "React", "fullClone", "useInsertionEffect", "useInsertionEffectPolyfill", "renderEffect", "effect", "deps", "useMemo", "useCompatibleInsertionEffect"], "sources": ["/home/<USER>/itai/frontend/node_modules/@ant-design/cssinjs/es/hooks/useCompatibleInsertionEffect.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// import canUseDom from 'rc-util/lib/Dom/canUseDom';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\n\n// We need fully clone React function here\n// to avoid webpack warning React 17 do not export `useId`\nvar fullClone = _objectSpread({}, React);\nvar useInsertionEffect = fullClone.useInsertionEffect;\n/**\n * Polyfill `useInsertionEffect` for React < 18\n * @param renderEffect will be executed in `useMemo`, and do not have callback\n * @param effect will be executed in `useLayoutEffect`\n * @param deps\n */\nvar useInsertionEffectPolyfill = function useInsertionEffectPolyfill(renderEffect, effect, deps) {\n  React.useMemo(renderEffect, deps);\n  useLayoutEffect(function () {\n    return effect(true);\n  }, deps);\n};\n\n/**\n * Compatible `useInsertionEffect`\n * will use `useInsertionEffect` if React version >= 18,\n * otherwise use `useInsertionEffectPolyfill`.\n */\nvar useCompatibleInsertionEffect = useInsertionEffect ? function (renderEffect, effect, deps) {\n  return useInsertionEffect(function () {\n    renderEffect();\n    return effect();\n  }, deps);\n} : useInsertionEffectPolyfill;\nexport default useCompatibleInsertionEffect;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE;AACA,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA,IAAIC,SAAS,GAAGH,aAAa,CAAC,CAAC,CAAC,EAAEE,KAAK,CAAC;AACxC,IAAIE,kBAAkB,GAAGD,SAAS,CAACC,kBAAkB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,0BAA0B,GAAG,SAASA,0BAA0BA,CAACC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAC/FN,KAAK,CAACO,OAAO,CAACH,YAAY,EAAEE,IAAI,CAAC;EACjCP,eAAe,CAAC,YAAY;IAC1B,OAAOM,MAAM,CAAC,IAAI,CAAC;EACrB,CAAC,EAAEC,IAAI,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAIE,4BAA4B,GAAGN,kBAAkB,GAAG,UAAUE,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAE;EAC5F,OAAOJ,kBAAkB,CAAC,YAAY;IACpCE,YAAY,CAAC,CAAC;IACd,OAAOC,MAAM,CAAC,CAAC;EACjB,CAAC,EAAEC,IAAI,CAAC;AACV,CAAC,GAAGH,0BAA0B;AAC9B,eAAeK,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}