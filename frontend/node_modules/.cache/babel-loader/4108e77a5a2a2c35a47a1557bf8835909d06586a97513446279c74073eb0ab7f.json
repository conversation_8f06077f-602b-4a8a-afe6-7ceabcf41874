{"ast": null, "code": "var _excluded = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport get from 'es-toolkit/compat/get';\nimport { clsx } from 'clsx';\nimport { selectPieLegend, selectPieSectors } from '../state/selectors/pieSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { SetPolarGraphicalItem } from '../state/SetGraphicalItem';\nimport { Layer } from '../container/Layer';\nimport { Curve } from '../shape/Curve';\nimport { Text } from '../component/Text';\nimport { Cell } from '../component/Cell';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getMaxRadius, polarToCartesian } from '../util/PolarUtils';\nimport { getPercentValue, interpolateNumber, isNumber, mathSign, uniqueId } from '../util/DataUtils';\nimport { getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { Shape } from '../util/ActiveShapeUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetPolarLegendPayload } from '../state/SetLegendPayload';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\nfunction SetPiePayloadLegend(props) {\n  var presentationProps = useMemo(() => filterProps(props, false), [props]);\n  var cells = useMemo(() => findAllByType(props.children, Cell), [props.children]);\n  var pieSettings = useMemo(() => ({\n    name: props.name,\n    nameKey: props.nameKey,\n    tooltipType: props.tooltipType,\n    data: props.data,\n    dataKey: props.dataKey,\n    cx: props.cx,\n    cy: props.cy,\n    startAngle: props.startAngle,\n    endAngle: props.endAngle,\n    minAngle: props.minAngle,\n    paddingAngle: props.paddingAngle,\n    innerRadius: props.innerRadius,\n    outerRadius: props.outerRadius,\n    cornerRadius: props.cornerRadius,\n    legendType: props.legendType,\n    fill: props.fill,\n    presentationProps\n  }), [props.cornerRadius, props.cx, props.cy, props.data, props.dataKey, props.endAngle, props.innerRadius, props.minAngle, props.name, props.nameKey, props.outerRadius, props.paddingAngle, props.startAngle, props.tooltipType, props.legendType, props.fill, presentationProps]);\n  var legendPayload = useAppSelector(state => selectPieLegend(state, pieSettings, cells));\n  return /*#__PURE__*/React.createElement(SetPolarLegendPayload, {\n    legendPayload: legendPayload\n  });\n}\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    nameKey,\n    sectors,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType\n  } = props;\n  return {\n    dataDefinedOnItem: sectors === null || sectors === void 0 ? void 0 : sectors.map(p => p.tooltipPayload),\n    positions: sectors === null || sectors === void 0 ? void 0 : sectors.map(p => p.tooltipPosition),\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // why doesn't Pie support unit?\n    }\n  };\n}\nvar getTextAnchor = (x, cx) => {\n  if (x > cx) {\n    return 'start';\n  }\n  if (x < cx) {\n    return 'end';\n  }\n  return 'middle';\n};\nvar getOuterRadius = (dataPoint, outerRadius, maxPieRadius) => {\n  if (typeof outerRadius === 'function') {\n    return outerRadius(dataPoint);\n  }\n  return getPercentValue(outerRadius, maxPieRadius, maxPieRadius * 0.8);\n};\nvar parseCoordinateOfPie = (item, offset, dataPoint) => {\n  var {\n    top,\n    left,\n    width,\n    height\n  } = offset;\n  var maxPieRadius = getMaxRadius(width, height);\n  var cx = left + getPercentValue(item.cx, width, width / 2);\n  var cy = top + getPercentValue(item.cy, height, height / 2);\n  var innerRadius = getPercentValue(item.innerRadius, maxPieRadius, 0);\n  var outerRadius = getOuterRadius(dataPoint, item.outerRadius, maxPieRadius);\n  var maxRadius = item.maxRadius || Math.sqrt(width * width + height * height) / 2;\n  return {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    maxRadius\n  };\n};\nvar parseDeltaAngle = (startAngle, endAngle) => {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderLabelLineItem = (option, props) => {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  if (typeof option === 'function') {\n    return option(props);\n  }\n  var className = clsx('recharts-pie-label-line', typeof option !== 'boolean' ? option.className : '');\n  return /*#__PURE__*/React.createElement(Curve, _extends({}, props, {\n    type: \"linear\",\n    className: className\n  }));\n};\nvar renderLabelItem = (option, props, value) => {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  var label = value;\n  if (typeof option === 'function') {\n    label = option(props);\n    if (/*#__PURE__*/React.isValidElement(label)) {\n      return label;\n    }\n  }\n  var className = clsx('recharts-pie-label-text', typeof option !== 'boolean' && typeof option !== 'function' ? option.className : '');\n  return /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n    alignmentBaseline: \"middle\",\n    className: className\n  }), label);\n};\nfunction PieLabels(_ref) {\n  var {\n    sectors,\n    props,\n    showLabels\n  } = _ref;\n  var {\n    label,\n    labelLine,\n    dataKey\n  } = props;\n  if (!showLabels || !label || !sectors) {\n    return null;\n  }\n  var pieProps = filterProps(props, false);\n  var customLabelProps = filterProps(label, false);\n  var customLabelLineProps = filterProps(labelLine, false);\n  var offsetRadius = typeof label === 'object' && 'offsetRadius' in label && label.offsetRadius || 20;\n  var labels = sectors.map((entry, i) => {\n    var midAngle = (entry.startAngle + entry.endAngle) / 2;\n    var endPoint = polarToCartesian(entry.cx, entry.cy, entry.outerRadius + offsetRadius, midAngle);\n    var labelProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n      stroke: 'none'\n    }, customLabelProps), {}, {\n      index: i,\n      textAnchor: getTextAnchor(endPoint.x, entry.cx)\n    }, endPoint);\n    var lineProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n      fill: 'none',\n      stroke: entry.fill\n    }, customLabelLineProps), {}, {\n      index: i,\n      points: [polarToCartesian(entry.cx, entry.cy, entry.outerRadius, midAngle), endPoint],\n      key: 'line'\n    });\n    return (/*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(Layer, {\n        key: \"label-\".concat(entry.startAngle, \"-\").concat(entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n      }, labelLine && renderLabelLineItem(labelLine, lineProps), renderLabelItem(label, labelProps, getValueByDataKey(entry, dataKey)))\n    );\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-pie-labels\"\n  }, labels);\n}\nfunction PieSectors(props) {\n  var {\n    sectors,\n    activeShape,\n    inactiveShape: inactiveShapeProp,\n    allOtherPieProps,\n    showLabels\n  } = props;\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = allOtherPieProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherPieProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherPieProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherPieProps.dataKey);\n  if (sectors == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, sectors.map((entry, i) => {\n    if ((entry === null || entry === void 0 ? void 0 : entry.startAngle) === 0 && (entry === null || entry === void 0 ? void 0 : entry.endAngle) === 0 && sectors.length !== 1) return null;\n    var isSectorActive = activeShape && String(i) === activeIndex;\n    var inactiveShape = activeIndex ? inactiveShapeProp : null;\n    var sectorOptions = isSectorActive ? activeShape : inactiveShape;\n    var sectorProps = _objectSpread(_objectSpread({}, entry), {}, {\n      stroke: entry.stroke,\n      tabIndex: -1,\n      [DATA_ITEM_INDEX_ATTRIBUTE_NAME]: i,\n      [DATA_ITEM_DATAKEY_ATTRIBUTE_NAME]: allOtherPieProps.dataKey\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      tabIndex: -1,\n      className: \"recharts-pie-sector\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error the types need a bit of attention\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n\n      onClick: onClickFromContext(entry, i)\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n\n      key: \"sector-\".concat(entry === null || entry === void 0 ? void 0 : entry.startAngle, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(Shape, _extends({\n      option: sectorOptions,\n      isActive: isSectorActive,\n      shapeType: \"sector\"\n    }, sectorProps)));\n  }), /*#__PURE__*/React.createElement(PieLabels, {\n    sectors: sectors,\n    props: allOtherPieProps,\n    showLabels: showLabels\n  }));\n}\nexport function computePieSectors(_ref2) {\n  var _pieSettings$paddingA;\n  var {\n    pieSettings,\n    displayedData,\n    cells,\n    offset\n  } = _ref2;\n  var {\n    cornerRadius,\n    startAngle,\n    endAngle,\n    dataKey,\n    nameKey,\n    tooltipType\n  } = pieSettings;\n  var minAngle = Math.abs(pieSettings.minAngle);\n  var deltaAngle = parseDeltaAngle(startAngle, endAngle);\n  var absDeltaAngle = Math.abs(deltaAngle);\n  var paddingAngle = displayedData.length <= 1 ? 0 : (_pieSettings$paddingA = pieSettings.paddingAngle) !== null && _pieSettings$paddingA !== void 0 ? _pieSettings$paddingA : 0;\n  var notZeroItemCount = displayedData.filter(entry => getValueByDataKey(entry, dataKey, 0) !== 0).length;\n  var totalPaddingAngle = (absDeltaAngle >= 360 ? notZeroItemCount : notZeroItemCount - 1) * paddingAngle;\n  var realTotalAngle = absDeltaAngle - notZeroItemCount * minAngle - totalPaddingAngle;\n  var sum = displayedData.reduce((result, entry) => {\n    var val = getValueByDataKey(entry, dataKey, 0);\n    return result + (isNumber(val) ? val : 0);\n  }, 0);\n  var sectors;\n  if (sum > 0) {\n    var prev;\n    sectors = displayedData.map((entry, i) => {\n      var val = getValueByDataKey(entry, dataKey, 0);\n      var name = getValueByDataKey(entry, nameKey, i);\n      var coordinate = parseCoordinateOfPie(pieSettings, offset, entry);\n      var percent = (isNumber(val) ? val : 0) / sum;\n      var tempStartAngle;\n      var entryWithCellInfo = _objectSpread(_objectSpread({}, entry), cells && cells[i] && cells[i].props);\n      if (i) {\n        tempStartAngle = prev.endAngle + mathSign(deltaAngle) * paddingAngle * (val !== 0 ? 1 : 0);\n      } else {\n        tempStartAngle = startAngle;\n      }\n      var tempEndAngle = tempStartAngle + mathSign(deltaAngle) * ((val !== 0 ? minAngle : 0) + percent * realTotalAngle);\n      var midAngle = (tempStartAngle + tempEndAngle) / 2;\n      var middleRadius = (coordinate.innerRadius + coordinate.outerRadius) / 2;\n      var tooltipPayload = [{\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        name,\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        value: val,\n        payload: entryWithCellInfo,\n        dataKey,\n        type: tooltipType\n      }];\n      var tooltipPosition = polarToCartesian(coordinate.cx, coordinate.cy, middleRadius, midAngle);\n      prev = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieSettings.presentationProps), {}, {\n        percent,\n        cornerRadius,\n        name,\n        tooltipPayload,\n        midAngle,\n        middleRadius,\n        tooltipPosition\n      }, entryWithCellInfo), coordinate), {}, {\n        value: getValueByDataKey(entry, dataKey),\n        startAngle: tempStartAngle,\n        endAngle: tempEndAngle,\n        payload: entryWithCellInfo,\n        paddingAngle: mathSign(deltaAngle) * paddingAngle\n      });\n      return prev;\n    });\n  }\n  return sectors;\n}\nfunction SectorsWithAnimation(_ref3) {\n  var {\n    props,\n    previousSectorsRef\n  } = _ref3;\n  var {\n    sectors,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    activeShape,\n    inactiveShape,\n    onAnimationStart,\n    onAnimationEnd\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-pie-');\n  var prevSectors = previousSectorsRef.current;\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationStart: handleAnimationStart,\n    onAnimationEnd: handleAnimationEnd,\n    key: animationId\n  }, _ref4 => {\n    var {\n      t\n    } = _ref4;\n    var stepData = [];\n    var first = sectors && sectors[0];\n    var curAngle = first.startAngle;\n    sectors.forEach((entry, index) => {\n      var prev = prevSectors && prevSectors[index];\n      var paddingAngle = index > 0 ? get(entry, 'paddingAngle', 0) : 0;\n      if (prev) {\n        var angleIp = interpolateNumber(prev.endAngle - prev.startAngle, entry.endAngle - entry.startAngle);\n        var latest = _objectSpread(_objectSpread({}, entry), {}, {\n          startAngle: curAngle + paddingAngle,\n          endAngle: curAngle + angleIp(t) + paddingAngle\n        });\n        stepData.push(latest);\n        curAngle = latest.endAngle;\n      } else {\n        var {\n          endAngle,\n          startAngle\n        } = entry;\n        var interpolatorAngle = interpolateNumber(0, endAngle - startAngle);\n        var deltaAngle = interpolatorAngle(t);\n        var _latest = _objectSpread(_objectSpread({}, entry), {}, {\n          startAngle: curAngle + paddingAngle,\n          endAngle: curAngle + deltaAngle + paddingAngle\n        });\n        stepData.push(_latest);\n        curAngle = _latest.endAngle;\n      }\n    });\n\n    // eslint-disable-next-line no-param-reassign\n    previousSectorsRef.current = stepData;\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(PieSectors, {\n      sectors: stepData,\n      activeShape: activeShape,\n      inactiveShape: inactiveShape,\n      allOtherPieProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderSectors(props) {\n  var {\n    sectors,\n    isAnimationActive,\n    activeShape,\n    inactiveShape\n  } = props;\n  var previousSectorsRef = useRef(null);\n  var prevSectors = previousSectorsRef.current;\n  if (isAnimationActive && sectors && sectors.length && (!prevSectors || prevSectors !== sectors)) {\n    return /*#__PURE__*/React.createElement(SectorsWithAnimation, {\n      props: props,\n      previousSectorsRef: previousSectorsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(PieSectors, {\n    sectors: sectors,\n    activeShape: activeShape,\n    inactiveShape: inactiveShape,\n    allOtherPieProps: props,\n    showLabels: true\n  });\n}\nfunction PieWithTouchMove(props) {\n  var {\n    hide,\n    className,\n    rootTabIndex\n  } = props;\n  var layerClass = clsx('recharts-pie', className);\n  if (hide) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    tabIndex: rootTabIndex,\n    className: layerClass\n  }, /*#__PURE__*/React.createElement(RenderSectors, props));\n}\nvar defaultPieProps = {\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  cx: '50%',\n  cy: '50%',\n  dataKey: 'value',\n  endAngle: 360,\n  fill: '#808080',\n  hide: false,\n  innerRadius: 0,\n  isAnimationActive: !Global.isSsr,\n  labelLine: true,\n  legendType: 'rect',\n  minAngle: 0,\n  nameKey: 'name',\n  outerRadius: '80%',\n  paddingAngle: 0,\n  rootTabIndex: 0,\n  startAngle: 0,\n  stroke: '#fff'\n};\nfunction PieImpl(props) {\n  var propsWithDefaults = resolveDefaultProps(props, defaultPieProps);\n  var cells = useMemo(() => findAllByType(props.children, Cell), [props.children]);\n  var presentationProps = filterProps(propsWithDefaults, false);\n  var pieSettings = useMemo(() => ({\n    name: propsWithDefaults.name,\n    nameKey: propsWithDefaults.nameKey,\n    tooltipType: propsWithDefaults.tooltipType,\n    data: propsWithDefaults.data,\n    dataKey: propsWithDefaults.dataKey,\n    cx: propsWithDefaults.cx,\n    cy: propsWithDefaults.cy,\n    startAngle: propsWithDefaults.startAngle,\n    endAngle: propsWithDefaults.endAngle,\n    minAngle: propsWithDefaults.minAngle,\n    paddingAngle: propsWithDefaults.paddingAngle,\n    innerRadius: propsWithDefaults.innerRadius,\n    outerRadius: propsWithDefaults.outerRadius,\n    cornerRadius: propsWithDefaults.cornerRadius,\n    legendType: propsWithDefaults.legendType,\n    fill: propsWithDefaults.fill,\n    presentationProps\n  }), [propsWithDefaults.cornerRadius, propsWithDefaults.cx, propsWithDefaults.cy, propsWithDefaults.data, propsWithDefaults.dataKey, propsWithDefaults.endAngle, propsWithDefaults.innerRadius, propsWithDefaults.minAngle, propsWithDefaults.name, propsWithDefaults.nameKey, propsWithDefaults.outerRadius, propsWithDefaults.paddingAngle, propsWithDefaults.startAngle, propsWithDefaults.tooltipType, propsWithDefaults.legendType, propsWithDefaults.fill, presentationProps]);\n  var sectors = useAppSelector(state => selectPieSectors(state, pieSettings, cells));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, propsWithDefaults), {}, {\n      sectors\n    })\n  }), /*#__PURE__*/React.createElement(PieWithTouchMove, _extends({}, propsWithDefaults, {\n    sectors: sectors\n  })));\n}\nexport class Pie extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-pie-'));\n  }\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetPolarGraphicalItem, {\n      data: this.props.data,\n      dataKey: this.props.dataKey,\n      hide: this.props.hide,\n      angleAxisId: 0,\n      radiusAxisId: 0,\n      stackId: undefined,\n      barSize: undefined,\n      type: \"pie\"\n    }), /*#__PURE__*/React.createElement(SetPiePayloadLegend, this.props), /*#__PURE__*/React.createElement(PieImpl, this.props), this.props.children);\n  }\n}\n_defineProperty(Pie, \"displayName\", 'Pie');\n_defineProperty(Pie, \"defaultProps\", defaultPieProps);", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "ownKeys", "keys", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "_extends", "assign", "bind", "React", "PureComponent", "useCallback", "useMemo", "useRef", "useState", "get", "clsx", "selectPieLegend", "selectPieSectors", "useAppSelector", "SetPolarGraphicalItem", "Layer", "Curve", "Text", "Cell", "filterProps", "findAllByType", "Global", "getMaxRadius", "polarToCartesian", "getPercentValue", "interpolateNumber", "isNumber", "mathSign", "uniqueId", "getTooltipNameProp", "getValueByDataKey", "adaptEventsOfChild", "<PERSON><PERSON><PERSON>", "useMouseClickItemDispatch", "useMouseEnterItemDispatch", "useMouseLeaveItemDispatch", "SetTooltipEntrySettings", "selectActiveTooltipIndex", "SetPolarLegendPayload", "DATA_ITEM_DATAKEY_ATTRIBUTE_NAME", "DATA_ITEM_INDEX_ATTRIBUTE_NAME", "useAnimationId", "resolveDefaultProps", "Animate", "SetPiePayloadLegend", "props", "presentationProps", "cells", "children", "pieSettings", "name", "<PERSON><PERSON><PERSON>", "tooltipType", "data", "dataKey", "cx", "cy", "startAngle", "endAngle", "minAngle", "paddingAngle", "innerRadius", "outerRadius", "cornerRadius", "legendType", "fill", "legendPayload", "state", "createElement", "getTooltipEntrySettings", "sectors", "stroke", "strokeWidth", "hide", "dataDefinedOnItem", "map", "p", "tooltipPayload", "positions", "tooltipPosition", "settings", "type", "color", "unit", "getTextAnchor", "x", "getOuterRadius", "dataPoint", "maxPieRadius", "parseCoordinateOfPie", "item", "offset", "top", "left", "width", "height", "maxRadius", "Math", "sqrt", "parseDeltaAngle", "sign", "deltaAngle", "min", "abs", "renderLabelLineItem", "option", "isValidElement", "cloneElement", "className", "renderLabelItem", "label", "alignmentBaseline", "PieLabels", "_ref", "showLabels", "labelLine", "pieProps", "customLabelProps", "customLabelLineProps", "offsetRadius", "labels", "entry", "midAngle", "endPoint", "labelProps", "index", "textAnchor", "lineProps", "points", "key", "concat", "PieSectors", "activeShape", "inactiveShape", "inactiveShapeProp", "allOtherPieProps", "activeIndex", "onMouseEnter", "onMouseEnterFromProps", "onClick", "onItemClickFromProps", "onMouseLeave", "onMouseLeaveFromProps", "restOfAllOtherProps", "onMouseEnterFromContext", "onMouseLeaveFromContext", "onClickFromContext", "Fragment", "isSectorActive", "sectorOptions", "sectorProps", "tabIndex", "isActive", "shapeType", "computePieSectors", "_ref2", "_pieSettings$paddingA", "displayedData", "absDeltaAngle", "notZeroItemCount", "totalPaddingAngle", "realTotalAngle", "sum", "reduce", "result", "val", "prev", "coordinate", "percent", "tempStartAngle", "entryWithCellInfo", "tempEndAngle", "middleRadius", "payload", "SectorsWithAnimation", "_ref3", "previousSectorsRef", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "onAnimationStart", "onAnimationEnd", "animationId", "prevSectors", "current", "isAnimating", "setIsAnimating", "handleAnimationEnd", "handleAnimationStart", "begin", "duration", "easing", "from", "to", "_ref4", "stepData", "first", "curAngle", "angleIp", "latest", "interpolatorAngle", "_latest", "RenderSectors", "PieWithTouchMove", "rootTabIndex", "layerClass", "defaultPieProps", "isSsr", "PieImpl", "propsWithDefaults", "fn", "args", "Pie", "constructor", "render", "angleAxisId", "radiusAxisId", "stackId", "undefined", "barSize"], "sources": ["/home/<USER>/itai/node_modules/recharts/es6/polar/Pie.js"], "sourcesContent": ["var _excluded = [\"onMouseEnter\", \"onClick\", \"onMouseLeave\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from 'react';\nimport { PureComponent, useCallback, useMemo, useRef, useState } from 'react';\nimport get from 'es-toolkit/compat/get';\nimport { clsx } from 'clsx';\nimport { selectPieLegend, selectPieSectors } from '../state/selectors/pieSelectors';\nimport { useAppSelector } from '../state/hooks';\nimport { SetPolarGraphicalItem } from '../state/SetGraphicalItem';\nimport { Layer } from '../container/Layer';\nimport { Curve } from '../shape/Curve';\nimport { Text } from '../component/Text';\nimport { Cell } from '../component/Cell';\nimport { filterProps, findAllByType } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { getMaxRadius, polarToCartesian } from '../util/PolarUtils';\nimport { getPercentValue, interpolateNumber, isNumber, mathSign, uniqueId } from '../util/DataUtils';\nimport { getTooltipNameProp, getValueByDataKey } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { Shape } from '../util/ActiveShapeUtils';\nimport { useMouseClickItemDispatch, useMouseEnterItemDispatch, useMouseLeaveItemDispatch } from '../context/tooltipContext';\nimport { SetTooltipEntrySettings } from '../state/SetTooltipEntrySettings';\nimport { selectActiveTooltipIndex } from '../state/selectors/tooltipSelectors';\nimport { SetPolarLegendPayload } from '../state/SetLegendPayload';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { useAnimationId } from '../util/useAnimationId';\nimport { resolveDefaultProps } from '../util/resolveDefaultProps';\nimport { Animate } from '../animation/Animate';\n\n/**\n * Internal props, combination of external props + defaultProps + private Recharts state\n */\n\nfunction SetPiePayloadLegend(props) {\n  var presentationProps = useMemo(() => filterProps(props, false), [props]);\n  var cells = useMemo(() => findAllByType(props.children, Cell), [props.children]);\n  var pieSettings = useMemo(() => ({\n    name: props.name,\n    nameKey: props.nameKey,\n    tooltipType: props.tooltipType,\n    data: props.data,\n    dataKey: props.dataKey,\n    cx: props.cx,\n    cy: props.cy,\n    startAngle: props.startAngle,\n    endAngle: props.endAngle,\n    minAngle: props.minAngle,\n    paddingAngle: props.paddingAngle,\n    innerRadius: props.innerRadius,\n    outerRadius: props.outerRadius,\n    cornerRadius: props.cornerRadius,\n    legendType: props.legendType,\n    fill: props.fill,\n    presentationProps\n  }), [props.cornerRadius, props.cx, props.cy, props.data, props.dataKey, props.endAngle, props.innerRadius, props.minAngle, props.name, props.nameKey, props.outerRadius, props.paddingAngle, props.startAngle, props.tooltipType, props.legendType, props.fill, presentationProps]);\n  var legendPayload = useAppSelector(state => selectPieLegend(state, pieSettings, cells));\n  return /*#__PURE__*/React.createElement(SetPolarLegendPayload, {\n    legendPayload: legendPayload\n  });\n}\nfunction getTooltipEntrySettings(props) {\n  var {\n    dataKey,\n    nameKey,\n    sectors,\n    stroke,\n    strokeWidth,\n    fill,\n    name,\n    hide,\n    tooltipType\n  } = props;\n  return {\n    dataDefinedOnItem: sectors === null || sectors === void 0 ? void 0 : sectors.map(p => p.tooltipPayload),\n    positions: sectors === null || sectors === void 0 ? void 0 : sectors.map(p => p.tooltipPosition),\n    settings: {\n      stroke,\n      strokeWidth,\n      fill,\n      dataKey,\n      nameKey,\n      name: getTooltipNameProp(name, dataKey),\n      hide,\n      type: tooltipType,\n      color: fill,\n      unit: '' // why doesn't Pie support unit?\n    }\n  };\n}\nvar getTextAnchor = (x, cx) => {\n  if (x > cx) {\n    return 'start';\n  }\n  if (x < cx) {\n    return 'end';\n  }\n  return 'middle';\n};\nvar getOuterRadius = (dataPoint, outerRadius, maxPieRadius) => {\n  if (typeof outerRadius === 'function') {\n    return outerRadius(dataPoint);\n  }\n  return getPercentValue(outerRadius, maxPieRadius, maxPieRadius * 0.8);\n};\nvar parseCoordinateOfPie = (item, offset, dataPoint) => {\n  var {\n    top,\n    left,\n    width,\n    height\n  } = offset;\n  var maxPieRadius = getMaxRadius(width, height);\n  var cx = left + getPercentValue(item.cx, width, width / 2);\n  var cy = top + getPercentValue(item.cy, height, height / 2);\n  var innerRadius = getPercentValue(item.innerRadius, maxPieRadius, 0);\n  var outerRadius = getOuterRadius(dataPoint, item.outerRadius, maxPieRadius);\n  var maxRadius = item.maxRadius || Math.sqrt(width * width + height * height) / 2;\n  return {\n    cx,\n    cy,\n    innerRadius,\n    outerRadius,\n    maxRadius\n  };\n};\nvar parseDeltaAngle = (startAngle, endAngle) => {\n  var sign = mathSign(endAngle - startAngle);\n  var deltaAngle = Math.min(Math.abs(endAngle - startAngle), 360);\n  return sign * deltaAngle;\n};\nvar renderLabelLineItem = (option, props) => {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  if (typeof option === 'function') {\n    return option(props);\n  }\n  var className = clsx('recharts-pie-label-line', typeof option !== 'boolean' ? option.className : '');\n  return /*#__PURE__*/React.createElement(Curve, _extends({}, props, {\n    type: \"linear\",\n    className: className\n  }));\n};\nvar renderLabelItem = (option, props, value) => {\n  if (/*#__PURE__*/React.isValidElement(option)) {\n    return /*#__PURE__*/React.cloneElement(option, props);\n  }\n  var label = value;\n  if (typeof option === 'function') {\n    label = option(props);\n    if (/*#__PURE__*/React.isValidElement(label)) {\n      return label;\n    }\n  }\n  var className = clsx('recharts-pie-label-text', typeof option !== 'boolean' && typeof option !== 'function' ? option.className : '');\n  return /*#__PURE__*/React.createElement(Text, _extends({}, props, {\n    alignmentBaseline: \"middle\",\n    className: className\n  }), label);\n};\nfunction PieLabels(_ref) {\n  var {\n    sectors,\n    props,\n    showLabels\n  } = _ref;\n  var {\n    label,\n    labelLine,\n    dataKey\n  } = props;\n  if (!showLabels || !label || !sectors) {\n    return null;\n  }\n  var pieProps = filterProps(props, false);\n  var customLabelProps = filterProps(label, false);\n  var customLabelLineProps = filterProps(labelLine, false);\n  var offsetRadius = typeof label === 'object' && 'offsetRadius' in label && label.offsetRadius || 20;\n  var labels = sectors.map((entry, i) => {\n    var midAngle = (entry.startAngle + entry.endAngle) / 2;\n    var endPoint = polarToCartesian(entry.cx, entry.cy, entry.outerRadius + offsetRadius, midAngle);\n    var labelProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n      stroke: 'none'\n    }, customLabelProps), {}, {\n      index: i,\n      textAnchor: getTextAnchor(endPoint.x, entry.cx)\n    }, endPoint);\n    var lineProps = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieProps), entry), {}, {\n      fill: 'none',\n      stroke: entry.fill\n    }, customLabelLineProps), {}, {\n      index: i,\n      points: [polarToCartesian(entry.cx, entry.cy, entry.outerRadius, midAngle), endPoint],\n      key: 'line'\n    });\n    return (\n      /*#__PURE__*/\n      // eslint-disable-next-line react/no-array-index-key\n      React.createElement(Layer, {\n        key: \"label-\".concat(entry.startAngle, \"-\").concat(entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n      }, labelLine && renderLabelLineItem(labelLine, lineProps), renderLabelItem(label, labelProps, getValueByDataKey(entry, dataKey)))\n    );\n  });\n  return /*#__PURE__*/React.createElement(Layer, {\n    className: \"recharts-pie-labels\"\n  }, labels);\n}\nfunction PieSectors(props) {\n  var {\n    sectors,\n    activeShape,\n    inactiveShape: inactiveShapeProp,\n    allOtherPieProps,\n    showLabels\n  } = props;\n  var activeIndex = useAppSelector(selectActiveTooltipIndex);\n  var {\n      onMouseEnter: onMouseEnterFromProps,\n      onClick: onItemClickFromProps,\n      onMouseLeave: onMouseLeaveFromProps\n    } = allOtherPieProps,\n    restOfAllOtherProps = _objectWithoutProperties(allOtherPieProps, _excluded);\n  var onMouseEnterFromContext = useMouseEnterItemDispatch(onMouseEnterFromProps, allOtherPieProps.dataKey);\n  var onMouseLeaveFromContext = useMouseLeaveItemDispatch(onMouseLeaveFromProps);\n  var onClickFromContext = useMouseClickItemDispatch(onItemClickFromProps, allOtherPieProps.dataKey);\n  if (sectors == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, sectors.map((entry, i) => {\n    if ((entry === null || entry === void 0 ? void 0 : entry.startAngle) === 0 && (entry === null || entry === void 0 ? void 0 : entry.endAngle) === 0 && sectors.length !== 1) return null;\n    var isSectorActive = activeShape && String(i) === activeIndex;\n    var inactiveShape = activeIndex ? inactiveShapeProp : null;\n    var sectorOptions = isSectorActive ? activeShape : inactiveShape;\n    var sectorProps = _objectSpread(_objectSpread({}, entry), {}, {\n      stroke: entry.stroke,\n      tabIndex: -1,\n      [DATA_ITEM_INDEX_ATTRIBUTE_NAME]: i,\n      [DATA_ITEM_DATAKEY_ATTRIBUTE_NAME]: allOtherPieProps.dataKey\n    });\n    return /*#__PURE__*/React.createElement(Layer, _extends({\n      tabIndex: -1,\n      className: \"recharts-pie-sector\"\n    }, adaptEventsOfChild(restOfAllOtherProps, entry, i), {\n      // @ts-expect-error the types need a bit of attention\n      onMouseEnter: onMouseEnterFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onMouseLeave: onMouseLeaveFromContext(entry, i)\n      // @ts-expect-error the types need a bit of attention\n      ,\n      onClick: onClickFromContext(entry, i)\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      key: \"sector-\".concat(entry === null || entry === void 0 ? void 0 : entry.startAngle, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.endAngle, \"-\").concat(entry.midAngle, \"-\").concat(i)\n    }), /*#__PURE__*/React.createElement(Shape, _extends({\n      option: sectorOptions,\n      isActive: isSectorActive,\n      shapeType: \"sector\"\n    }, sectorProps)));\n  }), /*#__PURE__*/React.createElement(PieLabels, {\n    sectors: sectors,\n    props: allOtherPieProps,\n    showLabels: showLabels\n  }));\n}\nexport function computePieSectors(_ref2) {\n  var _pieSettings$paddingA;\n  var {\n    pieSettings,\n    displayedData,\n    cells,\n    offset\n  } = _ref2;\n  var {\n    cornerRadius,\n    startAngle,\n    endAngle,\n    dataKey,\n    nameKey,\n    tooltipType\n  } = pieSettings;\n  var minAngle = Math.abs(pieSettings.minAngle);\n  var deltaAngle = parseDeltaAngle(startAngle, endAngle);\n  var absDeltaAngle = Math.abs(deltaAngle);\n  var paddingAngle = displayedData.length <= 1 ? 0 : (_pieSettings$paddingA = pieSettings.paddingAngle) !== null && _pieSettings$paddingA !== void 0 ? _pieSettings$paddingA : 0;\n  var notZeroItemCount = displayedData.filter(entry => getValueByDataKey(entry, dataKey, 0) !== 0).length;\n  var totalPaddingAngle = (absDeltaAngle >= 360 ? notZeroItemCount : notZeroItemCount - 1) * paddingAngle;\n  var realTotalAngle = absDeltaAngle - notZeroItemCount * minAngle - totalPaddingAngle;\n  var sum = displayedData.reduce((result, entry) => {\n    var val = getValueByDataKey(entry, dataKey, 0);\n    return result + (isNumber(val) ? val : 0);\n  }, 0);\n  var sectors;\n  if (sum > 0) {\n    var prev;\n    sectors = displayedData.map((entry, i) => {\n      var val = getValueByDataKey(entry, dataKey, 0);\n      var name = getValueByDataKey(entry, nameKey, i);\n      var coordinate = parseCoordinateOfPie(pieSettings, offset, entry);\n      var percent = (isNumber(val) ? val : 0) / sum;\n      var tempStartAngle;\n      var entryWithCellInfo = _objectSpread(_objectSpread({}, entry), cells && cells[i] && cells[i].props);\n      if (i) {\n        tempStartAngle = prev.endAngle + mathSign(deltaAngle) * paddingAngle * (val !== 0 ? 1 : 0);\n      } else {\n        tempStartAngle = startAngle;\n      }\n      var tempEndAngle = tempStartAngle + mathSign(deltaAngle) * ((val !== 0 ? minAngle : 0) + percent * realTotalAngle);\n      var midAngle = (tempStartAngle + tempEndAngle) / 2;\n      var middleRadius = (coordinate.innerRadius + coordinate.outerRadius) / 2;\n      var tooltipPayload = [{\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        name,\n        // @ts-expect-error getValueByDataKey does not validate the output type\n        value: val,\n        payload: entryWithCellInfo,\n        dataKey,\n        type: tooltipType\n      }];\n      var tooltipPosition = polarToCartesian(coordinate.cx, coordinate.cy, middleRadius, midAngle);\n      prev = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, pieSettings.presentationProps), {}, {\n        percent,\n        cornerRadius,\n        name,\n        tooltipPayload,\n        midAngle,\n        middleRadius,\n        tooltipPosition\n      }, entryWithCellInfo), coordinate), {}, {\n        value: getValueByDataKey(entry, dataKey),\n        startAngle: tempStartAngle,\n        endAngle: tempEndAngle,\n        payload: entryWithCellInfo,\n        paddingAngle: mathSign(deltaAngle) * paddingAngle\n      });\n      return prev;\n    });\n  }\n  return sectors;\n}\nfunction SectorsWithAnimation(_ref3) {\n  var {\n    props,\n    previousSectorsRef\n  } = _ref3;\n  var {\n    sectors,\n    isAnimationActive,\n    animationBegin,\n    animationDuration,\n    animationEasing,\n    activeShape,\n    inactiveShape,\n    onAnimationStart,\n    onAnimationEnd\n  } = props;\n  var animationId = useAnimationId(props, 'recharts-pie-');\n  var prevSectors = previousSectorsRef.current;\n  var [isAnimating, setIsAnimating] = useState(true);\n  var handleAnimationEnd = useCallback(() => {\n    if (typeof onAnimationEnd === 'function') {\n      onAnimationEnd();\n    }\n    setIsAnimating(false);\n  }, [onAnimationEnd]);\n  var handleAnimationStart = useCallback(() => {\n    if (typeof onAnimationStart === 'function') {\n      onAnimationStart();\n    }\n    setIsAnimating(true);\n  }, [onAnimationStart]);\n  return /*#__PURE__*/React.createElement(Animate, {\n    begin: animationBegin,\n    duration: animationDuration,\n    isActive: isAnimationActive,\n    easing: animationEasing,\n    from: {\n      t: 0\n    },\n    to: {\n      t: 1\n    },\n    onAnimationStart: handleAnimationStart,\n    onAnimationEnd: handleAnimationEnd,\n    key: animationId\n  }, _ref4 => {\n    var {\n      t\n    } = _ref4;\n    var stepData = [];\n    var first = sectors && sectors[0];\n    var curAngle = first.startAngle;\n    sectors.forEach((entry, index) => {\n      var prev = prevSectors && prevSectors[index];\n      var paddingAngle = index > 0 ? get(entry, 'paddingAngle', 0) : 0;\n      if (prev) {\n        var angleIp = interpolateNumber(prev.endAngle - prev.startAngle, entry.endAngle - entry.startAngle);\n        var latest = _objectSpread(_objectSpread({}, entry), {}, {\n          startAngle: curAngle + paddingAngle,\n          endAngle: curAngle + angleIp(t) + paddingAngle\n        });\n        stepData.push(latest);\n        curAngle = latest.endAngle;\n      } else {\n        var {\n          endAngle,\n          startAngle\n        } = entry;\n        var interpolatorAngle = interpolateNumber(0, endAngle - startAngle);\n        var deltaAngle = interpolatorAngle(t);\n        var _latest = _objectSpread(_objectSpread({}, entry), {}, {\n          startAngle: curAngle + paddingAngle,\n          endAngle: curAngle + deltaAngle + paddingAngle\n        });\n        stepData.push(_latest);\n        curAngle = _latest.endAngle;\n      }\n    });\n\n    // eslint-disable-next-line no-param-reassign\n    previousSectorsRef.current = stepData;\n    return /*#__PURE__*/React.createElement(Layer, null, /*#__PURE__*/React.createElement(PieSectors, {\n      sectors: stepData,\n      activeShape: activeShape,\n      inactiveShape: inactiveShape,\n      allOtherPieProps: props,\n      showLabels: !isAnimating\n    }));\n  });\n}\nfunction RenderSectors(props) {\n  var {\n    sectors,\n    isAnimationActive,\n    activeShape,\n    inactiveShape\n  } = props;\n  var previousSectorsRef = useRef(null);\n  var prevSectors = previousSectorsRef.current;\n  if (isAnimationActive && sectors && sectors.length && (!prevSectors || prevSectors !== sectors)) {\n    return /*#__PURE__*/React.createElement(SectorsWithAnimation, {\n      props: props,\n      previousSectorsRef: previousSectorsRef\n    });\n  }\n  return /*#__PURE__*/React.createElement(PieSectors, {\n    sectors: sectors,\n    activeShape: activeShape,\n    inactiveShape: inactiveShape,\n    allOtherPieProps: props,\n    showLabels: true\n  });\n}\nfunction PieWithTouchMove(props) {\n  var {\n    hide,\n    className,\n    rootTabIndex\n  } = props;\n  var layerClass = clsx('recharts-pie', className);\n  if (hide) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(Layer, {\n    tabIndex: rootTabIndex,\n    className: layerClass\n  }, /*#__PURE__*/React.createElement(RenderSectors, props));\n}\nvar defaultPieProps = {\n  animationBegin: 400,\n  animationDuration: 1500,\n  animationEasing: 'ease',\n  cx: '50%',\n  cy: '50%',\n  dataKey: 'value',\n  endAngle: 360,\n  fill: '#808080',\n  hide: false,\n  innerRadius: 0,\n  isAnimationActive: !Global.isSsr,\n  labelLine: true,\n  legendType: 'rect',\n  minAngle: 0,\n  nameKey: 'name',\n  outerRadius: '80%',\n  paddingAngle: 0,\n  rootTabIndex: 0,\n  startAngle: 0,\n  stroke: '#fff'\n};\nfunction PieImpl(props) {\n  var propsWithDefaults = resolveDefaultProps(props, defaultPieProps);\n  var cells = useMemo(() => findAllByType(props.children, Cell), [props.children]);\n  var presentationProps = filterProps(propsWithDefaults, false);\n  var pieSettings = useMemo(() => ({\n    name: propsWithDefaults.name,\n    nameKey: propsWithDefaults.nameKey,\n    tooltipType: propsWithDefaults.tooltipType,\n    data: propsWithDefaults.data,\n    dataKey: propsWithDefaults.dataKey,\n    cx: propsWithDefaults.cx,\n    cy: propsWithDefaults.cy,\n    startAngle: propsWithDefaults.startAngle,\n    endAngle: propsWithDefaults.endAngle,\n    minAngle: propsWithDefaults.minAngle,\n    paddingAngle: propsWithDefaults.paddingAngle,\n    innerRadius: propsWithDefaults.innerRadius,\n    outerRadius: propsWithDefaults.outerRadius,\n    cornerRadius: propsWithDefaults.cornerRadius,\n    legendType: propsWithDefaults.legendType,\n    fill: propsWithDefaults.fill,\n    presentationProps\n  }), [propsWithDefaults.cornerRadius, propsWithDefaults.cx, propsWithDefaults.cy, propsWithDefaults.data, propsWithDefaults.dataKey, propsWithDefaults.endAngle, propsWithDefaults.innerRadius, propsWithDefaults.minAngle, propsWithDefaults.name, propsWithDefaults.nameKey, propsWithDefaults.outerRadius, propsWithDefaults.paddingAngle, propsWithDefaults.startAngle, propsWithDefaults.tooltipType, propsWithDefaults.legendType, propsWithDefaults.fill, presentationProps]);\n  var sectors = useAppSelector(state => selectPieSectors(state, pieSettings, cells));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetTooltipEntrySettings, {\n    fn: getTooltipEntrySettings,\n    args: _objectSpread(_objectSpread({}, propsWithDefaults), {}, {\n      sectors\n    })\n  }), /*#__PURE__*/React.createElement(PieWithTouchMove, _extends({}, propsWithDefaults, {\n    sectors: sectors\n  })));\n}\nexport class Pie extends PureComponent {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"id\", uniqueId('recharts-pie-'));\n  }\n  render() {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(SetPolarGraphicalItem, {\n      data: this.props.data,\n      dataKey: this.props.dataKey,\n      hide: this.props.hide,\n      angleAxisId: 0,\n      radiusAxisId: 0,\n      stackId: undefined,\n      barSize: undefined,\n      type: \"pie\"\n    }), /*#__PURE__*/React.createElement(SetPiePayloadLegend, this.props), /*#__PURE__*/React.createElement(PieImpl, this.props), this.props.children);\n  }\n}\n_defineProperty(Pie, \"displayName\", 'Pie');\n_defineProperty(Pie, \"defaultProps\", defaultPieProps);"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,cAAc,EAAE,SAAS,EAAE,cAAc,CAAC;AAC3D,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,OAAOA,CAACd,CAAC,EAAEG,CAAC,EAAE;EAAE,IAAIF,CAAC,GAAGK,MAAM,CAACS,IAAI,CAACf,CAAC,CAAC;EAAE,IAAIM,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIL,CAAC,GAAGI,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAEG,CAAC,KAAKD,CAAC,GAAGA,CAAC,CAACc,MAAM,CAAC,UAAUb,CAAC,EAAE;MAAE,OAAOG,MAAM,CAACW,wBAAwB,CAACjB,CAAC,EAAEG,CAAC,CAAC,CAACe,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEjB,CAAC,CAACkB,IAAI,CAACC,KAAK,CAACnB,CAAC,EAAEC,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AAC9P,SAASoB,aAAaA,CAACrB,CAAC,EAAE;EAAE,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,SAAS,CAACb,MAAM,EAAEN,CAAC,EAAE,EAAE;IAAE,IAAIF,CAAC,GAAG,IAAI,IAAIqB,SAAS,CAACnB,CAAC,CAAC,GAAGmB,SAAS,CAACnB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGW,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEqB,eAAe,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGG,MAAM,CAACmB,yBAAyB,GAAGnB,MAAM,CAACoB,gBAAgB,CAAC1B,CAAC,EAAEM,MAAM,CAACmB,yBAAyB,CAACxB,CAAC,CAAC,CAAC,GAAGa,OAAO,CAACR,MAAM,CAACL,CAAC,CAAC,CAAC,CAACsB,OAAO,CAAC,UAAUpB,CAAC,EAAE;MAAEG,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAEG,MAAM,CAACW,wBAAwB,CAAChB,CAAC,EAAEE,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOH,CAAC;AAAE;AACtb,SAASwB,eAAeA,CAACxB,CAAC,EAAEG,CAAC,EAAEF,CAAC,EAAE;EAAE,OAAO,CAACE,CAAC,GAAGyB,cAAc,CAACzB,CAAC,CAAC,KAAKH,CAAC,GAAGM,MAAM,CAACqB,cAAc,CAAC3B,CAAC,EAAEG,CAAC,EAAE;IAAE0B,KAAK,EAAE5B,CAAC;IAAEiB,UAAU,EAAE,CAAC,CAAC;IAAEY,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAG/B,CAAC,CAACG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC;AAAE;AACnL,SAAS4B,cAAcA,CAAC3B,CAAC,EAAE;EAAE,IAAIG,CAAC,GAAG4B,YAAY,CAAC/B,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAAS4B,YAAYA,CAAC/B,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOF,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACgC,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKlC,CAAC,EAAE;IAAE,IAAII,CAAC,GAAGJ,CAAC,CAACY,IAAI,CAACX,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI+B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKhC,CAAC,GAAGiC,MAAM,GAAGC,MAAM,EAAEpC,CAAC,CAAC;AAAE;AACvT,SAASqC,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGhC,MAAM,CAACiC,MAAM,GAAGjC,MAAM,CAACiC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUhC,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,SAAS,CAACb,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGqB,SAAS,CAACtB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAE8B,QAAQ,CAAClB,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;AAAE;AACnR,OAAO,KAAKmB,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC7E,OAAOC,GAAG,MAAM,uBAAuB;AACvC,SAASC,IAAI,QAAQ,MAAM;AAC3B,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,iCAAiC;AACnF,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,WAAW,EAAEC,aAAa,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,oBAAoB;AACnE,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,mBAAmB;AACpG,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,oBAAoB;AAC1E,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,yBAAyB,EAAEC,yBAAyB,EAAEC,yBAAyB,QAAQ,2BAA2B;AAC3H,SAASC,uBAAuB,QAAQ,kCAAkC;AAC1E,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,gCAAgC,EAAEC,8BAA8B,QAAQ,mBAAmB;AACpG,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,OAAO,QAAQ,sBAAsB;;AAE9C;AACA;AACA;;AAEA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,IAAIC,iBAAiB,GAAGxC,OAAO,CAAC,MAAMa,WAAW,CAAC0B,KAAK,EAAE,KAAK,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACzE,IAAIE,KAAK,GAAGzC,OAAO,CAAC,MAAMc,aAAa,CAACyB,KAAK,CAACG,QAAQ,EAAE9B,IAAI,CAAC,EAAE,CAAC2B,KAAK,CAACG,QAAQ,CAAC,CAAC;EAChF,IAAIC,WAAW,GAAG3C,OAAO,CAAC,OAAO;IAC/B4C,IAAI,EAAEL,KAAK,CAACK,IAAI;IAChBC,OAAO,EAAEN,KAAK,CAACM,OAAO;IACtBC,WAAW,EAAEP,KAAK,CAACO,WAAW;IAC9BC,IAAI,EAAER,KAAK,CAACQ,IAAI;IAChBC,OAAO,EAAET,KAAK,CAACS,OAAO;IACtBC,EAAE,EAAEV,KAAK,CAACU,EAAE;IACZC,EAAE,EAAEX,KAAK,CAACW,EAAE;IACZC,UAAU,EAAEZ,KAAK,CAACY,UAAU;IAC5BC,QAAQ,EAAEb,KAAK,CAACa,QAAQ;IACxBC,QAAQ,EAAEd,KAAK,CAACc,QAAQ;IACxBC,YAAY,EAAEf,KAAK,CAACe,YAAY;IAChCC,WAAW,EAAEhB,KAAK,CAACgB,WAAW;IAC9BC,WAAW,EAAEjB,KAAK,CAACiB,WAAW;IAC9BC,YAAY,EAAElB,KAAK,CAACkB,YAAY;IAChCC,UAAU,EAAEnB,KAAK,CAACmB,UAAU;IAC5BC,IAAI,EAAEpB,KAAK,CAACoB,IAAI;IAChBnB;EACF,CAAC,CAAC,EAAE,CAACD,KAAK,CAACkB,YAAY,EAAElB,KAAK,CAACU,EAAE,EAAEV,KAAK,CAACW,EAAE,EAAEX,KAAK,CAACQ,IAAI,EAAER,KAAK,CAACS,OAAO,EAAET,KAAK,CAACa,QAAQ,EAAEb,KAAK,CAACgB,WAAW,EAAEhB,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAACK,IAAI,EAAEL,KAAK,CAACM,OAAO,EAAEN,KAAK,CAACiB,WAAW,EAAEjB,KAAK,CAACe,YAAY,EAAEf,KAAK,CAACY,UAAU,EAAEZ,KAAK,CAACO,WAAW,EAAEP,KAAK,CAACmB,UAAU,EAAEnB,KAAK,CAACoB,IAAI,EAAEnB,iBAAiB,CAAC,CAAC;EACnR,IAAIoB,aAAa,GAAGrD,cAAc,CAACsD,KAAK,IAAIxD,eAAe,CAACwD,KAAK,EAAElB,WAAW,EAAEF,KAAK,CAAC,CAAC;EACvF,OAAO,aAAa5C,KAAK,CAACiE,aAAa,CAAC9B,qBAAqB,EAAE;IAC7D4B,aAAa,EAAEA;EACjB,CAAC,CAAC;AACJ;AACA,SAASG,uBAAuBA,CAACxB,KAAK,EAAE;EACtC,IAAI;IACFS,OAAO;IACPH,OAAO;IACPmB,OAAO;IACPC,MAAM;IACNC,WAAW;IACXP,IAAI;IACJf,IAAI;IACJuB,IAAI;IACJrB;EACF,CAAC,GAAGP,KAAK;EACT,OAAO;IACL6B,iBAAiB,EAAEJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC;IACvGC,SAAS,EAAER,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC;IAChGC,QAAQ,EAAE;MACRT,MAAM;MACNC,WAAW;MACXP,IAAI;MACJX,OAAO;MACPH,OAAO;MACPD,IAAI,EAAErB,kBAAkB,CAACqB,IAAI,EAAEI,OAAO,CAAC;MACvCmB,IAAI;MACJQ,IAAI,EAAE7B,WAAW;MACjB8B,KAAK,EAAEjB,IAAI;MACXkB,IAAI,EAAE,EAAE,CAAC;IACX;EACF,CAAC;AACH;AACA,IAAIC,aAAa,GAAGA,CAACC,CAAC,EAAE9B,EAAE,KAAK;EAC7B,IAAI8B,CAAC,GAAG9B,EAAE,EAAE;IACV,OAAO,OAAO;EAChB;EACA,IAAI8B,CAAC,GAAG9B,EAAE,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAO,QAAQ;AACjB,CAAC;AACD,IAAI+B,cAAc,GAAGA,CAACC,SAAS,EAAEzB,WAAW,EAAE0B,YAAY,KAAK;EAC7D,IAAI,OAAO1B,WAAW,KAAK,UAAU,EAAE;IACrC,OAAOA,WAAW,CAACyB,SAAS,CAAC;EAC/B;EACA,OAAO/D,eAAe,CAACsC,WAAW,EAAE0B,YAAY,EAAEA,YAAY,GAAG,GAAG,CAAC;AACvE,CAAC;AACD,IAAIC,oBAAoB,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEJ,SAAS,KAAK;EACtD,IAAI;IACFK,GAAG;IACHC,IAAI;IACJC,KAAK;IACLC;EACF,CAAC,GAAGJ,MAAM;EACV,IAAIH,YAAY,GAAGlE,YAAY,CAACwE,KAAK,EAAEC,MAAM,CAAC;EAC9C,IAAIxC,EAAE,GAAGsC,IAAI,GAAGrE,eAAe,CAACkE,IAAI,CAACnC,EAAE,EAAEuC,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;EAC1D,IAAItC,EAAE,GAAGoC,GAAG,GAAGpE,eAAe,CAACkE,IAAI,CAAClC,EAAE,EAAEuC,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;EAC3D,IAAIlC,WAAW,GAAGrC,eAAe,CAACkE,IAAI,CAAC7B,WAAW,EAAE2B,YAAY,EAAE,CAAC,CAAC;EACpE,IAAI1B,WAAW,GAAGwB,cAAc,CAACC,SAAS,EAAEG,IAAI,CAAC5B,WAAW,EAAE0B,YAAY,CAAC;EAC3E,IAAIQ,SAAS,GAAGN,IAAI,CAACM,SAAS,IAAIC,IAAI,CAACC,IAAI,CAACJ,KAAK,GAAGA,KAAK,GAAGC,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC;EAChF,OAAO;IACLxC,EAAE;IACFC,EAAE;IACFK,WAAW;IACXC,WAAW;IACXkC;EACF,CAAC;AACH,CAAC;AACD,IAAIG,eAAe,GAAGA,CAAC1C,UAAU,EAAEC,QAAQ,KAAK;EAC9C,IAAI0C,IAAI,GAAGzE,QAAQ,CAAC+B,QAAQ,GAAGD,UAAU,CAAC;EAC1C,IAAI4C,UAAU,GAAGJ,IAAI,CAACK,GAAG,CAACL,IAAI,CAACM,GAAG,CAAC7C,QAAQ,GAAGD,UAAU,CAAC,EAAE,GAAG,CAAC;EAC/D,OAAO2C,IAAI,GAAGC,UAAU;AAC1B,CAAC;AACD,IAAIG,mBAAmB,GAAGA,CAACC,MAAM,EAAE5D,KAAK,KAAK;EAC3C,IAAI,aAAa1C,KAAK,CAACuG,cAAc,CAACD,MAAM,CAAC,EAAE;IAC7C,OAAO,aAAatG,KAAK,CAACwG,YAAY,CAACF,MAAM,EAAE5D,KAAK,CAAC;EACvD;EACA,IAAI,OAAO4D,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAAC5D,KAAK,CAAC;EACtB;EACA,IAAI+D,SAAS,GAAGlG,IAAI,CAAC,yBAAyB,EAAE,OAAO+F,MAAM,KAAK,SAAS,GAAGA,MAAM,CAACG,SAAS,GAAG,EAAE,CAAC;EACpG,OAAO,aAAazG,KAAK,CAACiE,aAAa,CAACpD,KAAK,EAAEhB,QAAQ,CAAC,CAAC,CAAC,EAAE6C,KAAK,EAAE;IACjEoC,IAAI,EAAE,QAAQ;IACd2B,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIC,eAAe,GAAGA,CAACJ,MAAM,EAAE5D,KAAK,EAAEtD,KAAK,KAAK;EAC9C,IAAI,aAAaY,KAAK,CAACuG,cAAc,CAACD,MAAM,CAAC,EAAE;IAC7C,OAAO,aAAatG,KAAK,CAACwG,YAAY,CAACF,MAAM,EAAE5D,KAAK,CAAC;EACvD;EACA,IAAIiE,KAAK,GAAGvH,KAAK;EACjB,IAAI,OAAOkH,MAAM,KAAK,UAAU,EAAE;IAChCK,KAAK,GAAGL,MAAM,CAAC5D,KAAK,CAAC;IACrB,IAAI,aAAa1C,KAAK,CAACuG,cAAc,CAACI,KAAK,CAAC,EAAE;MAC5C,OAAOA,KAAK;IACd;EACF;EACA,IAAIF,SAAS,GAAGlG,IAAI,CAAC,yBAAyB,EAAE,OAAO+F,MAAM,KAAK,SAAS,IAAI,OAAOA,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACG,SAAS,GAAG,EAAE,CAAC;EACpI,OAAO,aAAazG,KAAK,CAACiE,aAAa,CAACnD,IAAI,EAAEjB,QAAQ,CAAC,CAAC,CAAC,EAAE6C,KAAK,EAAE;IAChEkE,iBAAiB,EAAE,QAAQ;IAC3BH,SAAS,EAAEA;EACb,CAAC,CAAC,EAAEE,KAAK,CAAC;AACZ,CAAC;AACD,SAASE,SAASA,CAACC,IAAI,EAAE;EACvB,IAAI;IACF3C,OAAO;IACPzB,KAAK;IACLqE;EACF,CAAC,GAAGD,IAAI;EACR,IAAI;IACFH,KAAK;IACLK,SAAS;IACT7D;EACF,CAAC,GAAGT,KAAK;EACT,IAAI,CAACqE,UAAU,IAAI,CAACJ,KAAK,IAAI,CAACxC,OAAO,EAAE;IACrC,OAAO,IAAI;EACb;EACA,IAAI8C,QAAQ,GAAGjG,WAAW,CAAC0B,KAAK,EAAE,KAAK,CAAC;EACxC,IAAIwE,gBAAgB,GAAGlG,WAAW,CAAC2F,KAAK,EAAE,KAAK,CAAC;EAChD,IAAIQ,oBAAoB,GAAGnG,WAAW,CAACgG,SAAS,EAAE,KAAK,CAAC;EACxD,IAAII,YAAY,GAAG,OAAOT,KAAK,KAAK,QAAQ,IAAI,cAAc,IAAIA,KAAK,IAAIA,KAAK,CAACS,YAAY,IAAI,EAAE;EACnG,IAAIC,MAAM,GAAGlD,OAAO,CAACK,GAAG,CAAC,CAAC8C,KAAK,EAAE3J,CAAC,KAAK;IACrC,IAAI4J,QAAQ,GAAG,CAACD,KAAK,CAAChE,UAAU,GAAGgE,KAAK,CAAC/D,QAAQ,IAAI,CAAC;IACtD,IAAIiE,QAAQ,GAAGpG,gBAAgB,CAACkG,KAAK,CAAClE,EAAE,EAAEkE,KAAK,CAACjE,EAAE,EAAEiE,KAAK,CAAC3D,WAAW,GAAGyD,YAAY,EAAEG,QAAQ,CAAC;IAC/F,IAAIE,UAAU,GAAG7I,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqI,QAAQ,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAClGlD,MAAM,EAAE;IACV,CAAC,EAAE8C,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MACxBQ,KAAK,EAAE/J,CAAC;MACRgK,UAAU,EAAE1C,aAAa,CAACuC,QAAQ,CAACtC,CAAC,EAAEoC,KAAK,CAAClE,EAAE;IAChD,CAAC,EAAEoE,QAAQ,CAAC;IACZ,IAAII,SAAS,GAAGhJ,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqI,QAAQ,CAAC,EAAEK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjGxD,IAAI,EAAE,MAAM;MACZM,MAAM,EAAEkD,KAAK,CAACxD;IAChB,CAAC,EAAEqD,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5BO,KAAK,EAAE/J,CAAC;MACRkK,MAAM,EAAE,CAACzG,gBAAgB,CAACkG,KAAK,CAAClE,EAAE,EAAEkE,KAAK,CAACjE,EAAE,EAAEiE,KAAK,CAAC3D,WAAW,EAAE4D,QAAQ,CAAC,EAAEC,QAAQ,CAAC;MACrFM,GAAG,EAAE;IACP,CAAC,CAAC;IACF,QACE;MACA;MACA9H,KAAK,CAACiE,aAAa,CAACrD,KAAK,EAAE;QACzBkH,GAAG,EAAE,QAAQ,CAACC,MAAM,CAACT,KAAK,CAAChE,UAAU,EAAE,GAAG,CAAC,CAACyE,MAAM,CAACT,KAAK,CAAC/D,QAAQ,EAAE,GAAG,CAAC,CAACwE,MAAM,CAACT,KAAK,CAACC,QAAQ,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACpK,CAAC;MAC9G,CAAC,EAAEqJ,SAAS,IAAIX,mBAAmB,CAACW,SAAS,EAAEY,SAAS,CAAC,EAAElB,eAAe,CAACC,KAAK,EAAEc,UAAU,EAAE9F,iBAAiB,CAAC2F,KAAK,EAAEnE,OAAO,CAAC,CAAC;IAAC;EAErI,CAAC,CAAC;EACF,OAAO,aAAanD,KAAK,CAACiE,aAAa,CAACrD,KAAK,EAAE;IAC7C6F,SAAS,EAAE;EACb,CAAC,EAAEY,MAAM,CAAC;AACZ;AACA,SAASW,UAAUA,CAACtF,KAAK,EAAE;EACzB,IAAI;IACFyB,OAAO;IACP8D,WAAW;IACXC,aAAa,EAAEC,iBAAiB;IAChCC,gBAAgB;IAChBrB;EACF,CAAC,GAAGrE,KAAK;EACT,IAAI2F,WAAW,GAAG3H,cAAc,CAACwB,wBAAwB,CAAC;EAC1D,IAAI;MACAoG,YAAY,EAAEC,qBAAqB;MACnCC,OAAO,EAAEC,oBAAoB;MAC7BC,YAAY,EAAEC;IAChB,CAAC,GAAGP,gBAAgB;IACpBQ,mBAAmB,GAAGtL,wBAAwB,CAAC8K,gBAAgB,EAAE/K,SAAS,CAAC;EAC7E,IAAIwL,uBAAuB,GAAG9G,yBAAyB,CAACwG,qBAAqB,EAAEH,gBAAgB,CAACjF,OAAO,CAAC;EACxG,IAAI2F,uBAAuB,GAAG9G,yBAAyB,CAAC2G,qBAAqB,CAAC;EAC9E,IAAII,kBAAkB,GAAGjH,yBAAyB,CAAC2G,oBAAoB,EAAEL,gBAAgB,CAACjF,OAAO,CAAC;EAClG,IAAIgB,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EACA,OAAO,aAAanE,KAAK,CAACiE,aAAa,CAACjE,KAAK,CAACgJ,QAAQ,EAAE,IAAI,EAAE7E,OAAO,CAACK,GAAG,CAAC,CAAC8C,KAAK,EAAE3J,CAAC,KAAK;IACtF,IAAI,CAAC2J,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAChE,UAAU,MAAM,CAAC,IAAI,CAACgE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC/D,QAAQ,MAAM,CAAC,IAAIY,OAAO,CAACnG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IACvL,IAAIiL,cAAc,GAAGhB,WAAW,IAAItI,MAAM,CAAChC,CAAC,CAAC,KAAK0K,WAAW;IAC7D,IAAIH,aAAa,GAAGG,WAAW,GAAGF,iBAAiB,GAAG,IAAI;IAC1D,IAAIe,aAAa,GAAGD,cAAc,GAAGhB,WAAW,GAAGC,aAAa;IAChE,IAAIiB,WAAW,GAAGvK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0I,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5DlD,MAAM,EAAEkD,KAAK,CAAClD,MAAM;MACpBgF,QAAQ,EAAE,CAAC,CAAC;MACZ,CAAC/G,8BAA8B,GAAG1E,CAAC;MACnC,CAACyE,gCAAgC,GAAGgG,gBAAgB,CAACjF;IACvD,CAAC,CAAC;IACF,OAAO,aAAanD,KAAK,CAACiE,aAAa,CAACrD,KAAK,EAAEf,QAAQ,CAAC;MACtDuJ,QAAQ,EAAE,CAAC,CAAC;MACZ3C,SAAS,EAAE;IACb,CAAC,EAAE7E,kBAAkB,CAACgH,mBAAmB,EAAEtB,KAAK,EAAE3J,CAAC,CAAC,EAAE;MACpD;MACA2K,YAAY,EAAEO,uBAAuB,CAACvB,KAAK,EAAE3J,CAAC;MAC9C;MAAA;;MAEA+K,YAAY,EAAEI,uBAAuB,CAACxB,KAAK,EAAE3J,CAAC;MAC9C;MAAA;;MAEA6K,OAAO,EAAEO,kBAAkB,CAACzB,KAAK,EAAE3J,CAAC;MACpC;MAAA;;MAEAmK,GAAG,EAAE,SAAS,CAACC,MAAM,CAACT,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAChE,UAAU,EAAE,GAAG,CAAC,CAACyE,MAAM,CAACT,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC/D,QAAQ,EAAE,GAAG,CAAC,CAACwE,MAAM,CAACT,KAAK,CAACC,QAAQ,EAAE,GAAG,CAAC,CAACQ,MAAM,CAACpK,CAAC;IAC3M,CAAC,CAAC,EAAE,aAAaqC,KAAK,CAACiE,aAAa,CAACpC,KAAK,EAAEhC,QAAQ,CAAC;MACnDyG,MAAM,EAAE4C,aAAa;MACrBG,QAAQ,EAAEJ,cAAc;MACxBK,SAAS,EAAE;IACb,CAAC,EAAEH,WAAW,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,EAAE,aAAanJ,KAAK,CAACiE,aAAa,CAAC4C,SAAS,EAAE;IAC9C1C,OAAO,EAAEA,OAAO;IAChBzB,KAAK,EAAE0F,gBAAgB;IACvBrB,UAAU,EAAEA;EACd,CAAC,CAAC,CAAC;AACL;AACA,OAAO,SAASwC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,IAAIC,qBAAqB;EACzB,IAAI;IACF3G,WAAW;IACX4G,aAAa;IACb9G,KAAK;IACL4C;EACF,CAAC,GAAGgE,KAAK;EACT,IAAI;IACF5F,YAAY;IACZN,UAAU;IACVC,QAAQ;IACRJ,OAAO;IACPH,OAAO;IACPC;EACF,CAAC,GAAGH,WAAW;EACf,IAAIU,QAAQ,GAAGsC,IAAI,CAACM,GAAG,CAACtD,WAAW,CAACU,QAAQ,CAAC;EAC7C,IAAI0C,UAAU,GAAGF,eAAe,CAAC1C,UAAU,EAAEC,QAAQ,CAAC;EACtD,IAAIoG,aAAa,GAAG7D,IAAI,CAACM,GAAG,CAACF,UAAU,CAAC;EACxC,IAAIzC,YAAY,GAAGiG,aAAa,CAAC1L,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAACyL,qBAAqB,GAAG3G,WAAW,CAACW,YAAY,MAAM,IAAI,IAAIgG,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC;EAC9K,IAAIG,gBAAgB,GAAGF,aAAa,CAACnL,MAAM,CAAC+I,KAAK,IAAI3F,iBAAiB,CAAC2F,KAAK,EAAEnE,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAACnF,MAAM;EACvG,IAAI6L,iBAAiB,GAAG,CAACF,aAAa,IAAI,GAAG,GAAGC,gBAAgB,GAAGA,gBAAgB,GAAG,CAAC,IAAInG,YAAY;EACvG,IAAIqG,cAAc,GAAGH,aAAa,GAAGC,gBAAgB,GAAGpG,QAAQ,GAAGqG,iBAAiB;EACpF,IAAIE,GAAG,GAAGL,aAAa,CAACM,MAAM,CAAC,CAACC,MAAM,EAAE3C,KAAK,KAAK;IAChD,IAAI4C,GAAG,GAAGvI,iBAAiB,CAAC2F,KAAK,EAAEnE,OAAO,EAAE,CAAC,CAAC;IAC9C,OAAO8G,MAAM,IAAI1I,QAAQ,CAAC2I,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;EAC3C,CAAC,EAAE,CAAC,CAAC;EACL,IAAI/F,OAAO;EACX,IAAI4F,GAAG,GAAG,CAAC,EAAE;IACX,IAAII,IAAI;IACRhG,OAAO,GAAGuF,aAAa,CAAClF,GAAG,CAAC,CAAC8C,KAAK,EAAE3J,CAAC,KAAK;MACxC,IAAIuM,GAAG,GAAGvI,iBAAiB,CAAC2F,KAAK,EAAEnE,OAAO,EAAE,CAAC,CAAC;MAC9C,IAAIJ,IAAI,GAAGpB,iBAAiB,CAAC2F,KAAK,EAAEtE,OAAO,EAAErF,CAAC,CAAC;MAC/C,IAAIyM,UAAU,GAAG9E,oBAAoB,CAACxC,WAAW,EAAE0C,MAAM,EAAE8B,KAAK,CAAC;MACjE,IAAI+C,OAAO,GAAG,CAAC9I,QAAQ,CAAC2I,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,IAAIH,GAAG;MAC7C,IAAIO,cAAc;MAClB,IAAIC,iBAAiB,GAAG3L,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0I,KAAK,CAAC,EAAE1E,KAAK,IAAIA,KAAK,CAACjF,CAAC,CAAC,IAAIiF,KAAK,CAACjF,CAAC,CAAC,CAAC+E,KAAK,CAAC;MACpG,IAAI/E,CAAC,EAAE;QACL2M,cAAc,GAAGH,IAAI,CAAC5G,QAAQ,GAAG/B,QAAQ,CAAC0E,UAAU,CAAC,GAAGzC,YAAY,IAAIyG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5F,CAAC,MAAM;QACLI,cAAc,GAAGhH,UAAU;MAC7B;MACA,IAAIkH,YAAY,GAAGF,cAAc,GAAG9I,QAAQ,CAAC0E,UAAU,CAAC,IAAI,CAACgE,GAAG,KAAK,CAAC,GAAG1G,QAAQ,GAAG,CAAC,IAAI6G,OAAO,GAAGP,cAAc,CAAC;MAClH,IAAIvC,QAAQ,GAAG,CAAC+C,cAAc,GAAGE,YAAY,IAAI,CAAC;MAClD,IAAIC,YAAY,GAAG,CAACL,UAAU,CAAC1G,WAAW,GAAG0G,UAAU,CAACzG,WAAW,IAAI,CAAC;MACxE,IAAIe,cAAc,GAAG,CAAC;QACpB;QACA3B,IAAI;QACJ;QACA3D,KAAK,EAAE8K,GAAG;QACVQ,OAAO,EAAEH,iBAAiB;QAC1BpH,OAAO;QACP2B,IAAI,EAAE7B;MACR,CAAC,CAAC;MACF,IAAI2B,eAAe,GAAGxD,gBAAgB,CAACgJ,UAAU,CAAChH,EAAE,EAAEgH,UAAU,CAAC/G,EAAE,EAAEoH,YAAY,EAAElD,QAAQ,CAAC;MAC5F4C,IAAI,GAAGvL,aAAa,CAACA,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkE,WAAW,CAACH,iBAAiB,CAAC,EAAE,CAAC,CAAC,EAAE;QACrG0H,OAAO;QACPzG,YAAY;QACZb,IAAI;QACJ2B,cAAc;QACd6C,QAAQ;QACRkD,YAAY;QACZ7F;MACF,CAAC,EAAE2F,iBAAiB,CAAC,EAAEH,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;QACtChL,KAAK,EAAEuC,iBAAiB,CAAC2F,KAAK,EAAEnE,OAAO,CAAC;QACxCG,UAAU,EAAEgH,cAAc;QAC1B/G,QAAQ,EAAEiH,YAAY;QACtBE,OAAO,EAAEH,iBAAiB;QAC1B9G,YAAY,EAAEjC,QAAQ,CAAC0E,UAAU,CAAC,GAAGzC;MACvC,CAAC,CAAC;MACF,OAAO0G,IAAI;IACb,CAAC,CAAC;EACJ;EACA,OAAOhG,OAAO;AAChB;AACA,SAASwG,oBAAoBA,CAACC,KAAK,EAAE;EACnC,IAAI;IACFlI,KAAK;IACLmI;EACF,CAAC,GAAGD,KAAK;EACT,IAAI;IACFzG,OAAO;IACP2G,iBAAiB;IACjBC,cAAc;IACdC,iBAAiB;IACjBC,eAAe;IACfhD,WAAW;IACXC,aAAa;IACbgD,gBAAgB;IAChBC;EACF,CAAC,GAAGzI,KAAK;EACT,IAAI0I,WAAW,GAAG9I,cAAc,CAACI,KAAK,EAAE,eAAe,CAAC;EACxD,IAAI2I,WAAW,GAAGR,kBAAkB,CAACS,OAAO;EAC5C,IAAI,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnL,QAAQ,CAAC,IAAI,CAAC;EAClD,IAAIoL,kBAAkB,GAAGvL,WAAW,CAAC,MAAM;IACzC,IAAI,OAAOiL,cAAc,KAAK,UAAU,EAAE;MACxCA,cAAc,CAAC,CAAC;IAClB;IACAK,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC,EAAE,CAACL,cAAc,CAAC,CAAC;EACpB,IAAIO,oBAAoB,GAAGxL,WAAW,CAAC,MAAM;IAC3C,IAAI,OAAOgL,gBAAgB,KAAK,UAAU,EAAE;MAC1CA,gBAAgB,CAAC,CAAC;IACpB;IACAM,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,CAACN,gBAAgB,CAAC,CAAC;EACtB,OAAO,aAAalL,KAAK,CAACiE,aAAa,CAACzB,OAAO,EAAE;IAC/CmJ,KAAK,EAAEZ,cAAc;IACrBa,QAAQ,EAAEZ,iBAAiB;IAC3B3B,QAAQ,EAAEyB,iBAAiB;IAC3Be,MAAM,EAAEZ,eAAe;IACvBa,IAAI,EAAE;MACJtO,CAAC,EAAE;IACL,CAAC;IACDuO,EAAE,EAAE;MACFvO,CAAC,EAAE;IACL,CAAC;IACD0N,gBAAgB,EAAEQ,oBAAoB;IACtCP,cAAc,EAAEM,kBAAkB;IAClC3D,GAAG,EAAEsD;EACP,CAAC,EAAEY,KAAK,IAAI;IACV,IAAI;MACFxO;IACF,CAAC,GAAGwO,KAAK;IACT,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIC,KAAK,GAAG/H,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;IACjC,IAAIgI,QAAQ,GAAGD,KAAK,CAAC5I,UAAU;IAC/Ba,OAAO,CAACrF,OAAO,CAAC,CAACwI,KAAK,EAAEI,KAAK,KAAK;MAChC,IAAIyC,IAAI,GAAGkB,WAAW,IAAIA,WAAW,CAAC3D,KAAK,CAAC;MAC5C,IAAIjE,YAAY,GAAGiE,KAAK,GAAG,CAAC,GAAGpH,GAAG,CAACgH,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC,GAAG,CAAC;MAChE,IAAI6C,IAAI,EAAE;QACR,IAAIiC,OAAO,GAAG9K,iBAAiB,CAAC6I,IAAI,CAAC5G,QAAQ,GAAG4G,IAAI,CAAC7G,UAAU,EAAEgE,KAAK,CAAC/D,QAAQ,GAAG+D,KAAK,CAAChE,UAAU,CAAC;QACnG,IAAI+I,MAAM,GAAGzN,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0I,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACvDhE,UAAU,EAAE6I,QAAQ,GAAG1I,YAAY;UACnCF,QAAQ,EAAE4I,QAAQ,GAAGC,OAAO,CAAC5O,CAAC,CAAC,GAAGiG;QACpC,CAAC,CAAC;QACFwI,QAAQ,CAACvN,IAAI,CAAC2N,MAAM,CAAC;QACrBF,QAAQ,GAAGE,MAAM,CAAC9I,QAAQ;MAC5B,CAAC,MAAM;QACL,IAAI;UACFA,QAAQ;UACRD;QACF,CAAC,GAAGgE,KAAK;QACT,IAAIgF,iBAAiB,GAAGhL,iBAAiB,CAAC,CAAC,EAAEiC,QAAQ,GAAGD,UAAU,CAAC;QACnE,IAAI4C,UAAU,GAAGoG,iBAAiB,CAAC9O,CAAC,CAAC;QACrC,IAAI+O,OAAO,GAAG3N,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE0I,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACxDhE,UAAU,EAAE6I,QAAQ,GAAG1I,YAAY;UACnCF,QAAQ,EAAE4I,QAAQ,GAAGjG,UAAU,GAAGzC;QACpC,CAAC,CAAC;QACFwI,QAAQ,CAACvN,IAAI,CAAC6N,OAAO,CAAC;QACtBJ,QAAQ,GAAGI,OAAO,CAAChJ,QAAQ;MAC7B;IACF,CAAC,CAAC;;IAEF;IACAsH,kBAAkB,CAACS,OAAO,GAAGW,QAAQ;IACrC,OAAO,aAAajM,KAAK,CAACiE,aAAa,CAACrD,KAAK,EAAE,IAAI,EAAE,aAAaZ,KAAK,CAACiE,aAAa,CAAC+D,UAAU,EAAE;MAChG7D,OAAO,EAAE8H,QAAQ;MACjBhE,WAAW,EAAEA,WAAW;MACxBC,aAAa,EAAEA,aAAa;MAC5BE,gBAAgB,EAAE1F,KAAK;MACvBqE,UAAU,EAAE,CAACwE;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ;AACA,SAASiB,aAAaA,CAAC9J,KAAK,EAAE;EAC5B,IAAI;IACFyB,OAAO;IACP2G,iBAAiB;IACjB7C,WAAW;IACXC;EACF,CAAC,GAAGxF,KAAK;EACT,IAAImI,kBAAkB,GAAGzK,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIiL,WAAW,GAAGR,kBAAkB,CAACS,OAAO;EAC5C,IAAIR,iBAAiB,IAAI3G,OAAO,IAAIA,OAAO,CAACnG,MAAM,KAAK,CAACqN,WAAW,IAAIA,WAAW,KAAKlH,OAAO,CAAC,EAAE;IAC/F,OAAO,aAAanE,KAAK,CAACiE,aAAa,CAAC0G,oBAAoB,EAAE;MAC5DjI,KAAK,EAAEA,KAAK;MACZmI,kBAAkB,EAAEA;IACtB,CAAC,CAAC;EACJ;EACA,OAAO,aAAa7K,KAAK,CAACiE,aAAa,CAAC+D,UAAU,EAAE;IAClD7D,OAAO,EAAEA,OAAO;IAChB8D,WAAW,EAAEA,WAAW;IACxBC,aAAa,EAAEA,aAAa;IAC5BE,gBAAgB,EAAE1F,KAAK;IACvBqE,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AACA,SAAS0F,gBAAgBA,CAAC/J,KAAK,EAAE;EAC/B,IAAI;IACF4B,IAAI;IACJmC,SAAS;IACTiG;EACF,CAAC,GAAGhK,KAAK;EACT,IAAIiK,UAAU,GAAGpM,IAAI,CAAC,cAAc,EAAEkG,SAAS,CAAC;EAChD,IAAInC,IAAI,EAAE;IACR,OAAO,IAAI;EACb;EACA,OAAO,aAAatE,KAAK,CAACiE,aAAa,CAACrD,KAAK,EAAE;IAC7CwI,QAAQ,EAAEsD,YAAY;IACtBjG,SAAS,EAAEkG;EACb,CAAC,EAAE,aAAa3M,KAAK,CAACiE,aAAa,CAACuI,aAAa,EAAE9J,KAAK,CAAC,CAAC;AAC5D;AACA,IAAIkK,eAAe,GAAG;EACpB7B,cAAc,EAAE,GAAG;EACnBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,MAAM;EACvB7H,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,KAAK;EACTF,OAAO,EAAE,OAAO;EAChBI,QAAQ,EAAE,GAAG;EACbO,IAAI,EAAE,SAAS;EACfQ,IAAI,EAAE,KAAK;EACXZ,WAAW,EAAE,CAAC;EACdoH,iBAAiB,EAAE,CAAC5J,MAAM,CAAC2L,KAAK;EAChC7F,SAAS,EAAE,IAAI;EACfnD,UAAU,EAAE,MAAM;EAClBL,QAAQ,EAAE,CAAC;EACXR,OAAO,EAAE,MAAM;EACfW,WAAW,EAAE,KAAK;EAClBF,YAAY,EAAE,CAAC;EACfiJ,YAAY,EAAE,CAAC;EACfpJ,UAAU,EAAE,CAAC;EACbc,MAAM,EAAE;AACV,CAAC;AACD,SAAS0I,OAAOA,CAACpK,KAAK,EAAE;EACtB,IAAIqK,iBAAiB,GAAGxK,mBAAmB,CAACG,KAAK,EAAEkK,eAAe,CAAC;EACnE,IAAIhK,KAAK,GAAGzC,OAAO,CAAC,MAAMc,aAAa,CAACyB,KAAK,CAACG,QAAQ,EAAE9B,IAAI,CAAC,EAAE,CAAC2B,KAAK,CAACG,QAAQ,CAAC,CAAC;EAChF,IAAIF,iBAAiB,GAAG3B,WAAW,CAAC+L,iBAAiB,EAAE,KAAK,CAAC;EAC7D,IAAIjK,WAAW,GAAG3C,OAAO,CAAC,OAAO;IAC/B4C,IAAI,EAAEgK,iBAAiB,CAAChK,IAAI;IAC5BC,OAAO,EAAE+J,iBAAiB,CAAC/J,OAAO;IAClCC,WAAW,EAAE8J,iBAAiB,CAAC9J,WAAW;IAC1CC,IAAI,EAAE6J,iBAAiB,CAAC7J,IAAI;IAC5BC,OAAO,EAAE4J,iBAAiB,CAAC5J,OAAO;IAClCC,EAAE,EAAE2J,iBAAiB,CAAC3J,EAAE;IACxBC,EAAE,EAAE0J,iBAAiB,CAAC1J,EAAE;IACxBC,UAAU,EAAEyJ,iBAAiB,CAACzJ,UAAU;IACxCC,QAAQ,EAAEwJ,iBAAiB,CAACxJ,QAAQ;IACpCC,QAAQ,EAAEuJ,iBAAiB,CAACvJ,QAAQ;IACpCC,YAAY,EAAEsJ,iBAAiB,CAACtJ,YAAY;IAC5CC,WAAW,EAAEqJ,iBAAiB,CAACrJ,WAAW;IAC1CC,WAAW,EAAEoJ,iBAAiB,CAACpJ,WAAW;IAC1CC,YAAY,EAAEmJ,iBAAiB,CAACnJ,YAAY;IAC5CC,UAAU,EAAEkJ,iBAAiB,CAAClJ,UAAU;IACxCC,IAAI,EAAEiJ,iBAAiB,CAACjJ,IAAI;IAC5BnB;EACF,CAAC,CAAC,EAAE,CAACoK,iBAAiB,CAACnJ,YAAY,EAAEmJ,iBAAiB,CAAC3J,EAAE,EAAE2J,iBAAiB,CAAC1J,EAAE,EAAE0J,iBAAiB,CAAC7J,IAAI,EAAE6J,iBAAiB,CAAC5J,OAAO,EAAE4J,iBAAiB,CAACxJ,QAAQ,EAAEwJ,iBAAiB,CAACrJ,WAAW,EAAEqJ,iBAAiB,CAACvJ,QAAQ,EAAEuJ,iBAAiB,CAAChK,IAAI,EAAEgK,iBAAiB,CAAC/J,OAAO,EAAE+J,iBAAiB,CAACpJ,WAAW,EAAEoJ,iBAAiB,CAACtJ,YAAY,EAAEsJ,iBAAiB,CAACzJ,UAAU,EAAEyJ,iBAAiB,CAAC9J,WAAW,EAAE8J,iBAAiB,CAAClJ,UAAU,EAAEkJ,iBAAiB,CAACjJ,IAAI,EAAEnB,iBAAiB,CAAC,CAAC;EACnd,IAAIwB,OAAO,GAAGzD,cAAc,CAACsD,KAAK,IAAIvD,gBAAgB,CAACuD,KAAK,EAAElB,WAAW,EAAEF,KAAK,CAAC,CAAC;EAClF,OAAO,aAAa5C,KAAK,CAACiE,aAAa,CAACjE,KAAK,CAACgJ,QAAQ,EAAE,IAAI,EAAE,aAAahJ,KAAK,CAACiE,aAAa,CAAChC,uBAAuB,EAAE;IACtH+K,EAAE,EAAE9I,uBAAuB;IAC3B+I,IAAI,EAAErO,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmO,iBAAiB,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5D5I;IACF,CAAC;EACH,CAAC,CAAC,EAAE,aAAanE,KAAK,CAACiE,aAAa,CAACwI,gBAAgB,EAAE5M,QAAQ,CAAC,CAAC,CAAC,EAAEkN,iBAAiB,EAAE;IACrF5I,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC,CAAC;AACN;AACA,OAAO,MAAM+I,GAAG,SAASjN,aAAa,CAAC;EACrCkN,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,GAAGtO,SAAS,CAAC;IACnBE,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE0C,QAAQ,CAAC,eAAe,CAAC,CAAC;EACxD;EACA2L,MAAMA,CAAA,EAAG;IACP,OAAO,aAAapN,KAAK,CAACiE,aAAa,CAACjE,KAAK,CAACgJ,QAAQ,EAAE,IAAI,EAAE,aAAahJ,KAAK,CAACiE,aAAa,CAACtD,qBAAqB,EAAE;MACpHuC,IAAI,EAAE,IAAI,CAACR,KAAK,CAACQ,IAAI;MACrBC,OAAO,EAAE,IAAI,CAACT,KAAK,CAACS,OAAO;MAC3BmB,IAAI,EAAE,IAAI,CAAC5B,KAAK,CAAC4B,IAAI;MACrB+I,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,OAAO,EAAEC,SAAS;MAClBC,OAAO,EAAED,SAAS;MAClB1I,IAAI,EAAE;IACR,CAAC,CAAC,EAAE,aAAa9E,KAAK,CAACiE,aAAa,CAACxB,mBAAmB,EAAE,IAAI,CAACC,KAAK,CAAC,EAAE,aAAa1C,KAAK,CAACiE,aAAa,CAAC6I,OAAO,EAAE,IAAI,CAACpK,KAAK,CAAC,EAAE,IAAI,CAACA,KAAK,CAACG,QAAQ,CAAC;EACpJ;AACF;AACA9D,eAAe,CAACmO,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC;AAC1CnO,eAAe,CAACmO,GAAG,EAAE,cAAc,EAAEN,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}