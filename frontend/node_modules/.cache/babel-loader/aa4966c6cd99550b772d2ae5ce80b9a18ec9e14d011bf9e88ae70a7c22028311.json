{"ast": null, "code": "var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nexport var placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflow\n  }\n};\nexport var placementsRtl = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow\n  },\n  rightTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  rightBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  leftTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  leftBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflow\n  }\n};\nexport default placements;", "map": {"version": 3, "names": ["autoAdjustOverflow", "adjustX", "adjustY", "placements", "topLeft", "points", "overflow", "topRight", "bottomLeft", "bottomRight", "leftTop", "leftBottom", "rightTop", "rightBottom", "placementsRtl"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-menu/es/placements.js"], "sourcesContent": ["var autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nexport var placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflow\n  }\n};\nexport var placementsRtl = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflow\n  },\n  rightTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow\n  },\n  rightBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflow\n  },\n  leftTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow\n  },\n  leftBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflow\n  }\n};\nexport default placements;"], "mappings": "AAAA,IAAIA,kBAAkB,GAAG;EACvBC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG;EACtBC,OAAO,EAAE;IACPC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDO,QAAQ,EAAE;IACRF,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDQ,UAAU,EAAE;IACVH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDS,WAAW,EAAE;IACXJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDU,OAAO,EAAE;IACPL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDW,UAAU,EAAE;IACVN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDY,QAAQ,EAAE;IACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDa,WAAW,EAAE;IACXR,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ;AACF,CAAC;AACD,OAAO,IAAIc,aAAa,GAAG;EACzBV,OAAO,EAAE;IACPC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDO,QAAQ,EAAE;IACRF,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDQ,UAAU,EAAE;IACVH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDS,WAAW,EAAE;IACXJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDY,QAAQ,EAAE;IACRP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDa,WAAW,EAAE;IACXR,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDU,OAAO,EAAE;IACPL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ,CAAC;EACDW,UAAU,EAAE;IACVN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEN;EACZ;AACF,CAAC;AACD,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}