{"ast": null, "code": "/**\n * Computes the chart coordinates from the mouse event.\n *\n * The coordinates are relative to the top-left corner of the chart,\n * where the top-left corner of the chart is (0, 0).\n * Moving right, the x-coordinate increases, and moving down, the y-coordinate increases.\n *\n * The coordinates are rounded to the nearest integer and are including a CSS transform scale.\n * So a chart that's scaled will return the same coordinates as a chart that's not scaled.\n *\n * @param event The mouse event from React event handlers\n * @return chartPointer The chart coordinates relative to the top-left corner of the chart\n */\nexport var getChartPointer = event => {\n  var rect = event.currentTarget.getBoundingClientRect();\n  var scaleX = rect.width / event.currentTarget.offsetWidth;\n  var scaleY = rect.height / event.currentTarget.offsetHeight;\n  return {\n    /*\n     * Here it's important to use:\n     * - event.clientX and event.clientY to get the mouse position relative to the viewport, including scroll.\n     * - pageX and pageY are not used because they are relative to the whole document, and ignore scroll.\n     * - rect.left and rect.top are used to get the position of the chart relative to the viewport.\n     * - offsetX and offsetY are not used because they are relative to the offset parent\n     *  which may or may not be the same as the clientX and clientY, depending on the position of the chart in the DOM\n     *  and surrounding element styles. CSS position: relative, absolute, fixed, will change the offset parent.\n     * - scaleX and scaleY are necessary for when the chart element is scaled using CSS `transform: scale(N)`.\n     */\n    chartX: Math.round((event.clientX - rect.left) / scaleX),\n    chartY: Math.round((event.clientY - rect.top) / scaleY)\n  };\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "rect", "currentTarget", "getBoundingClientRect", "scaleX", "width", "offsetWidth", "scaleY", "height", "offsetHeight", "chartX", "Math", "round", "clientX", "left", "chartY", "clientY", "top"], "sources": ["/home/<USER>/itai/frontend/node_modules/recharts/es6/util/getChartPointer.js"], "sourcesContent": ["/**\n * Computes the chart coordinates from the mouse event.\n *\n * The coordinates are relative to the top-left corner of the chart,\n * where the top-left corner of the chart is (0, 0).\n * Moving right, the x-coordinate increases, and moving down, the y-coordinate increases.\n *\n * The coordinates are rounded to the nearest integer and are including a CSS transform scale.\n * So a chart that's scaled will return the same coordinates as a chart that's not scaled.\n *\n * @param event The mouse event from React event handlers\n * @return chartPointer The chart coordinates relative to the top-left corner of the chart\n */\nexport var getChartPointer = event => {\n  var rect = event.currentTarget.getBoundingClientRect();\n  var scaleX = rect.width / event.currentTarget.offsetWidth;\n  var scaleY = rect.height / event.currentTarget.offsetHeight;\n  return {\n    /*\n     * Here it's important to use:\n     * - event.clientX and event.clientY to get the mouse position relative to the viewport, including scroll.\n     * - pageX and pageY are not used because they are relative to the whole document, and ignore scroll.\n     * - rect.left and rect.top are used to get the position of the chart relative to the viewport.\n     * - offsetX and offsetY are not used because they are relative to the offset parent\n     *  which may or may not be the same as the clientX and clientY, depending on the position of the chart in the DOM\n     *  and surrounding element styles. CSS position: relative, absolute, fixed, will change the offset parent.\n     * - scaleX and scaleY are necessary for when the chart element is scaled using CSS `transform: scale(N)`.\n     */\n    chartX: Math.round((event.clientX - rect.left) / scaleX),\n    chartY: Math.round((event.clientY - rect.top) / scaleY)\n  };\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,eAAe,GAAGC,KAAK,IAAI;EACpC,IAAIC,IAAI,GAAGD,KAAK,CAACE,aAAa,CAACC,qBAAqB,CAAC,CAAC;EACtD,IAAIC,MAAM,GAAGH,IAAI,CAACI,KAAK,GAAGL,KAAK,CAACE,aAAa,CAACI,WAAW;EACzD,IAAIC,MAAM,GAAGN,IAAI,CAACO,MAAM,GAAGR,KAAK,CAACE,aAAa,CAACO,YAAY;EAC3D,OAAO;IACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,MAAM,EAAEC,IAAI,CAACC,KAAK,CAAC,CAACZ,KAAK,CAACa,OAAO,GAAGZ,IAAI,CAACa,IAAI,IAAIV,MAAM,CAAC;IACxDW,MAAM,EAAEJ,IAAI,CAACC,KAAK,CAAC,CAACZ,KAAK,CAACgB,OAAO,GAAGf,IAAI,CAACgB,GAAG,IAAIV,MAAM;EACxD,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}