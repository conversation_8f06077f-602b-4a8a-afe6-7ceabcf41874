{"ast": null, "code": "/**\n * API服务层\n */\nimport axios from 'axios';\nconst API_BASE_URL = 'http://localhost:8000';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 可以在这里添加认证token\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  console.error('API Error:', error);\n  // 如果是网络错误或服务器错误，返回模拟数据\n  if (error.code === 'ECONNREFUSED' || ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) >= 500) {\n    console.warn('API服务不可用，使用模拟数据');\n    return Promise.resolve(getMockData(error.config));\n  }\n  return Promise.reject(error);\n});\n\n// 模拟数据函数\nfunction getMockData(config) {\n  const url = config.url;\n  if (url.includes('/api/v1/servers') && config.method === 'get') {\n    return [{\n      id: 1,\n      name: '测试服务器1',\n      host: '*************',\n      port: 22,\n      username: 'root',\n      auth_type: 'password',\n      description: '测试用服务器',\n      tags: ['test', 'development'],\n      monitoring_enabled: true,\n      alert_enabled: true,\n      is_active: true,\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-01T00:00:00Z'\n    }];\n  }\n  if (url.includes('/api/v1/scripts') && config.method === 'get' && !url.includes('executions')) {\n    return [{\n      id: 1,\n      name: '系统信息收集',\n      description: '收集服务器基本信息',\n      script_type: 'shell',\n      category: '系统管理',\n      tags: ['system', 'info'],\n      content: '#!/bin/bash\\nuname -a\\ndf -h\\nfree -m',\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-01T00:00:00Z'\n    }];\n  }\n  if (url.includes('/api/v1/scripts/executions')) {\n    return [{\n      id: 1,\n      script_name: '系统信息收集',\n      server_name: '测试服务器1',\n      status: 'success',\n      exit_code: 0,\n      duration: 5,\n      started_at: '2024-01-01T10:00:00Z',\n      finished_at: '2024-01-01T10:00:05Z'\n    }];\n  }\n\n  // Docker API 模拟数据\n  if (url.includes('/api/v1/docker') && url.includes('/images')) {\n    return [{\n      repository: 'nginx',\n      tag: 'latest',\n      image_id: 'sha256:abcd1234',\n      created: '2024-01-01 00:00:00',\n      size: '133MB'\n    }, {\n      repository: 'mysql',\n      tag: '8.0',\n      image_id: 'sha256:efgh5678',\n      created: '2024-01-01 00:00:00',\n      size: '521MB'\n    }];\n  }\n  if (url.includes('/api/v1/docker') && url.includes('/containers')) {\n    return [{\n      container_id: 'abcd1234efgh',\n      name: 'web-server',\n      image: 'nginx:latest',\n      status: 'Up 2 hours',\n      ports: '0.0.0.0:80->80/tcp',\n      created: '2024-01-01 00:00:00'\n    }, {\n      container_id: 'ijkl5678mnop',\n      name: 'database',\n      image: 'mysql:8.0',\n      status: 'Up 1 day',\n      ports: '0.0.0.0:3306->3306/tcp',\n      created: '2024-01-01 00:00:00'\n    }];\n  }\n  if (url.includes('/api/v1/docker') && url.includes('/gpu-stats')) {\n    return [{\n      gpu_id: 0,\n      name: 'NVIDIA GeForce RTX 4090',\n      utilization: 75,\n      memory_used: 12000,\n      memory_total: 24000\n    }];\n  }\n\n  // 监控API模拟数据\n  if (url.includes('/api/v1/monitoring/servers')) {\n    return [{\n      id: 1,\n      name: '测试服务器1',\n      status: 'online',\n      cpu_usage: 25.5,\n      memory_usage: 68.2,\n      disk_usage: 45.8,\n      last_update: new Date().toISOString()\n    }, {\n      id: 2,\n      name: '生产服务器1',\n      status: 'warning',\n      cpu_usage: 85.2,\n      memory_usage: 92.1,\n      disk_usage: 78.5,\n      last_update: new Date().toISOString()\n    }];\n  }\n  if (url.includes('/api/v1/monitoring/alerts')) {\n    return [{\n      id: 1,\n      server_name: '生产服务器1',\n      alert_type: 'cpu_high',\n      severity: 'high',\n      message: 'CPU使用率过高: 85.2%',\n      created_at: new Date().toISOString(),\n      status: 'active'\n    }, {\n      id: 2,\n      server_name: '生产服务器1',\n      alert_type: 'memory_high',\n      severity: 'critical',\n      message: '内存使用率过高: 92.1%',\n      created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      status: 'active'\n    }];\n  }\n  if (url.includes('/test-connection')) {\n    return {\n      success: true,\n      message: '连接成功',\n      latency: 50\n    };\n  }\n  if (url.includes('/execute')) {\n    return {\n      success: true,\n      exit_code: 0,\n      stdout: 'Linux testserver 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux\\nFilesystem      Size  Used Avail Use% Mounted on\\n/dev/sda1        20G  8.5G   11G  45% /\\ntmpfs           2.0G     0  2.0G   0% /dev/shm',\n      stderr: '',\n      duration: 2.5\n    };\n  }\n  if (url.includes('/logs')) {\n    return {\n      logs: '2024-01-01 00:00:00 [INFO] Container started\\n2024-01-01 00:00:01 [INFO] Application ready\\n2024-01-01 00:00:02 [INFO] Listening on port 80'\n    };\n  }\n  return {\n    success: true,\n    message: '操作成功'\n  };\n}\n\n// 服务器管理API\nexport const serverApi = {\n  // 获取服务器列表\n  getServers: () => api.get('/api/v1/servers').then(response => response.data),\n  // 获取服务器详情\n  getServer: id => api.get(`/api/v1/servers/${id}`).then(response => response.data),\n  // 创建服务器\n  createServer: data => api.post('/api/v1/servers', data).then(response => response.data),\n  // 更新服务器\n  updateServer: (id, data) => api.put(`/api/v1/servers/${id}`, data).then(response => response.data),\n  // 删除服务器\n  deleteServer: id => api.delete(`/api/v1/servers/${id}`).then(response => response.data),\n  // 测试连接\n  testConnection: id => api.post(`/api/v1/servers/${id}/test-connection`).then(response => response.data),\n  // 执行命令\n  executeCommand: (id, data) => api.post(`/api/v1/servers/${id}/execute`, data).then(response => response.data),\n  // 开始监控\n  startMonitoring: id => api.post(`/api/v1/servers/${id}/start-monitoring`).then(response => response.data),\n  // 停止监控\n  stopMonitoring: id => api.post(`/api/v1/servers/${id}/stop-monitoring`).then(response => response.data)\n};\n\n// Docker管理API\nexport const dockerApi = {\n  // 获取镜像列表\n  getImages: serverId => api.get(`/api/v1/docker/${serverId}/images`).then(response => response.data),\n  // 拉取镜像\n  pullImage: (serverId, imageName) => api.post(`/api/v1/docker/${serverId}/images/pull?image_name=${imageName}`).then(response => response.data),\n  // 删除镜像\n  deleteImage: (serverId, imageId, force = false) => api.delete(`/api/v1/docker/${serverId}/images/${imageId}?force=${force}`).then(response => response.data),\n  // 获取容器列表\n  getContainers: (serverId, all = true) => api.get(`/api/v1/docker/${serverId}/containers?all_containers=${all}`).then(response => response.data),\n  // 创建容器\n  createContainer: (serverId, config) => api.post(`/api/v1/docker/${serverId}/containers`, config).then(response => response.data),\n  // 启动容器\n  startContainer: (serverId, containerId) => api.post(`/api/v1/docker/${serverId}/containers/${containerId}/start`).then(response => response.data),\n  // 停止容器\n  stopContainer: (serverId, containerId, timeout = 10) => api.post(`/api/v1/docker/${serverId}/containers/${containerId}/stop?timeout=${timeout}`).then(response => response.data),\n  // 重启容器\n  restartContainer: (serverId, containerId) => api.post(`/api/v1/docker/${serverId}/containers/${containerId}/restart`).then(response => response.data),\n  // 删除容器\n  deleteContainer: (serverId, containerId, force = false) => api.delete(`/api/v1/docker/${serverId}/containers/${containerId}?force=${force}`).then(response => response.data),\n  // 获取容器日志\n  getContainerLogs: (serverId, containerId, lines = 100) => api.get(`/api/v1/docker/${serverId}/containers/${containerId}/logs?lines=${lines}`).then(response => response.data),\n  // 获取容器统计\n  getContainerStats: (serverId, containerId) => api.get(`/api/v1/docker/${serverId}/containers/${containerId}/stats`).then(response => response.data),\n  // 获取GPU统计\n  getGpuStats: serverId => api.get(`/api/v1/docker/${serverId}/gpu-stats`).then(response => response.data)\n};\n\n// 脚本管理API\nexport const scriptApi = {\n  // 获取脚本列表\n  getScripts: () => api.get('/api/v1/scripts').then(response => response.data),\n  // 获取脚本详情\n  getScript: id => api.get(`/api/v1/scripts/${id}`).then(response => response.data),\n  // 创建脚本\n  createScript: data => api.post('/api/v1/scripts', data).then(response => response.data),\n  // 更新脚本\n  updateScript: (id, data) => api.put(`/api/v1/scripts/${id}`, data).then(response => response.data),\n  // 删除脚本\n  deleteScript: id => api.delete(`/api/v1/scripts/${id}`).then(response => response.data),\n  // 执行脚本\n  executeScript: (id, data) => api.post(`/api/v1/scripts/${id}/execute`, data).then(response => response.data),\n  // 获取执行记录\n  getExecutions: () => api.get('/api/v1/scripts/executions').then(response => response.data),\n  // 获取执行详情\n  getExecution: id => api.get(`/api/v1/scripts/executions/${id}`).then(response => response.data)\n};\n\n// 文件管理API\nexport const fileApi = {\n  // 上传文件\n  uploadFile: (serverId, formData) => api.post(`/api/v1/files/${serverId}/upload`, formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }).then(response => response.data),\n  // 下载文件\n  downloadFile: (serverId, remotePath) => api.get(`/api/v1/files/${serverId}/download?remote_path=${remotePath}`, {\n    responseType: 'blob'\n  }),\n  // 列出文件\n  listFiles: (serverId, remotePath = '/') => api.get(`/api/v1/files/${serverId}/list?remote_path=${remotePath}`).then(response => response.data),\n  // 删除文件\n  deleteFile: (serverId, remotePath) => api.delete(`/api/v1/files/${serverId}/delete?remote_path=${remotePath}`).then(response => response.data),\n  // 创建目录\n  createDirectory: (serverId, remotePath) => api.post(`/api/v1/files/${serverId}/mkdir?remote_path=${remotePath}`).then(response => response.data),\n  // 获取文件内容\n  getFileContent: (serverId, remotePath) => api.get(`/api/v1/files/${serverId}/content?remote_path=${remotePath}`).then(response => response.data),\n  // 保存文件内容\n  saveFileContent: (serverId, remotePath, content) => api.post(`/api/v1/files/${serverId}/content`, {\n    remote_path: remotePath,\n    content\n  }).then(response => response.data)\n};\n\n// 监控API\nexport const monitoringApi = {\n  // 获取服务器监控数据\n  getServerMonitoring: () => api.get('/api/v1/monitoring/servers'),\n  // 获取服务器详细监控\n  getServerStats: serverId => api.get(`/api/v1/monitoring/servers/${serverId}/stats`),\n  // 获取历史数据\n  getHistoryData: (serverId, timeRange) => api.get(`/api/v1/monitoring/servers/${serverId}/history?range=${timeRange}`),\n  // 获取告警列表\n  getAlerts: () => api.get('/api/v1/monitoring/alerts'),\n  // 确认告警\n  acknowledgeAlert: alertId => api.post(`/api/v1/monitoring/alerts/${alertId}/acknowledge`),\n  // 解决告警\n  resolveAlert: alertId => api.post(`/api/v1/monitoring/alerts/${alertId}/resolve`)\n};\n\n// 健康检查API\nexport const healthApi = {\n  // 系统健康检查\n  getHealth: () => api.get('/health'),\n  // 获取系统信息\n  getSystemInfo: () => api.get('/')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "data", "_error$response", "console", "code", "status", "warn", "resolve", "getMockData", "url", "includes", "method", "id", "name", "host", "port", "username", "auth_type", "description", "tags", "monitoring_enabled", "alert_enabled", "is_active", "created_at", "updated_at", "script_type", "category", "content", "script_name", "server_name", "exit_code", "duration", "started_at", "finished_at", "repository", "tag", "image_id", "created", "size", "container_id", "image", "ports", "gpu_id", "utilization", "memory_used", "memory_total", "cpu_usage", "memory_usage", "disk_usage", "last_update", "Date", "toISOString", "alert_type", "severity", "message", "now", "success", "latency", "stdout", "stderr", "logs", "serverApi", "getServers", "get", "then", "getServer", "createServer", "post", "updateServer", "put", "deleteServer", "delete", "testConnection", "executeCommand", "startMonitoring", "stopMonitoring", "docker<PERSON><PERSON>", "getImages", "serverId", "pullImage", "imageName", "deleteImage", "imageId", "force", "getContainers", "all", "createContainer", "startContainer", "containerId", "stopContainer", "restartContainer", "deleteContainer", "getContainerLogs", "lines", "getContainerStats", "getGpuStats", "script<PERSON><PERSON>", "getScripts", "getScript", "createScript", "updateScript", "deleteScript", "executeScript", "getExecutions", "getExecution", "fileApi", "uploadFile", "formData", "downloadFile", "remotePath", "responseType", "listFiles", "deleteFile", "createDirectory", "getFileContent", "saveFileContent", "remote_path", "monitoringApi", "getServerMonitoring", "getServerStats", "getHistoryData", "timeRange", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "<PERSON><PERSON><PERSON><PERSON>", "healthApi", "getHealth", "getSystemInfo"], "sources": ["/home/<USER>/itai/frontend/src/services/api.ts"], "sourcesContent": ["/**\n * API服务层\n */\nimport axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:8000';\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 可以在这里添加认证token\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    console.error('API Error:', error);\n    // 如果是网络错误或服务器错误，返回模拟数据\n    if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {\n      console.warn('API服务不可用，使用模拟数据');\n      return Promise.resolve(getMockData(error.config));\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 模拟数据函数\nfunction getMockData(config: any) {\n  const url = config.url;\n\n  if (url.includes('/api/v1/servers') && config.method === 'get') {\n    return [\n      {\n        id: 1,\n        name: '测试服务器1',\n        host: '*************',\n        port: 22,\n        username: 'root',\n        auth_type: 'password',\n        description: '测试用服务器',\n        tags: ['test', 'development'],\n        monitoring_enabled: true,\n        alert_enabled: true,\n        is_active: true,\n        created_at: '2024-01-01T00:00:00Z',\n        updated_at: '2024-01-01T00:00:00Z'\n      }\n    ];\n  }\n\n  if (url.includes('/api/v1/scripts') && config.method === 'get' && !url.includes('executions')) {\n    return [\n      {\n        id: 1,\n        name: '系统信息收集',\n        description: '收集服务器基本信息',\n        script_type: 'shell',\n        category: '系统管理',\n        tags: ['system', 'info'],\n        content: '#!/bin/bash\\nuname -a\\ndf -h\\nfree -m',\n        created_at: '2024-01-01T00:00:00Z',\n        updated_at: '2024-01-01T00:00:00Z'\n      }\n    ];\n  }\n\n  if (url.includes('/api/v1/scripts/executions')) {\n    return [\n      {\n        id: 1,\n        script_name: '系统信息收集',\n        server_name: '测试服务器1',\n        status: 'success',\n        exit_code: 0,\n        duration: 5,\n        started_at: '2024-01-01T10:00:00Z',\n        finished_at: '2024-01-01T10:00:05Z'\n      }\n    ];\n  }\n\n  // Docker API 模拟数据\n  if (url.includes('/api/v1/docker') && url.includes('/images')) {\n    return [\n      {\n        repository: 'nginx',\n        tag: 'latest',\n        image_id: 'sha256:abcd1234',\n        created: '2024-01-01 00:00:00',\n        size: '133MB'\n      },\n      {\n        repository: 'mysql',\n        tag: '8.0',\n        image_id: 'sha256:efgh5678',\n        created: '2024-01-01 00:00:00',\n        size: '521MB'\n      }\n    ];\n  }\n\n  if (url.includes('/api/v1/docker') && url.includes('/containers')) {\n    return [\n      {\n        container_id: 'abcd1234efgh',\n        name: 'web-server',\n        image: 'nginx:latest',\n        status: 'Up 2 hours',\n        ports: '0.0.0.0:80->80/tcp',\n        created: '2024-01-01 00:00:00'\n      },\n      {\n        container_id: 'ijkl5678mnop',\n        name: 'database',\n        image: 'mysql:8.0',\n        status: 'Up 1 day',\n        ports: '0.0.0.0:3306->3306/tcp',\n        created: '2024-01-01 00:00:00'\n      }\n    ];\n  }\n\n  if (url.includes('/api/v1/docker') && url.includes('/gpu-stats')) {\n    return [\n      {\n        gpu_id: 0,\n        name: 'NVIDIA GeForce RTX 4090',\n        utilization: 75,\n        memory_used: 12000,\n        memory_total: 24000\n      }\n    ];\n  }\n\n  // 监控API模拟数据\n  if (url.includes('/api/v1/monitoring/servers')) {\n    return [\n      {\n        id: 1,\n        name: '测试服务器1',\n        status: 'online',\n        cpu_usage: 25.5,\n        memory_usage: 68.2,\n        disk_usage: 45.8,\n        last_update: new Date().toISOString()\n      },\n      {\n        id: 2,\n        name: '生产服务器1',\n        status: 'warning',\n        cpu_usage: 85.2,\n        memory_usage: 92.1,\n        disk_usage: 78.5,\n        last_update: new Date().toISOString()\n      }\n    ];\n  }\n\n  if (url.includes('/api/v1/monitoring/alerts')) {\n    return [\n      {\n        id: 1,\n        server_name: '生产服务器1',\n        alert_type: 'cpu_high',\n        severity: 'high',\n        message: 'CPU使用率过高: 85.2%',\n        created_at: new Date().toISOString(),\n        status: 'active'\n      },\n      {\n        id: 2,\n        server_name: '生产服务器1',\n        alert_type: 'memory_high',\n        severity: 'critical',\n        message: '内存使用率过高: 92.1%',\n        created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n        status: 'active'\n      }\n    ];\n  }\n\n  if (url.includes('/test-connection')) {\n    return { success: true, message: '连接成功', latency: 50 };\n  }\n\n  if (url.includes('/execute')) {\n    return {\n      success: true,\n      exit_code: 0,\n      stdout: 'Linux testserver 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux\\nFilesystem      Size  Used Avail Use% Mounted on\\n/dev/sda1        20G  8.5G   11G  45% /\\ntmpfs           2.0G     0  2.0G   0% /dev/shm',\n      stderr: '',\n      duration: 2.5\n    };\n  }\n\n  if (url.includes('/logs')) {\n    return {\n      logs: '2024-01-01 00:00:00 [INFO] Container started\\n2024-01-01 00:00:01 [INFO] Application ready\\n2024-01-01 00:00:02 [INFO] Listening on port 80'\n    };\n  }\n\n  return { success: true, message: '操作成功' };\n}\n\n// 服务器管理API\nexport const serverApi = {\n  // 获取服务器列表\n  getServers: () => api.get('/api/v1/servers').then(response => response.data),\n\n  // 获取服务器详情\n  getServer: (id: number) => api.get(`/api/v1/servers/${id}`).then(response => response.data),\n\n  // 创建服务器\n  createServer: (data: any) => api.post('/api/v1/servers', data).then(response => response.data),\n\n  // 更新服务器\n  updateServer: (id: number, data: any) => api.put(`/api/v1/servers/${id}`, data).then(response => response.data),\n\n  // 删除服务器\n  deleteServer: (id: number) => api.delete(`/api/v1/servers/${id}`).then(response => response.data),\n\n  // 测试连接\n  testConnection: (id: number) => api.post(`/api/v1/servers/${id}/test-connection`).then(response => response.data),\n\n  // 执行命令\n  executeCommand: (id: number, data: any) => api.post(`/api/v1/servers/${id}/execute`, data).then(response => response.data),\n\n  // 开始监控\n  startMonitoring: (id: number) => api.post(`/api/v1/servers/${id}/start-monitoring`).then(response => response.data),\n\n  // 停止监控\n  stopMonitoring: (id: number) => api.post(`/api/v1/servers/${id}/stop-monitoring`).then(response => response.data),\n};\n\n// Docker管理API\nexport const dockerApi = {\n  // 获取镜像列表\n  getImages: (serverId: number) => api.get(`/api/v1/docker/${serverId}/images`).then(response => response.data),\n\n  // 拉取镜像\n  pullImage: (serverId: number, imageName: string) =>\n    api.post(`/api/v1/docker/${serverId}/images/pull?image_name=${imageName}`).then(response => response.data),\n\n  // 删除镜像\n  deleteImage: (serverId: number, imageId: string, force = false) =>\n    api.delete(`/api/v1/docker/${serverId}/images/${imageId}?force=${force}`).then(response => response.data),\n\n  // 获取容器列表\n  getContainers: (serverId: number, all = true) =>\n    api.get(`/api/v1/docker/${serverId}/containers?all_containers=${all}`).then(response => response.data),\n\n  // 创建容器\n  createContainer: (serverId: number, config: any) =>\n    api.post(`/api/v1/docker/${serverId}/containers`, config).then(response => response.data),\n\n  // 启动容器\n  startContainer: (serverId: number, containerId: string) =>\n    api.post(`/api/v1/docker/${serverId}/containers/${containerId}/start`).then(response => response.data),\n\n  // 停止容器\n  stopContainer: (serverId: number, containerId: string, timeout = 10) =>\n    api.post(`/api/v1/docker/${serverId}/containers/${containerId}/stop?timeout=${timeout}`).then(response => response.data),\n\n  // 重启容器\n  restartContainer: (serverId: number, containerId: string) =>\n    api.post(`/api/v1/docker/${serverId}/containers/${containerId}/restart`).then(response => response.data),\n\n  // 删除容器\n  deleteContainer: (serverId: number, containerId: string, force = false) =>\n    api.delete(`/api/v1/docker/${serverId}/containers/${containerId}?force=${force}`).then(response => response.data),\n\n  // 获取容器日志\n  getContainerLogs: (serverId: number, containerId: string, lines = 100) =>\n    api.get(`/api/v1/docker/${serverId}/containers/${containerId}/logs?lines=${lines}`).then(response => response.data),\n\n  // 获取容器统计\n  getContainerStats: (serverId: number, containerId: string) =>\n    api.get(`/api/v1/docker/${serverId}/containers/${containerId}/stats`).then(response => response.data),\n\n  // 获取GPU统计\n  getGpuStats: (serverId: number) =>\n    api.get(`/api/v1/docker/${serverId}/gpu-stats`).then(response => response.data),\n};\n\n// 脚本管理API\nexport const scriptApi = {\n  // 获取脚本列表\n  getScripts: () => api.get('/api/v1/scripts').then(response => response.data),\n\n  // 获取脚本详情\n  getScript: (id: number) => api.get(`/api/v1/scripts/${id}`).then(response => response.data),\n\n  // 创建脚本\n  createScript: (data: any) => api.post('/api/v1/scripts', data).then(response => response.data),\n\n  // 更新脚本\n  updateScript: (id: number, data: any) => api.put(`/api/v1/scripts/${id}`, data).then(response => response.data),\n\n  // 删除脚本\n  deleteScript: (id: number) => api.delete(`/api/v1/scripts/${id}`).then(response => response.data),\n\n  // 执行脚本\n  executeScript: (id: number, data: any) => api.post(`/api/v1/scripts/${id}/execute`, data).then(response => response.data),\n\n  // 获取执行记录\n  getExecutions: () => api.get('/api/v1/scripts/executions').then(response => response.data),\n\n  // 获取执行详情\n  getExecution: (id: number) => api.get(`/api/v1/scripts/executions/${id}`).then(response => response.data),\n};\n\n// 文件管理API\nexport const fileApi = {\n  // 上传文件\n  uploadFile: (serverId: number, formData: FormData) =>\n    api.post(`/api/v1/files/${serverId}/upload`, formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    }).then(response => response.data),\n\n  // 下载文件\n  downloadFile: (serverId: number, remotePath: string) =>\n    api.get(`/api/v1/files/${serverId}/download?remote_path=${remotePath}`, {\n      responseType: 'blob'\n    }),\n\n  // 列出文件\n  listFiles: (serverId: number, remotePath = '/') =>\n    api.get(`/api/v1/files/${serverId}/list?remote_path=${remotePath}`).then(response => response.data),\n\n  // 删除文件\n  deleteFile: (serverId: number, remotePath: string) =>\n    api.delete(`/api/v1/files/${serverId}/delete?remote_path=${remotePath}`).then(response => response.data),\n\n  // 创建目录\n  createDirectory: (serverId: number, remotePath: string) =>\n    api.post(`/api/v1/files/${serverId}/mkdir?remote_path=${remotePath}`).then(response => response.data),\n\n  // 获取文件内容\n  getFileContent: (serverId: number, remotePath: string) =>\n    api.get(`/api/v1/files/${serverId}/content?remote_path=${remotePath}`).then(response => response.data),\n\n  // 保存文件内容\n  saveFileContent: (serverId: number, remotePath: string, content: string) =>\n    api.post(`/api/v1/files/${serverId}/content`, { remote_path: remotePath, content }).then(response => response.data),\n};\n\n// 监控API\nexport const monitoringApi = {\n  // 获取服务器监控数据\n  getServerMonitoring: () => api.get('/api/v1/monitoring/servers'),\n  \n  // 获取服务器详细监控\n  getServerStats: (serverId: number) => api.get(`/api/v1/monitoring/servers/${serverId}/stats`),\n  \n  // 获取历史数据\n  getHistoryData: (serverId: number, timeRange: string) => \n    api.get(`/api/v1/monitoring/servers/${serverId}/history?range=${timeRange}`),\n  \n  // 获取告警列表\n  getAlerts: () => api.get('/api/v1/monitoring/alerts'),\n  \n  // 确认告警\n  acknowledgeAlert: (alertId: number) => \n    api.post(`/api/v1/monitoring/alerts/${alertId}/acknowledge`),\n  \n  // 解决告警\n  resolveAlert: (alertId: number) => \n    api.post(`/api/v1/monitoring/alerts/${alertId}/resolve`),\n};\n\n// 健康检查API\nexport const healthApi = {\n  // 系统健康检查\n  getHealth: () => api.get('/health'),\n  \n  // 获取系统信息\n  getSystemInfo: () => api.get('/'),\n};\n\nexport default api;\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,uBAAuB;;AAE5C;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,OAAOA,MAAM;AACf,CAAC,EACAC,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAT,GAAG,CAACK,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC1BK,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EAAA,IAAAK,eAAA;EACTC,OAAO,CAACN,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAClC;EACA,IAAIA,KAAK,CAACO,IAAI,KAAK,cAAc,IAAI,EAAAF,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,KAAI,GAAG,EAAE;IAClEF,OAAO,CAACG,IAAI,CAAC,iBAAiB,CAAC;IAC/B,OAAOR,OAAO,CAACS,OAAO,CAACC,WAAW,CAACX,KAAK,CAACD,MAAM,CAAC,CAAC;EACnD;EACA,OAAOE,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,SAASW,WAAWA,CAACZ,MAAW,EAAE;EAChC,MAAMa,GAAG,GAAGb,MAAM,CAACa,GAAG;EAEtB,IAAIA,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC,IAAId,MAAM,CAACe,MAAM,KAAK,KAAK,EAAE;IAC9D,OAAO,CACL;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE,UAAU;MACrBC,WAAW,EAAE,QAAQ;MACrBC,IAAI,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC;MAC7BC,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE,IAAI;MACnBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,sBAAsB;MAClCC,UAAU,EAAE;IACd,CAAC,CACF;EACH;EAEA,IAAIf,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC,IAAId,MAAM,CAACe,MAAM,KAAK,KAAK,IAAI,CAACF,GAAG,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;IAC7F,OAAO,CACL;MACEE,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdK,WAAW,EAAE,WAAW;MACxBO,WAAW,EAAE,OAAO;MACpBC,QAAQ,EAAE,MAAM;MAChBP,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;MACxBQ,OAAO,EAAE,uCAAuC;MAChDJ,UAAU,EAAE,sBAAsB;MAClCC,UAAU,EAAE;IACd,CAAC,CACF;EACH;EAEA,IAAIf,GAAG,CAACC,QAAQ,CAAC,4BAA4B,CAAC,EAAE;IAC9C,OAAO,CACL;MACEE,EAAE,EAAE,CAAC;MACLgB,WAAW,EAAE,QAAQ;MACrBC,WAAW,EAAE,QAAQ;MACrBxB,MAAM,EAAE,SAAS;MACjByB,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,UAAU,EAAE,sBAAsB;MAClCC,WAAW,EAAE;IACf,CAAC,CACF;EACH;;EAEA;EACA,IAAIxB,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC7D,OAAO,CACL;MACEwB,UAAU,EAAE,OAAO;MACnBC,GAAG,EAAE,QAAQ;MACbC,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,qBAAqB;MAC9BC,IAAI,EAAE;IACR,CAAC,EACD;MACEJ,UAAU,EAAE,OAAO;MACnBC,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,qBAAqB;MAC9BC,IAAI,EAAE;IACR,CAAC,CACF;EACH;EAEA,IAAI7B,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;IACjE,OAAO,CACL;MACE6B,YAAY,EAAE,cAAc;MAC5B1B,IAAI,EAAE,YAAY;MAClB2B,KAAK,EAAE,cAAc;MACrBnC,MAAM,EAAE,YAAY;MACpBoC,KAAK,EAAE,oBAAoB;MAC3BJ,OAAO,EAAE;IACX,CAAC,EACD;MACEE,YAAY,EAAE,cAAc;MAC5B1B,IAAI,EAAE,UAAU;MAChB2B,KAAK,EAAE,WAAW;MAClBnC,MAAM,EAAE,UAAU;MAClBoC,KAAK,EAAE,wBAAwB;MAC/BJ,OAAO,EAAE;IACX,CAAC,CACF;EACH;EAEA,IAAI5B,GAAG,CAACC,QAAQ,CAAC,gBAAgB,CAAC,IAAID,GAAG,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;IAChE,OAAO,CACL;MACEgC,MAAM,EAAE,CAAC;MACT7B,IAAI,EAAE,yBAAyB;MAC/B8B,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;IAChB,CAAC,CACF;EACH;;EAEA;EACA,IAAIpC,GAAG,CAACC,QAAQ,CAAC,4BAA4B,CAAC,EAAE;IAC9C,OAAO,CACL;MACEE,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdR,MAAM,EAAE,QAAQ;MAChByC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC,EACD;MACEvC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdR,MAAM,EAAE,SAAS;MACjByC,SAAS,EAAE,IAAI;MACfC,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACtC,CAAC,CACF;EACH;EAEA,IAAI1C,GAAG,CAACC,QAAQ,CAAC,2BAA2B,CAAC,EAAE;IAC7C,OAAO,CACL;MACEE,EAAE,EAAE,CAAC;MACLiB,WAAW,EAAE,QAAQ;MACrBuB,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,iBAAiB;MAC1B/B,UAAU,EAAE,IAAI2B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpC9C,MAAM,EAAE;IACV,CAAC,EACD;MACEO,EAAE,EAAE,CAAC;MACLiB,WAAW,EAAE,QAAQ;MACrBuB,UAAU,EAAE,aAAa;MACzBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,gBAAgB;MACzB/B,UAAU,EAAE,IAAI2B,IAAI,CAACA,IAAI,CAACK,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACJ,WAAW,CAAC,CAAC;MAC9D9C,MAAM,EAAE;IACV,CAAC,CACF;EACH;EAEA,IAAII,GAAG,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;IACpC,OAAO;MAAE8C,OAAO,EAAE,IAAI;MAAEF,OAAO,EAAE,MAAM;MAAEG,OAAO,EAAE;IAAG,CAAC;EACxD;EAEA,IAAIhD,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;IAC5B,OAAO;MACL8C,OAAO,EAAE,IAAI;MACb1B,SAAS,EAAE,CAAC;MACZ4B,MAAM,EAAE,wPAAwP;MAChQC,MAAM,EAAE,EAAE;MACV5B,QAAQ,EAAE;IACZ,CAAC;EACH;EAEA,IAAItB,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;IACzB,OAAO;MACLkD,IAAI,EAAE;IACR,CAAC;EACH;EAEA,OAAO;IAAEJ,OAAO,EAAE,IAAI;IAAEF,OAAO,EAAE;EAAO,CAAC;AAC3C;;AAEA;AACA,OAAO,MAAMO,SAAS,GAAG;EACvB;EACAC,UAAU,EAAEA,CAAA,KAAM1E,GAAG,CAAC2E,GAAG,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE5E;EACAgE,SAAS,EAAGrD,EAAU,IAAKxB,GAAG,CAAC2E,GAAG,CAAC,mBAAmBnD,EAAE,EAAE,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE3F;EACAiE,YAAY,EAAGjE,IAAS,IAAKb,GAAG,CAAC+E,IAAI,CAAC,iBAAiB,EAAElE,IAAI,CAAC,CAAC+D,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE9F;EACAmE,YAAY,EAAEA,CAACxD,EAAU,EAAEX,IAAS,KAAKb,GAAG,CAACiF,GAAG,CAAC,mBAAmBzD,EAAE,EAAE,EAAEX,IAAI,CAAC,CAAC+D,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE/G;EACAqE,YAAY,EAAG1D,EAAU,IAAKxB,GAAG,CAACmF,MAAM,CAAC,mBAAmB3D,EAAE,EAAE,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEjG;EACAuE,cAAc,EAAG5D,EAAU,IAAKxB,GAAG,CAAC+E,IAAI,CAAC,mBAAmBvD,EAAE,kBAAkB,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEjH;EACAwE,cAAc,EAAEA,CAAC7D,EAAU,EAAEX,IAAS,KAAKb,GAAG,CAAC+E,IAAI,CAAC,mBAAmBvD,EAAE,UAAU,EAAEX,IAAI,CAAC,CAAC+D,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE1H;EACAyE,eAAe,EAAG9D,EAAU,IAAKxB,GAAG,CAAC+E,IAAI,CAAC,mBAAmBvD,EAAE,mBAAmB,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEnH;EACA0E,cAAc,EAAG/D,EAAU,IAAKxB,GAAG,CAAC+E,IAAI,CAAC,mBAAmBvD,EAAE,kBAAkB,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI;AAClH,CAAC;;AAED;AACA,OAAO,MAAM2E,SAAS,GAAG;EACvB;EACAC,SAAS,EAAGC,QAAgB,IAAK1F,GAAG,CAAC2E,GAAG,CAAC,kBAAkBe,QAAQ,SAAS,CAAC,CAACd,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE7G;EACA8E,SAAS,EAAEA,CAACD,QAAgB,EAAEE,SAAiB,KAC7C5F,GAAG,CAAC+E,IAAI,CAAC,kBAAkBW,QAAQ,2BAA2BE,SAAS,EAAE,CAAC,CAAChB,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE5G;EACAgF,WAAW,EAAEA,CAACH,QAAgB,EAAEI,OAAe,EAAEC,KAAK,GAAG,KAAK,KAC5D/F,GAAG,CAACmF,MAAM,CAAC,kBAAkBO,QAAQ,WAAWI,OAAO,UAAUC,KAAK,EAAE,CAAC,CAACnB,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE3G;EACAmF,aAAa,EAAEA,CAACN,QAAgB,EAAEO,GAAG,GAAG,IAAI,KAC1CjG,GAAG,CAAC2E,GAAG,CAAC,kBAAkBe,QAAQ,8BAA8BO,GAAG,EAAE,CAAC,CAACrB,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAExG;EACAqF,eAAe,EAAEA,CAACR,QAAgB,EAAElF,MAAW,KAC7CR,GAAG,CAAC+E,IAAI,CAAC,kBAAkBW,QAAQ,aAAa,EAAElF,MAAM,CAAC,CAACoE,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE3F;EACAsF,cAAc,EAAEA,CAACT,QAAgB,EAAEU,WAAmB,KACpDpG,GAAG,CAAC+E,IAAI,CAAC,kBAAkBW,QAAQ,eAAeU,WAAW,QAAQ,CAAC,CAACxB,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAExG;EACAwF,aAAa,EAAEA,CAACX,QAAgB,EAAEU,WAAmB,EAAEjG,OAAO,GAAG,EAAE,KACjEH,GAAG,CAAC+E,IAAI,CAAC,kBAAkBW,QAAQ,eAAeU,WAAW,iBAAiBjG,OAAO,EAAE,CAAC,CAACyE,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE1H;EACAyF,gBAAgB,EAAEA,CAACZ,QAAgB,EAAEU,WAAmB,KACtDpG,GAAG,CAAC+E,IAAI,CAAC,kBAAkBW,QAAQ,eAAeU,WAAW,UAAU,CAAC,CAACxB,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE1G;EACA0F,eAAe,EAAEA,CAACb,QAAgB,EAAEU,WAAmB,EAAEL,KAAK,GAAG,KAAK,KACpE/F,GAAG,CAACmF,MAAM,CAAC,kBAAkBO,QAAQ,eAAeU,WAAW,UAAUL,KAAK,EAAE,CAAC,CAACnB,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEnH;EACA2F,gBAAgB,EAAEA,CAACd,QAAgB,EAAEU,WAAmB,EAAEK,KAAK,GAAG,GAAG,KACnEzG,GAAG,CAAC2E,GAAG,CAAC,kBAAkBe,QAAQ,eAAeU,WAAW,eAAeK,KAAK,EAAE,CAAC,CAAC7B,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAErH;EACA6F,iBAAiB,EAAEA,CAAChB,QAAgB,EAAEU,WAAmB,KACvDpG,GAAG,CAAC2E,GAAG,CAAC,kBAAkBe,QAAQ,eAAeU,WAAW,QAAQ,CAAC,CAACxB,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEvG;EACA8F,WAAW,EAAGjB,QAAgB,IAC5B1F,GAAG,CAAC2E,GAAG,CAAC,kBAAkBe,QAAQ,YAAY,CAAC,CAACd,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI;AAClF,CAAC;;AAED;AACA,OAAO,MAAM+F,SAAS,GAAG;EACvB;EACAC,UAAU,EAAEA,CAAA,KAAM7G,GAAG,CAAC2E,GAAG,CAAC,iBAAiB,CAAC,CAACC,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE5E;EACAiG,SAAS,EAAGtF,EAAU,IAAKxB,GAAG,CAAC2E,GAAG,CAAC,mBAAmBnD,EAAE,EAAE,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE3F;EACAkG,YAAY,EAAGlG,IAAS,IAAKb,GAAG,CAAC+E,IAAI,CAAC,iBAAiB,EAAElE,IAAI,CAAC,CAAC+D,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE9F;EACAmG,YAAY,EAAEA,CAACxF,EAAU,EAAEX,IAAS,KAAKb,GAAG,CAACiF,GAAG,CAAC,mBAAmBzD,EAAE,EAAE,EAAEX,IAAI,CAAC,CAAC+D,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE/G;EACAoG,YAAY,EAAGzF,EAAU,IAAKxB,GAAG,CAACmF,MAAM,CAAC,mBAAmB3D,EAAE,EAAE,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEjG;EACAqG,aAAa,EAAEA,CAAC1F,EAAU,EAAEX,IAAS,KAAKb,GAAG,CAAC+E,IAAI,CAAC,mBAAmBvD,EAAE,UAAU,EAAEX,IAAI,CAAC,CAAC+D,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEzH;EACAsG,aAAa,EAAEA,CAAA,KAAMnH,GAAG,CAAC2E,GAAG,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE1F;EACAuG,YAAY,EAAG5F,EAAU,IAAKxB,GAAG,CAAC2E,GAAG,CAAC,8BAA8BnD,EAAE,EAAE,CAAC,CAACoD,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI;AAC1G,CAAC;;AAED;AACA,OAAO,MAAMwG,OAAO,GAAG;EACrB;EACAC,UAAU,EAAEA,CAAC5B,QAAgB,EAAE6B,QAAkB,KAC/CvH,GAAG,CAAC+E,IAAI,CAAC,iBAAiBW,QAAQ,SAAS,EAAE6B,QAAQ,EAAE;IACrDnH,OAAO,EAAE;MAAE,cAAc,EAAE;IAAsB;EACnD,CAAC,CAAC,CAACwE,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEpC;EACA2G,YAAY,EAAEA,CAAC9B,QAAgB,EAAE+B,UAAkB,KACjDzH,GAAG,CAAC2E,GAAG,CAAC,iBAAiBe,QAAQ,yBAAyB+B,UAAU,EAAE,EAAE;IACtEC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEJ;EACAC,SAAS,EAAEA,CAACjC,QAAgB,EAAE+B,UAAU,GAAG,GAAG,KAC5CzH,GAAG,CAAC2E,GAAG,CAAC,iBAAiBe,QAAQ,qBAAqB+B,UAAU,EAAE,CAAC,CAAC7C,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAErG;EACA+G,UAAU,EAAEA,CAAClC,QAAgB,EAAE+B,UAAkB,KAC/CzH,GAAG,CAACmF,MAAM,CAAC,iBAAiBO,QAAQ,uBAAuB+B,UAAU,EAAE,CAAC,CAAC7C,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAE1G;EACAgH,eAAe,EAAEA,CAACnC,QAAgB,EAAE+B,UAAkB,KACpDzH,GAAG,CAAC+E,IAAI,CAAC,iBAAiBW,QAAQ,sBAAsB+B,UAAU,EAAE,CAAC,CAAC7C,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAEvG;EACAiH,cAAc,EAAEA,CAACpC,QAAgB,EAAE+B,UAAkB,KACnDzH,GAAG,CAAC2E,GAAG,CAAC,iBAAiBe,QAAQ,wBAAwB+B,UAAU,EAAE,CAAC,CAAC7C,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAExG;EACAkH,eAAe,EAAEA,CAACrC,QAAgB,EAAE+B,UAAkB,EAAElF,OAAe,KACrEvC,GAAG,CAAC+E,IAAI,CAAC,iBAAiBW,QAAQ,UAAU,EAAE;IAAEsC,WAAW,EAAEP,UAAU;IAAElF;EAAQ,CAAC,CAAC,CAACqC,IAAI,CAAChE,QAAQ,IAAIA,QAAQ,CAACC,IAAI;AACtH,CAAC;;AAED;AACA,OAAO,MAAMoH,aAAa,GAAG;EAC3B;EACAC,mBAAmB,EAAEA,CAAA,KAAMlI,GAAG,CAAC2E,GAAG,CAAC,4BAA4B,CAAC;EAEhE;EACAwD,cAAc,EAAGzC,QAAgB,IAAK1F,GAAG,CAAC2E,GAAG,CAAC,8BAA8Be,QAAQ,QAAQ,CAAC;EAE7F;EACA0C,cAAc,EAAEA,CAAC1C,QAAgB,EAAE2C,SAAiB,KAClDrI,GAAG,CAAC2E,GAAG,CAAC,8BAA8Be,QAAQ,kBAAkB2C,SAAS,EAAE,CAAC;EAE9E;EACAC,SAAS,EAAEA,CAAA,KAAMtI,GAAG,CAAC2E,GAAG,CAAC,2BAA2B,CAAC;EAErD;EACA4D,gBAAgB,EAAGC,OAAe,IAChCxI,GAAG,CAAC+E,IAAI,CAAC,6BAA6ByD,OAAO,cAAc,CAAC;EAE9D;EACAC,YAAY,EAAGD,OAAe,IAC5BxI,GAAG,CAAC+E,IAAI,CAAC,6BAA6ByD,OAAO,UAAU;AAC3D,CAAC;;AAED;AACA,OAAO,MAAME,SAAS,GAAG;EACvB;EACAC,SAAS,EAAEA,CAAA,KAAM3I,GAAG,CAAC2E,GAAG,CAAC,SAAS,CAAC;EAEnC;EACAiE,aAAa,EAAEA,CAAA,KAAM5I,GAAG,CAAC2E,GAAG,CAAC,GAAG;AAClC,CAAC;AAED,eAAe3E,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}