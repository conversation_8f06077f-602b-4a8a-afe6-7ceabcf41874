{"ast": null, "code": "import number from \"../number.js\";\nimport { parseCss, parseSvg } from \"./parse.js\";\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({\n        i: i - 4,\n        x: number(xa, xb)\n      }, {\n        i: i - 2,\n        x: number(ya, yb)\n      });\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360;else if (b - a > 180) a += 360; // shortest path\n      q.push({\n        i: s.push(pop(s) + \"rotate(\", null, degParen) - 2,\n        x: number(a, b)\n      });\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({\n        i: s.push(pop(s) + \"skewX(\", null, degParen) - 2,\n        x: number(a, b)\n      });\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({\n        i: i - 4,\n        x: number(xa, xb)\n      }, {\n        i: i - 2,\n        x: number(ya, yb)\n      });\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n  return function (a, b) {\n    var s = [],\n      // string constants and placeholders\n      q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function (t) {\n      var i = -1,\n        n = q.length,\n        o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");", "map": {"version": 3, "names": ["number", "parseCss", "parseSvg", "interpolateTransform", "parse", "pxComma", "pxParen", "degParen", "pop", "s", "length", "translate", "xa", "ya", "xb", "yb", "q", "i", "push", "x", "rotate", "a", "b", "skewX", "scale", "translateX", "translateY", "scaleX", "scaleY", "t", "n", "o", "join", "interpolateTransformCss", "interpolateTransformSvg"], "sources": ["/home/<USER>/itai/frontend/node_modules/d3-interpolate/src/transform/index.js"], "sourcesContent": ["import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,SAAQC,QAAQ,EAAEC,QAAQ,QAAO,YAAY;AAE7C,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAE/D,SAASC,GAAGA,CAACC,CAAC,EAAE;IACd,OAAOA,CAAC,CAACC,MAAM,GAAGD,CAAC,CAACD,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;EACtC;EAEA,SAASG,SAASA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEN,CAAC,EAAEO,CAAC,EAAE;IACvC,IAAIJ,EAAE,KAAKE,EAAE,IAAID,EAAE,KAAKE,EAAE,EAAE;MAC1B,IAAIE,CAAC,GAAGR,CAAC,CAACS,IAAI,CAAC,YAAY,EAAE,IAAI,EAAEb,OAAO,EAAE,IAAI,EAAEC,OAAO,CAAC;MAC1DU,CAAC,CAACE,IAAI,CAAC;QAACD,CAAC,EAAEA,CAAC,GAAG,CAAC;QAAEE,CAAC,EAAEnB,MAAM,CAACY,EAAE,EAAEE,EAAE;MAAC,CAAC,EAAE;QAACG,CAAC,EAAEA,CAAC,GAAG,CAAC;QAAEE,CAAC,EAAEnB,MAAM,CAACa,EAAE,EAAEE,EAAE;MAAC,CAAC,CAAC;IACtE,CAAC,MAAM,IAAID,EAAE,IAAIC,EAAE,EAAE;MACnBN,CAAC,CAACS,IAAI,CAAC,YAAY,GAAGJ,EAAE,GAAGT,OAAO,GAAGU,EAAE,GAAGT,OAAO,CAAC;IACpD;EACF;EAEA,SAASc,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAEb,CAAC,EAAEO,CAAC,EAAE;IAC1B,IAAIK,CAAC,KAAKC,CAAC,EAAE;MACX,IAAID,CAAC,GAAGC,CAAC,GAAG,GAAG,EAAEA,CAAC,IAAI,GAAG,CAAC,KAAM,IAAIA,CAAC,GAAGD,CAAC,GAAG,GAAG,EAAEA,CAAC,IAAI,GAAG,CAAC,CAAC;MAC3DL,CAAC,CAACE,IAAI,CAAC;QAACD,CAAC,EAAER,CAAC,CAACS,IAAI,CAACV,GAAG,CAACC,CAAC,CAAC,GAAG,SAAS,EAAE,IAAI,EAAEF,QAAQ,CAAC,GAAG,CAAC;QAAEY,CAAC,EAAEnB,MAAM,CAACqB,CAAC,EAAEC,CAAC;MAAC,CAAC,CAAC;IAC9E,CAAC,MAAM,IAAIA,CAAC,EAAE;MACZb,CAAC,CAACS,IAAI,CAACV,GAAG,CAACC,CAAC,CAAC,GAAG,SAAS,GAAGa,CAAC,GAAGf,QAAQ,CAAC;IAC3C;EACF;EAEA,SAASgB,KAAKA,CAACF,CAAC,EAAEC,CAAC,EAAEb,CAAC,EAAEO,CAAC,EAAE;IACzB,IAAIK,CAAC,KAAKC,CAAC,EAAE;MACXN,CAAC,CAACE,IAAI,CAAC;QAACD,CAAC,EAAER,CAAC,CAACS,IAAI,CAACV,GAAG,CAACC,CAAC,CAAC,GAAG,QAAQ,EAAE,IAAI,EAAEF,QAAQ,CAAC,GAAG,CAAC;QAAEY,CAAC,EAAEnB,MAAM,CAACqB,CAAC,EAAEC,CAAC;MAAC,CAAC,CAAC;IAC7E,CAAC,MAAM,IAAIA,CAAC,EAAE;MACZb,CAAC,CAACS,IAAI,CAACV,GAAG,CAACC,CAAC,CAAC,GAAG,QAAQ,GAAGa,CAAC,GAAGf,QAAQ,CAAC;IAC1C;EACF;EAEA,SAASiB,KAAKA,CAACZ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEN,CAAC,EAAEO,CAAC,EAAE;IACnC,IAAIJ,EAAE,KAAKE,EAAE,IAAID,EAAE,KAAKE,EAAE,EAAE;MAC1B,IAAIE,CAAC,GAAGR,CAAC,CAACS,IAAI,CAACV,GAAG,CAACC,CAAC,CAAC,GAAG,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC;MACvDO,CAAC,CAACE,IAAI,CAAC;QAACD,CAAC,EAAEA,CAAC,GAAG,CAAC;QAAEE,CAAC,EAAEnB,MAAM,CAACY,EAAE,EAAEE,EAAE;MAAC,CAAC,EAAE;QAACG,CAAC,EAAEA,CAAC,GAAG,CAAC;QAAEE,CAAC,EAAEnB,MAAM,CAACa,EAAE,EAAEE,EAAE;MAAC,CAAC,CAAC;IACtE,CAAC,MAAM,IAAID,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,EAAE;MAC/BN,CAAC,CAACS,IAAI,CAACV,GAAG,CAACC,CAAC,CAAC,GAAG,QAAQ,GAAGK,EAAE,GAAG,GAAG,GAAGC,EAAE,GAAG,GAAG,CAAC;IACjD;EACF;EAEA,OAAO,UAASM,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAIb,CAAC,GAAG,EAAE;MAAE;MACRO,CAAC,GAAG,EAAE,CAAC,CAAC;IACZK,CAAC,GAAGjB,KAAK,CAACiB,CAAC,CAAC,EAAEC,CAAC,GAAGlB,KAAK,CAACkB,CAAC,CAAC;IAC1BX,SAAS,CAACU,CAAC,CAACI,UAAU,EAAEJ,CAAC,CAACK,UAAU,EAAEJ,CAAC,CAACG,UAAU,EAAEH,CAAC,CAACI,UAAU,EAAEjB,CAAC,EAAEO,CAAC,CAAC;IACvEI,MAAM,CAACC,CAAC,CAACD,MAAM,EAAEE,CAAC,CAACF,MAAM,EAAEX,CAAC,EAAEO,CAAC,CAAC;IAChCO,KAAK,CAACF,CAAC,CAACE,KAAK,EAAED,CAAC,CAACC,KAAK,EAAEd,CAAC,EAAEO,CAAC,CAAC;IAC7BQ,KAAK,CAACH,CAAC,CAACM,MAAM,EAAEN,CAAC,CAACO,MAAM,EAAEN,CAAC,CAACK,MAAM,EAAEL,CAAC,CAACM,MAAM,EAAEnB,CAAC,EAAEO,CAAC,CAAC;IACnDK,CAAC,GAAGC,CAAC,GAAG,IAAI,CAAC,CAAC;IACd,OAAO,UAASO,CAAC,EAAE;MACjB,IAAIZ,CAAC,GAAG,CAAC,CAAC;QAAEa,CAAC,GAAGd,CAAC,CAACN,MAAM;QAAEqB,CAAC;MAC3B,OAAO,EAAEd,CAAC,GAAGa,CAAC,EAAErB,CAAC,CAAC,CAACsB,CAAC,GAAGf,CAAC,CAACC,CAAC,CAAC,EAAEA,CAAC,CAAC,GAAGc,CAAC,CAACZ,CAAC,CAACU,CAAC,CAAC;MACxC,OAAOpB,CAAC,CAACuB,IAAI,CAAC,EAAE,CAAC;IACnB,CAAC;EACH,CAAC;AACH;AAEA,OAAO,IAAIC,uBAAuB,GAAG9B,oBAAoB,CAACF,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;AAC1F,OAAO,IAAIiC,uBAAuB,GAAG/B,oBAAoB,CAACD,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}