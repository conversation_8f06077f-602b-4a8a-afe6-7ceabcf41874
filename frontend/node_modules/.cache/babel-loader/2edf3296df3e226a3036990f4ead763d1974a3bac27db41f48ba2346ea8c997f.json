{"ast": null, "code": "export default function identity(x) {\n  return x;\n}", "map": {"version": 3, "names": ["identity", "x"], "sources": ["/home/<USER>/itai/node_modules/d3-array/src/identity.js"], "sourcesContent": ["export default function identity(x) {\n  return x;\n}\n"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,CAAC,EAAE;EAClC,OAAOA,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}