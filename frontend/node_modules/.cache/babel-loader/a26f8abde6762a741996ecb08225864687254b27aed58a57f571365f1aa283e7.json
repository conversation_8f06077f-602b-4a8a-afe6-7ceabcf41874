{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isTypedArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\nexports.isTypedArray = isTypedArray;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isTypedArray", "x", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DataView"], "sources": ["/home/<USER>/itai/frontend/node_modules/es-toolkit/dist/predicate/isTypedArray.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isTypedArray(x) {\n    return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n\nexports.isTypedArray = isTypedArray;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,YAAYA,CAACC,CAAC,EAAE;EACrB,OAAOC,WAAW,CAACC,MAAM,CAACF,CAAC,CAAC,IAAI,EAAEA,CAAC,YAAYG,QAAQ,CAAC;AAC5D;AAEAR,OAAO,CAACI,YAAY,GAAGA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}