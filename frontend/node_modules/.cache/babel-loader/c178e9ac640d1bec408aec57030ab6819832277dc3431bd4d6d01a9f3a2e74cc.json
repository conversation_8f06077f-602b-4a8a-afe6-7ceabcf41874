{"ast": null, "code": "export default function some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["some", "values", "test", "TypeError", "index", "value"], "sources": ["/home/<USER>/itai/node_modules/d3-array/src/some.js"], "sourcesContent": ["export default function some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n"], "mappings": "AAAA,eAAe,SAASA,IAAIA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACzC,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC7E,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIJ,MAAM,EAAE;IAC1B,IAAIC,IAAI,CAACG,KAAK,EAAE,EAAED,KAAK,EAAEH,MAAM,CAAC,EAAE;MAChC,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}