{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport rules from \"../rule\";\nvar required = function required(rule, value, callback, source, options) {\n  var errors = [];\n  var type = Array.isArray(value) ? 'array' : _typeof(value);\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\nexport default required;", "map": {"version": 3, "names": ["_typeof", "rules", "required", "rule", "value", "callback", "source", "options", "errors", "type", "Array", "isArray"], "sources": ["/home/<USER>/itai/frontend/node_modules/@rc-component/async-validator/es/validator/required.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport rules from \"../rule\";\nvar required = function required(rule, value, callback, source, options) {\n  var errors = [];\n  var type = Array.isArray(value) ? 'array' : _typeof(value);\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n};\nexport default required;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,KAAK,MAAM,SAAS;AAC3B,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACvE,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,GAAG,OAAO,GAAGJ,OAAO,CAACI,KAAK,CAAC;EAC1DH,KAAK,CAACC,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,EAAEE,IAAI,CAAC;EAC1DJ,QAAQ,CAACG,MAAM,CAAC;AAClB,CAAC;AACD,eAAeN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}