{"ast": null, "code": "var now = +new Date();\nvar index = 0;\nexport default function uid() {\n  // eslint-disable-next-line no-plusplus\n  return \"rc-upload-\".concat(now, \"-\").concat(++index);\n}", "map": {"version": 3, "names": ["now", "Date", "index", "uid", "concat"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-upload/es/uid.js"], "sourcesContent": ["var now = +new Date();\nvar index = 0;\nexport default function uid() {\n  // eslint-disable-next-line no-plusplus\n  return \"rc-upload-\".concat(now, \"-\").concat(++index);\n}"], "mappings": "AAAA,IAAIA,GAAG,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;AACrB,IAAIC,KAAK,GAAG,CAAC;AACb,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B;EACA,OAAO,YAAY,CAACC,MAAM,CAACJ,GAAG,EAAE,GAAG,CAAC,CAACI,MAAM,CAAC,EAAEF,KAAK,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}