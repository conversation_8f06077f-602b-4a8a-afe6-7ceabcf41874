{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Space, Tag, Modal, Form, Input, Select, Switch, message, Popconfirm, Card, Row, Col, Statistic } from 'antd';\nimport { serverApi } from '../../services/api';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, StopOutlined, ReloadOutlined, EyeOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst ServerManagement = () => {\n  _s();\n  const [servers, setServers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingServer, setEditingServer] = useState(null);\n  const [form] = Form.useForm();\n\n  // 加载服务器数据\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      const data = await serverApi.getServers();\n      setServers(data.map(server => ({\n        ...server,\n        status: Math.random() > 0.5 ? 'online' : 'offline' // 模拟状态\n      })));\n    } catch (error) {\n      console.error('加载服务器列表失败:', error);\n      message.error('加载服务器列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadServers();\n  }, []);\n  const columns = [{\n    title: '服务器名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: record.status === 'online' ? 'green' : record.status === 'offline' ? 'red' : 'orange',\n        children: record.status === 'online' ? '在线' : record.status === 'offline' ? '离线' : '连接中'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '地址',\n    dataIndex: 'host',\n    key: 'host',\n    render: (text, record) => `${text}:${record.port}`\n  }, {\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '认证方式',\n    dataIndex: 'auth_type',\n    key: 'auth_type',\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: text === 'password' ? 'blue' : 'green',\n      children: text === 'password' ? '密码' : '密钥'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '标签',\n    dataIndex: 'tags',\n    key: 'tags',\n    render: tags => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: tags === null || tags === void 0 ? void 0 : tags.map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: tag\n      }, tag, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 13\n      }, this))\n    }, void 0, false)\n  }, {\n    title: '监控状态',\n    key: 'monitoring',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        color: record.monitoring_enabled ? 'green' : 'default',\n        children: record.monitoring_enabled ? '已启用' : '已禁用'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: record.alert_enabled ? 'orange' : 'default',\n        children: record.alert_enabled ? '告警开启' : '告警关闭'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewServer(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditServer(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: record.monitoring_enabled ? /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 47\n        }, this) : /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 66\n        }, this),\n        onClick: () => handleToggleMonitoring(record),\n        children: record.monitoring_enabled ? '停止监控' : '开始监控'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u53F0\\u670D\\u52A1\\u5668\\u5417\\uFF1F\",\n        onConfirm: () => handleDeleteServer(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this)\n  }];\n  const handleAddServer = () => {\n    setEditingServer(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditServer = server => {\n    setEditingServer(server);\n    form.setFieldsValue(server);\n    setModalVisible(true);\n  };\n  const handleViewServer = async server => {\n    try {\n      var _server$tags;\n      // 测试连接状态\n      const connectionResult = await serverApi.testConnection(server.id);\n      Modal.info({\n        title: `服务器详情 - ${server.name}`,\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 16\n            }, this), \" \", server.host, \":\", server.port]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u7528\\u6237\\u540D:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 16\n            }, this), \" \", server.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BA4\\u8BC1\\u65B9\\u5F0F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 16\n            }, this), \" \", server.auth_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u63CF\\u8FF0:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 16\n            }, this), \" \", server.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6807\\u7B7E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 16\n            }, this), \" \", (_server$tags = server.tags) === null || _server$tags === void 0 ? void 0 : _server$tags.join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8FDE\\u63A5\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 16\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: connectionResult.success ? 'green' : 'red',\n              children: connectionResult.success ? '连接正常' : '连接失败'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), connectionResult.success && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5EF6\\u8FDF:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 18\n            }, this), \" \", connectionResult.latency, \"ms\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 16\n            }, this), \" \", new Date(server.created_at).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this),\n        width: 600\n      });\n    } catch (error) {\n      message.error('获取服务器详情失败');\n    }\n  };\n  const handleDeleteServer = async id => {\n    try {\n      await serverApi.deleteServer(id);\n      setServers(servers.filter(s => s.id !== id));\n      message.success('服务器删除成功');\n    } catch (error) {\n      message.error('删除服务器失败');\n    }\n  };\n  const handleToggleMonitoring = async server => {\n    try {\n      if (server.monitoring_enabled) {\n        await serverApi.stopMonitoring(server.id);\n      } else {\n        await serverApi.startMonitoring(server.id);\n      }\n      const updatedServers = servers.map(s => s.id === server.id ? {\n        ...s,\n        monitoring_enabled: !s.monitoring_enabled\n      } : s);\n      setServers(updatedServers);\n      message.success(`${server.name} 监控已${server.monitoring_enabled ? '停止' : '启动'}`);\n    } catch (error) {\n      message.error('切换监控状态失败');\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingServer) {\n        // 更新服务器\n        await serverApi.updateServer(editingServer.id, values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器更新成功');\n      } else {\n        // 创建新服务器\n        await serverApi.createServer(values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器添加成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n    form.resetFields();\n  };\n  const onlineCount = servers.filter(s => s.status === 'online').length;\n  const totalCount = servers.length;\n  const monitoringCount = servers.filter(s => s.monitoring_enabled).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u670D\\u52A1\\u5668\\u6570\",\n            value: totalCount,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u670D\\u52A1\\u5668\",\n            value: onlineCount,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u76D1\\u63A7\\u4E2D\",\n            value: monitoringCount,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u7387\",\n            value: totalCount > 0 ? Math.round(onlineCount / totalCount * 100) : 0,\n            suffix: \"%\",\n            valueStyle: {\n              color: onlineCount / totalCount > 0.8 ? '#52c41a' : '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u670D\\u52A1\\u5668\\u5217\\u8868\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 27\n          }, this),\n          onClick: () => setLoading(true),\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 42\n          }, this),\n          onClick: handleAddServer,\n          children: \"\\u6DFB\\u52A0\\u670D\\u52A1\\u5668\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: servers,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingServer ? '编辑服务器' : '添加服务器',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          port: 22,\n          auth_type: 'password',\n          monitoring_enabled: true,\n          alert_enabled: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u670D\\u52A1\\u5668\\u540D\\u79F0\",\n              name: \"name\",\n              rules: [{\n                required: true,\n                message: '请输入服务器名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u670D\\u52A1\\u5668\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u670D\\u52A1\\u5668\\u5730\\u5740\",\n              name: \"host\",\n              rules: [{\n                required: true,\n                message: '请输入服务器地址'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\\u6216\\u57DF\\u540D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u7AEF\\u53E3\",\n              name: \"port\",\n              rules: [{\n                required: true,\n                message: '请输入端口号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u7528\\u6237\\u540D\",\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"root\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u8BA4\\u8BC1\\u65B9\\u5F0F\",\n              name: \"auth_type\",\n              rules: [{\n                required: true,\n                message: '请选择认证方式'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"password\",\n                  children: \"\\u5BC6\\u7801\\u8BA4\\u8BC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"key\",\n                  children: \"\\u5BC6\\u94A5\\u8BA4\\u8BC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u63CF\\u8FF0\",\n          name: \"description\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u670D\\u52A1\\u5668\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u542F\\u7528\\u76D1\\u63A7\",\n              name: \"monitoring_enabled\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u542F\\u7528\\u544A\\u8B66\",\n              name: \"alert_enabled\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 5\n  }, this);\n};\n_s(ServerManagement, \"hePYHUqcoqbtYMSTLtyBOPHlOWg=\", false, function () {\n  return [Form.useForm];\n});\n_c = ServerManagement;\nexport default ServerManagement;\nvar _c;\n$RefreshReg$(_c, \"ServerManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "Switch", "message", "Popconfirm", "Card", "Row", "Col", "Statistic", "serverApi", "PlusOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "StopOutlined", "ReloadOutlined", "EyeOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "ServerManagement", "_s", "servers", "setServers", "loading", "setLoading", "modalVisible", "setModalVisible", "editingServer", "setEditingServer", "form", "useForm", "loadServers", "data", "getServers", "map", "server", "status", "Math", "random", "error", "console", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "port", "tags", "tag", "_", "monitoring_enabled", "alert_enabled", "size", "type", "icon", "onClick", "handleViewServer", "handleEditServer", "handleToggleMonitoring", "onConfirm", "handleDeleteServer", "id", "okText", "cancelText", "danger", "handleAddServer", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_server$tags", "connectionResult", "testConnection", "info", "name", "content", "host", "username", "auth_type", "description", "join", "success", "latency", "Date", "created_at", "toLocaleString", "width", "deleteServer", "filter", "s", "stopMonitoring", "startMonitoring", "updatedServers", "handleModalOk", "values", "validateFields", "updateServer", "createServer", "handleModalCancel", "onlineCount", "length", "totalCount", "monitoringCount", "gutter", "style", "marginBottom", "span", "value", "valueStyle", "round", "suffix", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onOk", "onCancel", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "TextArea", "rows", "valuePropName", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Switch,\n  message,\n  Popconfirm,\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Spin,\n  Alert,\n} from 'antd';\nimport { serverApi } from '../../services/api';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  StopOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\ninterface Server {\n  id: number;\n  name: string;\n  host: string;\n  port: number;\n  username: string;\n  auth_type: string;\n  description?: string;\n  tags?: string[];\n  monitoring_enabled: boolean;\n  alert_enabled: boolean;\n  is_active: boolean;\n  status?: 'online' | 'offline' | 'connecting';\n  created_at: string;\n  updated_at: string;\n}\n\nconst { Option } = Select;\n\nconst ServerManagement: React.FC = () => {\n  const [servers, setServers] = useState<Server[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingServer, setEditingServer] = useState<Server | null>(null);\n  const [form] = Form.useForm();\n\n  // 加载服务器数据\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      const data = await serverApi.getServers();\n      setServers(data.map((server: any) => ({\n        ...server,\n        status: Math.random() > 0.5 ? 'online' : 'offline' // 模拟状态\n      })));\n    } catch (error) {\n      console.error('加载服务器列表失败:', error);\n      message.error('加载服务器列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadServers();\n  }, []);\n\n  const columns: ColumnsType<Server> = [\n    {\n      title: '服务器名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <span>{text}</span>\n          <Tag color={record.status === 'online' ? 'green' : record.status === 'offline' ? 'red' : 'orange'}>\n            {record.status === 'online' ? '在线' : record.status === 'offline' ? '离线' : '连接中'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '地址',\n      dataIndex: 'host',\n      key: 'host',\n      render: (text, record) => `${text}:${record.port}`,\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '认证方式',\n      dataIndex: 'auth_type',\n      key: 'auth_type',\n      render: (text) => (\n        <Tag color={text === 'password' ? 'blue' : 'green'}>\n          {text === 'password' ? '密码' : '密钥'}\n        </Tag>\n      ),\n    },\n    {\n      title: '标签',\n      dataIndex: 'tags',\n      key: 'tags',\n      render: (tags: string[]) => (\n        <>\n          {tags?.map((tag) => (\n            <Tag key={tag} color=\"blue\">\n              {tag}\n            </Tag>\n          ))}\n        </>\n      ),\n    },\n    {\n      title: '监控状态',\n      key: 'monitoring',\n      render: (_, record) => (\n        <Space>\n          <Tag color={record.monitoring_enabled ? 'green' : 'default'}>\n            {record.monitoring_enabled ? '已启用' : '已禁用'}\n          </Tag>\n          <Tag color={record.alert_enabled ? 'orange' : 'default'}>\n            {record.alert_enabled ? '告警开启' : '告警关闭'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"link\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewServer(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditServer(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            icon={record.monitoring_enabled ? <StopOutlined /> : <PlayCircleOutlined />}\n            onClick={() => handleToggleMonitoring(record)}\n          >\n            {record.monitoring_enabled ? '停止监控' : '开始监控'}\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这台服务器吗？\"\n            onConfirm={() => handleDeleteServer(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const handleAddServer = () => {\n    setEditingServer(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditServer = (server: Server) => {\n    setEditingServer(server);\n    form.setFieldsValue(server);\n    setModalVisible(true);\n  };\n\n  const handleViewServer = async (server: Server) => {\n    try {\n      // 测试连接状态\n      const connectionResult = await serverApi.testConnection(server.id);\n\n      Modal.info({\n        title: `服务器详情 - ${server.name}`,\n        content: (\n          <div>\n            <p><strong>地址:</strong> {server.host}:{server.port}</p>\n            <p><strong>用户名:</strong> {server.username}</p>\n            <p><strong>认证方式:</strong> {server.auth_type}</p>\n            <p><strong>描述:</strong> {server.description}</p>\n            <p><strong>标签:</strong> {server.tags?.join(', ')}</p>\n            <p><strong>连接状态:</strong>\n              <Tag color={connectionResult.success ? 'green' : 'red'}>\n                {connectionResult.success ? '连接正常' : '连接失败'}\n              </Tag>\n            </p>\n            {connectionResult.success && (\n              <p><strong>延迟:</strong> {connectionResult.latency}ms</p>\n            )}\n            <p><strong>创建时间:</strong> {new Date(server.created_at).toLocaleString()}</p>\n          </div>\n        ),\n        width: 600,\n      });\n    } catch (error) {\n      message.error('获取服务器详情失败');\n    }\n  };\n\n  const handleDeleteServer = async (id: number) => {\n    try {\n      await serverApi.deleteServer(id);\n      setServers(servers.filter(s => s.id !== id));\n      message.success('服务器删除成功');\n    } catch (error) {\n      message.error('删除服务器失败');\n    }\n  };\n\n  const handleToggleMonitoring = async (server: Server) => {\n    try {\n      if (server.monitoring_enabled) {\n        await serverApi.stopMonitoring(server.id);\n      } else {\n        await serverApi.startMonitoring(server.id);\n      }\n\n      const updatedServers = servers.map(s =>\n        s.id === server.id\n          ? { ...s, monitoring_enabled: !s.monitoring_enabled }\n          : s\n      );\n      setServers(updatedServers);\n      message.success(\n        `${server.name} 监控已${server.monitoring_enabled ? '停止' : '启动'}`\n      );\n    } catch (error) {\n      message.error('切换监控状态失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingServer) {\n        // 更新服务器\n        await serverApi.updateServer(editingServer.id, values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器更新成功');\n      } else {\n        // 创建新服务器\n        await serverApi.createServer(values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器添加成功');\n      }\n      \n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n    form.resetFields();\n  };\n\n  const onlineCount = servers.filter(s => s.status === 'online').length;\n  const totalCount = servers.length;\n  const monitoringCount = servers.filter(s => s.monitoring_enabled).length;\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总服务器数\"\n              value={totalCount}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"在线服务器\"\n              value={onlineCount}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"监控中\"\n              value={monitoringCount}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"在线率\"\n              value={totalCount > 0 ? Math.round((onlineCount / totalCount) * 100) : 0}\n              suffix=\"%\"\n              valueStyle={{ color: onlineCount / totalCount > 0.8 ? '#52c41a' : '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card\n        title=\"服务器列表\"\n        extra={\n          <Space>\n            <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>\n              刷新\n            </Button>\n            <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddServer}>\n              添加服务器\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={servers}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingServer ? '编辑服务器' : '添加服务器'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            port: 22,\n            auth_type: 'password',\n            monitoring_enabled: true,\n            alert_enabled: true,\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                label=\"服务器名称\"\n                name=\"name\"\n                rules={[{ required: true, message: '请输入服务器名称' }]}\n              >\n                <Input placeholder=\"请输入服务器名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label=\"服务器地址\"\n                name=\"host\"\n                rules={[{ required: true, message: '请输入服务器地址' }]}\n              >\n                <Input placeholder=\"请输入IP地址或域名\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                label=\"端口\"\n                name=\"port\"\n                rules={[{ required: true, message: '请输入端口号' }]}\n              >\n                <Input type=\"number\" placeholder=\"22\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                label=\"用户名\"\n                name=\"username\"\n                rules={[{ required: true, message: '请输入用户名' }]}\n              >\n                <Input placeholder=\"root\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                label=\"认证方式\"\n                name=\"auth_type\"\n                rules={[{ required: true, message: '请选择认证方式' }]}\n              >\n                <Select>\n                  <Option value=\"password\">密码认证</Option>\n                  <Option value=\"key\">密钥认证</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            label=\"描述\"\n            name=\"description\"\n          >\n            <Input.TextArea rows={3} placeholder=\"请输入服务器描述\" />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                label=\"启用监控\"\n                name=\"monitoring_enabled\"\n                valuePropName=\"checked\"\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label=\"启用告警\"\n                name=\"alert_enabled\"\n                valuePropName=\"checked\"\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ServerManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,QAGJ,MAAM;AACb,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,WAAW,QACN,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAoB3B,MAAM;EAAEC;AAAO,CAAC,GAAGpB,MAAM;AAEzB,MAAMqB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACwC,IAAI,CAAC,GAAGjC,IAAI,CAACkC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,IAAI,GAAG,MAAM1B,SAAS,CAAC2B,UAAU,CAAC,CAAC;MACzCX,UAAU,CAACU,IAAI,CAACE,GAAG,CAAEC,MAAW,KAAM;QACpC,GAAGA,MAAM;QACTC,MAAM,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,SAAS,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCvC,OAAO,CAACuC,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlC,SAAS,CAAC,MAAM;IACdyC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,OAA4B,GAAG,CACnC;IACEC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBhC,OAAA,CAACtB,KAAK;MAAAuD,QAAA,gBACJjC,OAAA;QAAAiC,QAAA,EAAOF;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnBrC,OAAA,CAACrB,GAAG;QAAC2D,KAAK,EAAEN,MAAM,CAACX,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAGW,MAAM,CAACX,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,QAAS;QAAAY,QAAA,EAC/FD,MAAM,CAACX,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAGW,MAAM,CAACX,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK,GAAGD,IAAI,IAAIC,MAAM,CAACO,IAAI;EAClD,CAAC,EACD;IACEZ,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGC,IAAI,iBACX/B,OAAA,CAACrB,GAAG;MAAC2D,KAAK,EAAEP,IAAI,KAAK,UAAU,GAAG,MAAM,GAAG,OAAQ;MAAAE,QAAA,EAChDF,IAAI,KAAK,UAAU,GAAG,IAAI,GAAG;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGU,IAAc,iBACrBxC,OAAA,CAAAE,SAAA;MAAA+B,QAAA,EACGO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAErB,GAAG,CAAEsB,GAAG,iBACbzC,OAAA,CAACrB,GAAG;QAAW2D,KAAK,EAAC,MAAM;QAAAL,QAAA,EACxBQ;MAAG,GADIA,GAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CACN;IAAC,gBACF;EAEN,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACY,CAAC,EAAEV,MAAM,kBAChBhC,OAAA,CAACtB,KAAK;MAAAuD,QAAA,gBACJjC,OAAA,CAACrB,GAAG;QAAC2D,KAAK,EAAEN,MAAM,CAACW,kBAAkB,GAAG,OAAO,GAAG,SAAU;QAAAV,QAAA,EACzDD,MAAM,CAACW,kBAAkB,GAAG,KAAK,GAAG;MAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACNrC,OAAA,CAACrB,GAAG;QAAC2D,KAAK,EAAEN,MAAM,CAACY,aAAa,GAAG,QAAQ,GAAG,SAAU;QAAAX,QAAA,EACrDD,MAAM,CAACY,aAAa,GAAG,MAAM,GAAG;MAAM;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACY,CAAC,EAAEV,MAAM,kBAChBhC,OAAA,CAACtB,KAAK;MAACmE,IAAI,EAAC,QAAQ;MAAAZ,QAAA,gBAClBjC,OAAA,CAACvB,MAAM;QACLqE,IAAI,EAAC,MAAM;QACXC,IAAI,eAAE/C,OAAA,CAACF,WAAW;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBW,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAACjB,MAAM,CAAE;QAAAC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrC,OAAA,CAACvB,MAAM;QACLqE,IAAI,EAAC,MAAM;QACXC,IAAI,eAAE/C,OAAA,CAACP,YAAY;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBW,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAAClB,MAAM,CAAE;QAAAC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrC,OAAA,CAACvB,MAAM;QACLqE,IAAI,EAAC,MAAM;QACXC,IAAI,EAAEf,MAAM,CAACW,kBAAkB,gBAAG3C,OAAA,CAACJ,YAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGrC,OAAA,CAACL,kBAAkB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5EW,OAAO,EAAEA,CAAA,KAAMG,sBAAsB,CAACnB,MAAM,CAAE;QAAAC,QAAA,EAE7CD,MAAM,CAACW,kBAAkB,GAAG,MAAM,GAAG;MAAM;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACTrC,OAAA,CAACd,UAAU;QACTyC,KAAK,EAAC,0EAAc;QACpByB,SAAS,EAAEA,CAAA,KAAMC,kBAAkB,CAACrB,MAAM,CAACsB,EAAE,CAAE;QAC/CC,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAvB,QAAA,eAEfjC,OAAA,CAACvB,MAAM;UAACqE,IAAI,EAAC,MAAM;UAACW,MAAM;UAACV,IAAI,eAAE/C,OAAA,CAACN,cAAc;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMqB,eAAe,GAAGA,CAAA,KAAM;IAC5B7C,gBAAgB,CAAC,IAAI,CAAC;IACtBC,IAAI,CAAC6C,WAAW,CAAC,CAAC;IAClBhD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMuC,gBAAgB,GAAI9B,MAAc,IAAK;IAC3CP,gBAAgB,CAACO,MAAM,CAAC;IACxBN,IAAI,CAAC8C,cAAc,CAACxC,MAAM,CAAC;IAC3BT,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMsC,gBAAgB,GAAG,MAAO7B,MAAc,IAAK;IACjD,IAAI;MAAA,IAAAyC,YAAA;MACF;MACA,MAAMC,gBAAgB,GAAG,MAAMvE,SAAS,CAACwE,cAAc,CAAC3C,MAAM,CAACkC,EAAE,CAAC;MAElE1E,KAAK,CAACoF,IAAI,CAAC;QACTrC,KAAK,EAAE,WAAWP,MAAM,CAAC6C,IAAI,EAAE;QAC/BC,OAAO,eACLlE,OAAA;UAAAiC,QAAA,gBACEjC,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAAC+C,IAAI,EAAC,GAAC,EAAC/C,MAAM,CAACmB,IAAI;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDrC,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAACgD,QAAQ;UAAA;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CrC,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAACiD,SAAS;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDrC,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAACkD,WAAW;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDrC,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,GAAAwB,YAAA,GAACzC,MAAM,CAACoB,IAAI,cAAAqB,YAAA,uBAAXA,YAAA,CAAaU,IAAI,CAAC,IAAI,CAAC;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDrC,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvBrC,OAAA,CAACrB,GAAG;cAAC2D,KAAK,EAAEwB,gBAAgB,CAACU,OAAO,GAAG,OAAO,GAAG,KAAM;cAAAvC,QAAA,EACpD6B,gBAAgB,CAACU,OAAO,GAAG,MAAM,GAAG;YAAM;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACHyB,gBAAgB,CAACU,OAAO,iBACvBxE,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACyB,gBAAgB,CAACW,OAAO,EAAC,IAAE;UAAA;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACxD,eACDrC,OAAA;YAAAiC,QAAA,gBAAGjC,OAAA;cAAAiC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIqC,IAAI,CAACtD,MAAM,CAACuD,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CACN;QACDwC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,WAAW,CAAC;IAC5B;EACF,CAAC;EAED,MAAM6B,kBAAkB,GAAG,MAAOC,EAAU,IAAK;IAC/C,IAAI;MACF,MAAM/D,SAAS,CAACuF,YAAY,CAACxB,EAAE,CAAC;MAChC/C,UAAU,CAACD,OAAO,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC5CrE,OAAO,CAACuF,OAAO,CAAC,SAAS,CAAC;IAC5B,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,SAAS,CAAC;IAC1B;EACF,CAAC;EAED,MAAM2B,sBAAsB,GAAG,MAAO/B,MAAc,IAAK;IACvD,IAAI;MACF,IAAIA,MAAM,CAACuB,kBAAkB,EAAE;QAC7B,MAAMpD,SAAS,CAAC0F,cAAc,CAAC7D,MAAM,CAACkC,EAAE,CAAC;MAC3C,CAAC,MAAM;QACL,MAAM/D,SAAS,CAAC2F,eAAe,CAAC9D,MAAM,CAACkC,EAAE,CAAC;MAC5C;MAEA,MAAM6B,cAAc,GAAG7E,OAAO,CAACa,GAAG,CAAC6D,CAAC,IAClCA,CAAC,CAAC1B,EAAE,KAAKlC,MAAM,CAACkC,EAAE,GACd;QAAE,GAAG0B,CAAC;QAAErC,kBAAkB,EAAE,CAACqC,CAAC,CAACrC;MAAmB,CAAC,GACnDqC,CACN,CAAC;MACDzE,UAAU,CAAC4E,cAAc,CAAC;MAC1BlG,OAAO,CAACuF,OAAO,CACb,GAAGpD,MAAM,CAAC6C,IAAI,OAAO7C,MAAM,CAACuB,kBAAkB,GAAG,IAAI,GAAG,IAAI,EAC9D,CAAC;IACH,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAM4D,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvE,IAAI,CAACwE,cAAc,CAAC,CAAC;MAE1C,IAAI1E,aAAa,EAAE;QACjB;QACA,MAAMrB,SAAS,CAACgG,YAAY,CAAC3E,aAAa,CAAC0C,EAAE,EAAE+B,MAAM,CAAC;QACtD,MAAMrE,WAAW,CAAC,CAAC,CAAC,CAAC;QACrB/B,OAAO,CAACuF,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAC,MAAM;QACL;QACA,MAAMjF,SAAS,CAACiG,YAAY,CAACH,MAAM,CAAC;QACpC,MAAMrE,WAAW,CAAC,CAAC,CAAC,CAAC;QACrB/B,OAAO,CAACuF,OAAO,CAAC,SAAS,CAAC;MAC5B;MAEA7D,eAAe,CAAC,KAAK,CAAC;MACtBG,IAAI,CAAC6C,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAMiE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9E,eAAe,CAAC,KAAK,CAAC;IACtBG,IAAI,CAAC6C,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,MAAM+B,WAAW,GAAGpF,OAAO,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3D,MAAM,KAAK,QAAQ,CAAC,CAACsE,MAAM;EACrE,MAAMC,UAAU,GAAGtF,OAAO,CAACqF,MAAM;EACjC,MAAME,eAAe,GAAGvF,OAAO,CAACyE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,kBAAkB,CAAC,CAACgD,MAAM;EAExE,oBACE3F,OAAA;IAAAiC,QAAA,gBACEjC,OAAA,CAACZ,GAAG;MAAC0G,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAA/D,QAAA,gBAC3CjC,OAAA,CAACX,GAAG;QAAC4G,IAAI,EAAE,CAAE;QAAAhE,QAAA,eACXjC,OAAA,CAACb,IAAI;UAAA8C,QAAA,eACHjC,OAAA,CAACV,SAAS;YACRqC,KAAK,EAAC,gCAAO;YACbuE,KAAK,EAAEN,UAAW;YAClBO,UAAU,EAAE;cAAE7D,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACX,GAAG;QAAC4G,IAAI,EAAE,CAAE;QAAAhE,QAAA,eACXjC,OAAA,CAACb,IAAI;UAAA8C,QAAA,eACHjC,OAAA,CAACV,SAAS;YACRqC,KAAK,EAAC,gCAAO;YACbuE,KAAK,EAAER,WAAY;YACnBS,UAAU,EAAE;cAAE7D,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACX,GAAG;QAAC4G,IAAI,EAAE,CAAE;QAAAhE,QAAA,eACXjC,OAAA,CAACb,IAAI;UAAA8C,QAAA,eACHjC,OAAA,CAACV,SAAS;YACRqC,KAAK,EAAC,oBAAK;YACXuE,KAAK,EAAEL,eAAgB;YACvBM,UAAU,EAAE;cAAE7D,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACX,GAAG;QAAC4G,IAAI,EAAE,CAAE;QAAAhE,QAAA,eACXjC,OAAA,CAACb,IAAI;UAAA8C,QAAA,eACHjC,OAAA,CAACV,SAAS;YACRqC,KAAK,EAAC,oBAAK;YACXuE,KAAK,EAAEN,UAAU,GAAG,CAAC,GAAGtE,IAAI,CAAC8E,KAAK,CAAEV,WAAW,GAAGE,UAAU,GAAI,GAAG,CAAC,GAAG,CAAE;YACzES,MAAM,EAAC,GAAG;YACVF,UAAU,EAAE;cAAE7D,KAAK,EAAEoD,WAAW,GAAGE,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrC,OAAA,CAACb,IAAI;MACHwC,KAAK,EAAC,gCAAO;MACb2E,KAAK,eACHtG,OAAA,CAACtB,KAAK;QAAAuD,QAAA,gBACJjC,OAAA,CAACvB,MAAM;UAACsE,IAAI,eAAE/C,OAAA,CAACH,cAAc;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACW,OAAO,EAAEA,CAAA,KAAMvC,UAAU,CAAC,IAAI,CAAE;UAAAwB,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA,CAACvB,MAAM;UAACqE,IAAI,EAAC,SAAS;UAACC,IAAI,eAAE/C,OAAA,CAACR,YAAY;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACW,OAAO,EAAEU,eAAgB;UAAAzB,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAEDjC,OAAA,CAACxB,KAAK;QACJkD,OAAO,EAAEA,OAAQ;QACjB6E,UAAU,EAAEjG,OAAQ;QACpBkG,MAAM,EAAC,IAAI;QACXhG,OAAO,EAAEA,OAAQ;QACjBiG,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPrC,OAAA,CAACpB,KAAK;MACJ+C,KAAK,EAAEf,aAAa,GAAG,OAAO,GAAG,OAAQ;MACzCmG,IAAI,EAAErG,YAAa;MACnBsG,IAAI,EAAE5B,aAAc;MACpB6B,QAAQ,EAAExB,iBAAkB;MAC5BZ,KAAK,EAAE,GAAI;MAAA5C,QAAA,eAEXjC,OAAA,CAACnB,IAAI;QACHiC,IAAI,EAAEA,IAAK;QACXoG,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb5E,IAAI,EAAE,EAAE;UACR8B,SAAS,EAAE,UAAU;UACrB1B,kBAAkB,EAAE,IAAI;UACxBC,aAAa,EAAE;QACjB,CAAE;QAAAX,QAAA,gBAEFjC,OAAA,CAACZ,GAAG;UAAC0G,MAAM,EAAE,EAAG;UAAA7D,QAAA,gBACdjC,OAAA,CAACX,GAAG;YAAC4G,IAAI,EAAE,EAAG;YAAAhE,QAAA,eACZjC,OAAA,CAACnB,IAAI,CAACuI,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbpD,IAAI,EAAC,MAAM;cACXqD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtI,OAAO,EAAE;cAAW,CAAC,CAAE;cAAAgD,QAAA,eAEjDjC,OAAA,CAAClB,KAAK;gBAAC0I,WAAW,EAAC;cAAU;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrC,OAAA,CAACX,GAAG;YAAC4G,IAAI,EAAE,EAAG;YAAAhE,QAAA,eACZjC,OAAA,CAACnB,IAAI,CAACuI,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbpD,IAAI,EAAC,MAAM;cACXqD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtI,OAAO,EAAE;cAAW,CAAC,CAAE;cAAAgD,QAAA,eAEjDjC,OAAA,CAAClB,KAAK;gBAAC0I,WAAW,EAAC;cAAY;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA,CAACZ,GAAG;UAAC0G,MAAM,EAAE,EAAG;UAAA7D,QAAA,gBACdjC,OAAA,CAACX,GAAG;YAAC4G,IAAI,EAAE,CAAE;YAAAhE,QAAA,eACXjC,OAAA,CAACnB,IAAI,CAACuI,IAAI;cACRC,KAAK,EAAC,cAAI;cACVpD,IAAI,EAAC,MAAM;cACXqD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtI,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAgD,QAAA,eAE/CjC,OAAA,CAAClB,KAAK;gBAACgE,IAAI,EAAC,QAAQ;gBAAC0E,WAAW,EAAC;cAAI;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrC,OAAA,CAACX,GAAG;YAAC4G,IAAI,EAAE,CAAE;YAAAhE,QAAA,eACXjC,OAAA,CAACnB,IAAI,CAACuI,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXpD,IAAI,EAAC,UAAU;cACfqD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtI,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAgD,QAAA,eAE/CjC,OAAA,CAAClB,KAAK;gBAAC0I,WAAW,EAAC;cAAM;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrC,OAAA,CAACX,GAAG;YAAC4G,IAAI,EAAE,CAAE;YAAAhE,QAAA,eACXjC,OAAA,CAACnB,IAAI,CAACuI,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZpD,IAAI,EAAC,WAAW;cAChBqD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtI,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAgD,QAAA,eAEhDjC,OAAA,CAACjB,MAAM;gBAAAkD,QAAA,gBACLjC,OAAA,CAACG,MAAM;kBAAC+F,KAAK,EAAC,UAAU;kBAAAjE,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCrC,OAAA,CAACG,MAAM;kBAAC+F,KAAK,EAAC,KAAK;kBAAAjE,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrC,OAAA,CAACnB,IAAI,CAACuI,IAAI;UACRC,KAAK,EAAC,cAAI;UACVpD,IAAI,EAAC,aAAa;UAAAhC,QAAA,eAElBjC,OAAA,CAAClB,KAAK,CAAC2I,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACF,WAAW,EAAC;UAAU;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAEZrC,OAAA,CAACZ,GAAG;UAAC0G,MAAM,EAAE,EAAG;UAAA7D,QAAA,gBACdjC,OAAA,CAACX,GAAG;YAAC4G,IAAI,EAAE,EAAG;YAAAhE,QAAA,eACZjC,OAAA,CAACnB,IAAI,CAACuI,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZpD,IAAI,EAAC,oBAAoB;cACzB0D,aAAa,EAAC,SAAS;cAAA1F,QAAA,eAEvBjC,OAAA,CAAChB,MAAM;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrC,OAAA,CAACX,GAAG;YAAC4G,IAAI,EAAE,EAAG;YAAAhE,QAAA,eACZjC,OAAA,CAACnB,IAAI,CAACuI,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZpD,IAAI,EAAC,eAAe;cACpB0D,aAAa,EAAC,SAAS;cAAA1F,QAAA,eAEvBjC,OAAA,CAAChB,MAAM;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChC,EAAA,CA3ZID,gBAA0B;EAAA,QAKfvB,IAAI,CAACkC,OAAO;AAAA;AAAA6G,EAAA,GALvBxH,gBAA0B;AA6ZhC,eAAeA,gBAAgB;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}