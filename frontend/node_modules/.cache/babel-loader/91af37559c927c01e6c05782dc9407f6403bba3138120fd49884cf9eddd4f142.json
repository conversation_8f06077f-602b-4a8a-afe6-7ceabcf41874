{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"height\", \"itemHeight\", \"fullHeight\", \"style\", \"data\", \"children\", \"itemKey\", \"virtual\", \"direction\", \"scrollWidth\", \"component\", \"onScroll\", \"onVirtualScroll\", \"onVisibleChange\", \"innerProps\", \"extraRender\", \"styles\", \"showScrollBar\"];\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { useEvent } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport { flushSync } from 'react-dom';\nimport Filler from \"./Filler\";\nimport useChildren from \"./hooks/useChildren\";\nimport useDiffItem from \"./hooks/useDiffItem\";\nimport useFrameWheel from \"./hooks/useFrameWheel\";\nimport { useGetSize } from \"./hooks/useGetSize\";\nimport useHeights from \"./hooks/useHeights\";\nimport useMobileTouchMove from \"./hooks/useMobileTouchMove\";\nimport useOriginScroll from \"./hooks/useOriginScroll\";\nimport useScrollDrag from \"./hooks/useScrollDrag\";\nimport useScrollTo from \"./hooks/useScrollTo\";\nimport ScrollBar from \"./ScrollBar\";\nimport { getSpinSize } from \"./utils/scrollbarUtil\";\nvar EMPTY_DATA = [];\nvar ScrollStyle = {\n  overflowY: 'auto',\n  overflowAnchor: 'none'\n};\nexport function RawList(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-virtual-list' : _props$prefixCls,\n    className = props.className,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    _props$fullHeight = props.fullHeight,\n    fullHeight = _props$fullHeight === void 0 ? true : _props$fullHeight,\n    style = props.style,\n    data = props.data,\n    children = props.children,\n    itemKey = props.itemKey,\n    virtual = props.virtual,\n    direction = props.direction,\n    scrollWidth = props.scrollWidth,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    onScroll = props.onScroll,\n    onVirtualScroll = props.onVirtualScroll,\n    onVisibleChange = props.onVisibleChange,\n    innerProps = props.innerProps,\n    extraRender = props.extraRender,\n    styles = props.styles,\n    _props$showScrollBar = props.showScrollBar,\n    showScrollBar = _props$showScrollBar === void 0 ? 'optional' : _props$showScrollBar,\n    restProps = _objectWithoutProperties(props, _excluded);\n\n  // =============================== Item Key ===============================\n  var getKey = React.useCallback(function (item) {\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return item === null || item === void 0 ? void 0 : item[itemKey];\n  }, [itemKey]);\n\n  // ================================ Height ================================\n  var _useHeights = useHeights(getKey, null, null),\n    _useHeights2 = _slicedToArray(_useHeights, 4),\n    setInstanceRef = _useHeights2[0],\n    collectHeight = _useHeights2[1],\n    heights = _useHeights2[2],\n    heightUpdatedMark = _useHeights2[3];\n\n  // ================================= MISC =================================\n  var useVirtual = !!(virtual !== false && height && itemHeight);\n  var containerHeight = React.useMemo(function () {\n    return Object.values(heights.maps).reduce(function (total, curr) {\n      return total + curr;\n    }, 0);\n  }, [heights.id, heights.maps]);\n  var inVirtual = useVirtual && data && (Math.max(itemHeight * data.length, containerHeight) > height || !!scrollWidth);\n  var isRTL = direction === 'rtl';\n  var mergedClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), isRTL), className);\n  var mergedData = data || EMPTY_DATA;\n  var componentRef = useRef();\n  var fillerInnerRef = useRef();\n  var containerRef = useRef();\n\n  // =============================== Item Key ===============================\n\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    offsetTop = _useState2[0],\n    setOffsetTop = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    offsetLeft = _useState4[0],\n    setOffsetLeft = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    scrollMoving = _useState6[0],\n    setScrollMoving = _useState6[1];\n  var onScrollbarStartMove = function onScrollbarStartMove() {\n    setScrollMoving(true);\n  };\n  var onScrollbarStopMove = function onScrollbarStopMove() {\n    setScrollMoving(false);\n  };\n  var sharedConfig = {\n    getKey: getKey\n  };\n\n  // ================================ Scroll ================================\n  function syncScrollTop(newTop) {\n    setOffsetTop(function (origin) {\n      var value;\n      if (typeof newTop === 'function') {\n        value = newTop(origin);\n      } else {\n        value = newTop;\n      }\n      var alignedTop = keepInRange(value);\n      componentRef.current.scrollTop = alignedTop;\n      return alignedTop;\n    });\n  }\n\n  // ================================ Legacy ================================\n  // Put ref here since the range is generate by follow\n  var rangeRef = useRef({\n    start: 0,\n    end: mergedData.length\n  });\n  var diffItemRef = useRef();\n  var _useDiffItem = useDiffItem(mergedData, getKey),\n    _useDiffItem2 = _slicedToArray(_useDiffItem, 1),\n    diffItem = _useDiffItem2[0];\n  diffItemRef.current = diffItem;\n\n  // ========================== Visible Calculation =========================\n  var _React$useMemo = React.useMemo(function () {\n      if (!useVirtual) {\n        return {\n          scrollHeight: undefined,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n\n      // Always use virtual scroll bar in avoid shaking\n      if (!inVirtual) {\n        var _fillerInnerRef$curre;\n        return {\n          scrollHeight: ((_fillerInnerRef$curre = fillerInnerRef.current) === null || _fillerInnerRef$curre === void 0 ? void 0 : _fillerInnerRef$curre.offsetHeight) || 0,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n      var itemTop = 0;\n      var startIndex;\n      var startOffset;\n      var endIndex;\n      var dataLen = mergedData.length;\n      for (var i = 0; i < dataLen; i += 1) {\n        var _item = mergedData[i];\n        var key = getKey(_item);\n        var cacheHeight = heights.get(key);\n        var currentItemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n\n        // Check item top in the range\n        if (currentItemBottom >= offsetTop && startIndex === undefined) {\n          startIndex = i;\n          startOffset = itemTop;\n        }\n\n        // Check item bottom in the range. We will render additional one item for motion usage\n        if (currentItemBottom > offsetTop + height && endIndex === undefined) {\n          endIndex = i;\n        }\n        itemTop = currentItemBottom;\n      }\n\n      // When scrollTop at the end but data cut to small count will reach this\n      if (startIndex === undefined) {\n        startIndex = 0;\n        startOffset = 0;\n        endIndex = Math.ceil(height / itemHeight);\n      }\n      if (endIndex === undefined) {\n        endIndex = mergedData.length - 1;\n      }\n\n      // Give cache to improve scroll experience\n      endIndex = Math.min(endIndex + 1, mergedData.length - 1);\n      return {\n        scrollHeight: itemTop,\n        start: startIndex,\n        end: endIndex,\n        offset: startOffset\n      };\n    }, [inVirtual, useVirtual, offsetTop, mergedData, heightUpdatedMark, height]),\n    scrollHeight = _React$useMemo.scrollHeight,\n    start = _React$useMemo.start,\n    end = _React$useMemo.end,\n    fillerOffset = _React$useMemo.offset;\n  rangeRef.current.start = start;\n  rangeRef.current.end = end;\n\n  // When scroll up, first visible item get real height may not same as `itemHeight`,\n  // Which will make scroll jump.\n  // Let's sync scroll top to avoid jump\n  React.useLayoutEffect(function () {\n    var changedRecord = heights.getRecord();\n    if (changedRecord.size === 1) {\n      var recordKey = Array.from(changedRecord.keys())[0];\n      var prevCacheHeight = changedRecord.get(recordKey);\n\n      // Quick switch data may cause `start` not in `mergedData` anymore\n      var startItem = mergedData[start];\n      if (startItem && prevCacheHeight === undefined) {\n        var startIndexKey = getKey(startItem);\n        if (startIndexKey === recordKey) {\n          var realStartHeight = heights.get(recordKey);\n          var diffHeight = realStartHeight - itemHeight;\n          syncScrollTop(function (ori) {\n            return ori + diffHeight;\n          });\n        }\n      }\n    }\n    heights.resetRecord();\n  }, [scrollHeight]);\n\n  // ================================= Size =================================\n  var _React$useState = React.useState({\n      width: 0,\n      height: height\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    size = _React$useState2[0],\n    setSize = _React$useState2[1];\n  var onHolderResize = function onHolderResize(sizeInfo) {\n    setSize({\n      width: sizeInfo.offsetWidth,\n      height: sizeInfo.offsetHeight\n    });\n  };\n\n  // Hack on scrollbar to enable flash call\n  var verticalScrollBarRef = useRef();\n  var horizontalScrollBarRef = useRef();\n  var horizontalScrollBarSpinSize = React.useMemo(function () {\n    return getSpinSize(size.width, scrollWidth);\n  }, [size.width, scrollWidth]);\n  var verticalScrollBarSpinSize = React.useMemo(function () {\n    return getSpinSize(size.height, scrollHeight);\n  }, [size.height, scrollHeight]);\n\n  // =============================== In Range ===============================\n  var maxScrollHeight = scrollHeight - height;\n  var maxScrollHeightRef = useRef(maxScrollHeight);\n  maxScrollHeightRef.current = maxScrollHeight;\n  function keepInRange(newScrollTop) {\n    var newTop = newScrollTop;\n    if (!Number.isNaN(maxScrollHeightRef.current)) {\n      newTop = Math.min(newTop, maxScrollHeightRef.current);\n    }\n    newTop = Math.max(newTop, 0);\n    return newTop;\n  }\n  var isScrollAtTop = offsetTop <= 0;\n  var isScrollAtBottom = offsetTop >= maxScrollHeight;\n  var isScrollAtLeft = offsetLeft <= 0;\n  var isScrollAtRight = offsetLeft >= scrollWidth;\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight);\n\n  // ================================ Scroll ================================\n  var getVirtualScrollInfo = function getVirtualScrollInfo() {\n    return {\n      x: isRTL ? -offsetLeft : offsetLeft,\n      y: offsetTop\n    };\n  };\n  var lastVirtualScrollInfoRef = useRef(getVirtualScrollInfo());\n  var triggerScroll = useEvent(function (params) {\n    if (onVirtualScroll) {\n      var nextInfo = _objectSpread(_objectSpread({}, getVirtualScrollInfo()), params);\n\n      // Trigger when offset changed\n      if (lastVirtualScrollInfoRef.current.x !== nextInfo.x || lastVirtualScrollInfoRef.current.y !== nextInfo.y) {\n        onVirtualScroll(nextInfo);\n        lastVirtualScrollInfoRef.current = nextInfo;\n      }\n    }\n  });\n  function onScrollBar(newScrollOffset, horizontal) {\n    var newOffset = newScrollOffset;\n    if (horizontal) {\n      flushSync(function () {\n        setOffsetLeft(newOffset);\n      });\n      triggerScroll();\n    } else {\n      syncScrollTop(newOffset);\n    }\n  }\n\n  // When data size reduce. It may trigger native scroll event back to fit scroll position\n  function onFallbackScroll(e) {\n    var newScrollTop = e.currentTarget.scrollTop;\n    if (newScrollTop !== offsetTop) {\n      syncScrollTop(newScrollTop);\n    }\n\n    // Trigger origin onScroll\n    onScroll === null || onScroll === void 0 || onScroll(e);\n    triggerScroll();\n  }\n  var keepInHorizontalRange = function keepInHorizontalRange(nextOffsetLeft) {\n    var tmpOffsetLeft = nextOffsetLeft;\n    var max = !!scrollWidth ? scrollWidth - size.width : 0;\n    tmpOffsetLeft = Math.max(tmpOffsetLeft, 0);\n    tmpOffsetLeft = Math.min(tmpOffsetLeft, max);\n    return tmpOffsetLeft;\n  };\n  var onWheelDelta = useEvent(function (offsetXY, fromHorizontal) {\n    if (fromHorizontal) {\n      flushSync(function () {\n        setOffsetLeft(function (left) {\n          var nextOffsetLeft = left + (isRTL ? -offsetXY : offsetXY);\n          return keepInHorizontalRange(nextOffsetLeft);\n        });\n      });\n      triggerScroll();\n    } else {\n      syncScrollTop(function (top) {\n        var newTop = top + offsetXY;\n        return newTop;\n      });\n    }\n  });\n\n  // Since this added in global,should use ref to keep update\n  var _useFrameWheel = useFrameWheel(useVirtual, isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight, !!scrollWidth, onWheelDelta),\n    _useFrameWheel2 = _slicedToArray(_useFrameWheel, 2),\n    onRawWheel = _useFrameWheel2[0],\n    onFireFoxScroll = _useFrameWheel2[1];\n\n  // Mobile touch move\n  useMobileTouchMove(useVirtual, componentRef, function (isHorizontal, delta, smoothOffset, e) {\n    var event = e;\n    if (originScroll(isHorizontal, delta, smoothOffset)) {\n      return false;\n    }\n\n    // Fix nest List trigger TouchMove event\n    if (!event || !event._virtualHandled) {\n      if (event) {\n        event._virtualHandled = true;\n      }\n      onRawWheel({\n        preventDefault: function preventDefault() {},\n        deltaX: isHorizontal ? delta : 0,\n        deltaY: isHorizontal ? 0 : delta\n      });\n      return true;\n    }\n    return false;\n  });\n\n  // MouseDown drag for scroll\n  useScrollDrag(inVirtual, componentRef, function (offset) {\n    syncScrollTop(function (top) {\n      return top + offset;\n    });\n  });\n  useLayoutEffect(function () {\n    // Firefox only\n    function onMozMousePixelScroll(e) {\n      // scrolling at top/bottom limit\n      var scrollingUpAtTop = isScrollAtTop && e.detail < 0;\n      var scrollingDownAtBottom = isScrollAtBottom && e.detail > 0;\n      if (useVirtual && !scrollingUpAtTop && !scrollingDownAtBottom) {\n        e.preventDefault();\n      }\n    }\n    var componentEle = componentRef.current;\n    componentEle.addEventListener('wheel', onRawWheel, {\n      passive: false\n    });\n    componentEle.addEventListener('DOMMouseScroll', onFireFoxScroll, {\n      passive: true\n    });\n    componentEle.addEventListener('MozMousePixelScroll', onMozMousePixelScroll, {\n      passive: false\n    });\n    return function () {\n      componentEle.removeEventListener('wheel', onRawWheel);\n      componentEle.removeEventListener('DOMMouseScroll', onFireFoxScroll);\n      componentEle.removeEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n    };\n  }, [useVirtual, isScrollAtTop, isScrollAtBottom]);\n\n  // Sync scroll left\n  useLayoutEffect(function () {\n    if (scrollWidth) {\n      var newOffsetLeft = keepInHorizontalRange(offsetLeft);\n      setOffsetLeft(newOffsetLeft);\n      triggerScroll({\n        x: newOffsetLeft\n      });\n    }\n  }, [size.width, scrollWidth]);\n\n  // ================================= Ref ==================================\n  var delayHideScrollBar = function delayHideScrollBar() {\n    var _verticalScrollBarRef, _horizontalScrollBarR;\n    (_verticalScrollBarRef = verticalScrollBarRef.current) === null || _verticalScrollBarRef === void 0 || _verticalScrollBarRef.delayHidden();\n    (_horizontalScrollBarR = horizontalScrollBarRef.current) === null || _horizontalScrollBarR === void 0 || _horizontalScrollBarR.delayHidden();\n  };\n  var _scrollTo = useScrollTo(componentRef, mergedData, heights, itemHeight, getKey, function () {\n    return collectHeight(true);\n  }, syncScrollTop, delayHideScrollBar);\n  React.useImperativeHandle(ref, function () {\n    return {\n      nativeElement: containerRef.current,\n      getScrollInfo: getVirtualScrollInfo,\n      scrollTo: function scrollTo(config) {\n        function isPosScroll(arg) {\n          return arg && _typeof(arg) === 'object' && ('left' in arg || 'top' in arg);\n        }\n        if (isPosScroll(config)) {\n          // Scroll X\n          if (config.left !== undefined) {\n            setOffsetLeft(keepInHorizontalRange(config.left));\n          }\n\n          // Scroll Y\n          _scrollTo(config.top);\n        } else {\n          _scrollTo(config);\n        }\n      }\n    };\n  });\n\n  // ================================ Effect ================================\n  /** We need told outside that some list not rendered */\n  useLayoutEffect(function () {\n    if (onVisibleChange) {\n      var renderList = mergedData.slice(start, end + 1);\n      onVisibleChange(renderList, mergedData);\n    }\n  }, [start, end, mergedData]);\n\n  // ================================ Extra =================================\n  var getSize = useGetSize(mergedData, getKey, heights, itemHeight);\n  var extraContent = extraRender === null || extraRender === void 0 ? void 0 : extraRender({\n    start: start,\n    end: end,\n    virtual: inVirtual,\n    offsetX: offsetLeft,\n    offsetY: fillerOffset,\n    rtl: isRTL,\n    getSize: getSize\n  });\n\n  // ================================ Render ================================\n  var listChildren = useChildren(mergedData, start, end, scrollWidth, offsetLeft, setInstanceRef, children, sharedConfig);\n  var componentStyle = null;\n  if (height) {\n    componentStyle = _objectSpread(_defineProperty({}, fullHeight ? 'height' : 'maxHeight', height), ScrollStyle);\n    if (useVirtual) {\n      componentStyle.overflowY = 'hidden';\n      if (scrollWidth) {\n        componentStyle.overflowX = 'hidden';\n      }\n      if (scrollMoving) {\n        componentStyle.pointerEvents = 'none';\n      }\n    }\n  }\n  var containerProps = {};\n  if (isRTL) {\n    containerProps.dir = 'rtl';\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: containerRef,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      position: 'relative'\n    }),\n    className: mergedClassName\n  }, containerProps, restProps), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onHolderResize\n  }, /*#__PURE__*/React.createElement(Component, {\n    className: \"\".concat(prefixCls, \"-holder\"),\n    style: componentStyle,\n    ref: componentRef,\n    onScroll: onFallbackScroll,\n    onMouseEnter: delayHideScrollBar\n  }, /*#__PURE__*/React.createElement(Filler, {\n    prefixCls: prefixCls,\n    height: scrollHeight,\n    offsetX: offsetLeft,\n    offsetY: fillerOffset,\n    scrollWidth: scrollWidth,\n    onInnerResize: collectHeight,\n    ref: fillerInnerRef,\n    innerProps: innerProps,\n    rtl: isRTL,\n    extra: extraContent\n  }, listChildren))), inVirtual && scrollHeight > height && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: verticalScrollBarRef,\n    prefixCls: prefixCls,\n    scrollOffset: offsetTop,\n    scrollRange: scrollHeight,\n    rtl: isRTL,\n    onScroll: onScrollBar,\n    onStartMove: onScrollbarStartMove,\n    onStopMove: onScrollbarStopMove,\n    spinSize: verticalScrollBarSpinSize,\n    containerSize: size.height,\n    style: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBar,\n    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBarThumb,\n    showScrollBar: showScrollBar\n  }), inVirtual && scrollWidth > size.width && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: horizontalScrollBarRef,\n    prefixCls: prefixCls,\n    scrollOffset: offsetLeft,\n    scrollRange: scrollWidth,\n    rtl: isRTL,\n    onScroll: onScrollBar,\n    onStartMove: onScrollbarStartMove,\n    onStopMove: onScrollbarStopMove,\n    spinSize: horizontalScrollBarSpinSize,\n    containerSize: size.width,\n    horizontal: true,\n    style: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBar,\n    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBarThumb,\n    showScrollBar: showScrollBar\n  }));\n}\nvar List = /*#__PURE__*/React.forwardRef(RawList);\nList.displayName = 'List';\nexport default List;", "map": {"version": 3, "names": ["_extends", "_typeof", "_objectSpread", "_defineProperty", "_slicedToArray", "_objectWithoutProperties", "_excluded", "classNames", "ResizeObserver", "useEvent", "useLayoutEffect", "React", "useRef", "useState", "flushSync", "Filler", "useChildren", "useDiffItem", "useFrameWheel", "useGetSize", "useHeights", "useMobileTouchMove", "useOriginScroll", "useScrollDrag", "useScrollTo", "<PERSON><PERSON>Bar", "getSpinSize", "EMPTY_DATA", "ScrollStyle", "overflowY", "overflowAnchor", "RawList", "props", "ref", "_props$prefixCls", "prefixCls", "className", "height", "itemHeight", "_props$fullHeight", "fullHeight", "style", "data", "children", "itemKey", "virtual", "direction", "scrollWidth", "_props$component", "component", "Component", "onScroll", "onVirtualScroll", "onVisibleChange", "innerProps", "extraRender", "styles", "_props$showScrollBar", "showScrollBar", "restProps", "<PERSON><PERSON><PERSON>", "useCallback", "item", "_useHeights", "_useHeights2", "setInstanceRef", "collectHeight", "heights", "heightUpdatedMark", "useVirtual", "containerHeight", "useMemo", "Object", "values", "maps", "reduce", "total", "curr", "id", "inVirtual", "Math", "max", "length", "isRTL", "mergedClassName", "concat", "mergedData", "componentRef", "fillerInnerRef", "containerRef", "_useState", "_useState2", "offsetTop", "setOffsetTop", "_useState3", "_useState4", "offsetLeft", "setOffsetLeft", "_useState5", "_useState6", "scrollMoving", "setScrollMoving", "onScrollbarStartMove", "onScrollbarStopMove", "sharedConfig", "syncScrollTop", "newTop", "origin", "value", "alignedTop", "keepInRange", "current", "scrollTop", "rangeRef", "start", "end", "diffItemRef", "_useDiffItem", "_useDiffItem2", "diffItem", "_React$useMemo", "scrollHeight", "undefined", "offset", "_fillerInnerRef$curre", "offsetHeight", "itemTop", "startIndex", "startOffset", "endIndex", "dataLen", "i", "_item", "key", "cacheHeight", "get", "currentItemBottom", "ceil", "min", "fillerOffset", "changedRecord", "getRecord", "size", "<PERSON><PERSON>ey", "Array", "from", "keys", "prevCacheHeight", "startItem", "startIndexKey", "realStartHeight", "diffHeight", "ori", "resetRecord", "_React$useState", "width", "_React$useState2", "setSize", "onHolderResize", "sizeInfo", "offsetWidth", "verticalScrollBarRef", "horizontalScrollBarRef", "horizontalScrollBarSpinSize", "verticalScrollBarSpinSize", "maxScrollHeight", "maxScrollHeightRef", "newScrollTop", "Number", "isNaN", "isScrollAtTop", "isScrollAtBottom", "isScrollAtLeft", "isScrollAtRight", "originScroll", "getVirtualScrollInfo", "x", "y", "lastVirtualScrollInfoRef", "triggerScroll", "params", "nextInfo", "onScrollBar", "newScrollOffset", "horizontal", "newOffset", "onFallbackScroll", "e", "currentTarget", "keepInHorizontalRange", "nextOffsetLeft", "tmpOffsetLeft", "onWheelDelta", "offsetXY", "fromHorizontal", "left", "top", "_useFrameWheel", "_useFrameWheel2", "onRawWheel", "onFireFoxScroll", "isHorizontal", "delta", "smoothOffset", "event", "_virtualHandled", "preventDefault", "deltaX", "deltaY", "onMozMousePixelScroll", "scrollingUpAtTop", "detail", "scrollingDownAtBottom", "componentEle", "addEventListener", "passive", "removeEventListener", "newOffsetLeft", "delayHideScrollBar", "_verticalScrollBarRef", "_horizontalScrollBarR", "delayHidden", "_scrollTo", "useImperativeHandle", "nativeElement", "getScrollInfo", "scrollTo", "config", "isPosScroll", "arg", "renderList", "slice", "getSize", "extraContent", "offsetX", "offsetY", "rtl", "listC<PERSON><PERSON>n", "componentStyle", "overflowX", "pointerEvents", "containerProps", "dir", "createElement", "position", "onResize", "onMouseEnter", "onInnerResize", "extra", "scrollOffset", "scrollRange", "onStartMove", "onStopMove", "spinSize", "containerSize", "verticalScrollBar", "thumbStyle", "verticalScrollBarThumb", "horizontalScrollBar", "horizontalScrollBarThumb", "List", "forwardRef", "displayName"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-virtual-list/es/List.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"height\", \"itemHeight\", \"fullHeight\", \"style\", \"data\", \"children\", \"itemKey\", \"virtual\", \"direction\", \"scrollWidth\", \"component\", \"onScroll\", \"onVirtualScroll\", \"onVisibleChange\", \"innerProps\", \"extraRender\", \"styles\", \"showScrollBar\"];\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { useEvent } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { useRef, useState } from 'react';\nimport { flushSync } from 'react-dom';\nimport Filler from \"./Filler\";\nimport useChildren from \"./hooks/useChildren\";\nimport useDiffItem from \"./hooks/useDiffItem\";\nimport useFrameWheel from \"./hooks/useFrameWheel\";\nimport { useGetSize } from \"./hooks/useGetSize\";\nimport useHeights from \"./hooks/useHeights\";\nimport useMobileTouchMove from \"./hooks/useMobileTouchMove\";\nimport useOriginScroll from \"./hooks/useOriginScroll\";\nimport useScrollDrag from \"./hooks/useScrollDrag\";\nimport useScrollTo from \"./hooks/useScrollTo\";\nimport ScrollBar from \"./ScrollBar\";\nimport { getSpinSize } from \"./utils/scrollbarUtil\";\nvar EMPTY_DATA = [];\nvar ScrollStyle = {\n  overflowY: 'auto',\n  overflowAnchor: 'none'\n};\nexport function RawList(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-virtual-list' : _props$prefixCls,\n    className = props.className,\n    height = props.height,\n    itemHeight = props.itemHeight,\n    _props$fullHeight = props.fullHeight,\n    fullHeight = _props$fullHeight === void 0 ? true : _props$fullHeight,\n    style = props.style,\n    data = props.data,\n    children = props.children,\n    itemKey = props.itemKey,\n    virtual = props.virtual,\n    direction = props.direction,\n    scrollWidth = props.scrollWidth,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    onScroll = props.onScroll,\n    onVirtualScroll = props.onVirtualScroll,\n    onVisibleChange = props.onVisibleChange,\n    innerProps = props.innerProps,\n    extraRender = props.extraRender,\n    styles = props.styles,\n    _props$showScrollBar = props.showScrollBar,\n    showScrollBar = _props$showScrollBar === void 0 ? 'optional' : _props$showScrollBar,\n    restProps = _objectWithoutProperties(props, _excluded);\n\n  // =============================== Item Key ===============================\n  var getKey = React.useCallback(function (item) {\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return item === null || item === void 0 ? void 0 : item[itemKey];\n  }, [itemKey]);\n\n  // ================================ Height ================================\n  var _useHeights = useHeights(getKey, null, null),\n    _useHeights2 = _slicedToArray(_useHeights, 4),\n    setInstanceRef = _useHeights2[0],\n    collectHeight = _useHeights2[1],\n    heights = _useHeights2[2],\n    heightUpdatedMark = _useHeights2[3];\n\n  // ================================= MISC =================================\n  var useVirtual = !!(virtual !== false && height && itemHeight);\n  var containerHeight = React.useMemo(function () {\n    return Object.values(heights.maps).reduce(function (total, curr) {\n      return total + curr;\n    }, 0);\n  }, [heights.id, heights.maps]);\n  var inVirtual = useVirtual && data && (Math.max(itemHeight * data.length, containerHeight) > height || !!scrollWidth);\n  var isRTL = direction === 'rtl';\n  var mergedClassName = classNames(prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), isRTL), className);\n  var mergedData = data || EMPTY_DATA;\n  var componentRef = useRef();\n  var fillerInnerRef = useRef();\n  var containerRef = useRef();\n\n  // =============================== Item Key ===============================\n\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    offsetTop = _useState2[0],\n    setOffsetTop = _useState2[1];\n  var _useState3 = useState(0),\n    _useState4 = _slicedToArray(_useState3, 2),\n    offsetLeft = _useState4[0],\n    setOffsetLeft = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    scrollMoving = _useState6[0],\n    setScrollMoving = _useState6[1];\n  var onScrollbarStartMove = function onScrollbarStartMove() {\n    setScrollMoving(true);\n  };\n  var onScrollbarStopMove = function onScrollbarStopMove() {\n    setScrollMoving(false);\n  };\n  var sharedConfig = {\n    getKey: getKey\n  };\n\n  // ================================ Scroll ================================\n  function syncScrollTop(newTop) {\n    setOffsetTop(function (origin) {\n      var value;\n      if (typeof newTop === 'function') {\n        value = newTop(origin);\n      } else {\n        value = newTop;\n      }\n      var alignedTop = keepInRange(value);\n      componentRef.current.scrollTop = alignedTop;\n      return alignedTop;\n    });\n  }\n\n  // ================================ Legacy ================================\n  // Put ref here since the range is generate by follow\n  var rangeRef = useRef({\n    start: 0,\n    end: mergedData.length\n  });\n  var diffItemRef = useRef();\n  var _useDiffItem = useDiffItem(mergedData, getKey),\n    _useDiffItem2 = _slicedToArray(_useDiffItem, 1),\n    diffItem = _useDiffItem2[0];\n  diffItemRef.current = diffItem;\n\n  // ========================== Visible Calculation =========================\n  var _React$useMemo = React.useMemo(function () {\n      if (!useVirtual) {\n        return {\n          scrollHeight: undefined,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n\n      // Always use virtual scroll bar in avoid shaking\n      if (!inVirtual) {\n        var _fillerInnerRef$curre;\n        return {\n          scrollHeight: ((_fillerInnerRef$curre = fillerInnerRef.current) === null || _fillerInnerRef$curre === void 0 ? void 0 : _fillerInnerRef$curre.offsetHeight) || 0,\n          start: 0,\n          end: mergedData.length - 1,\n          offset: undefined\n        };\n      }\n      var itemTop = 0;\n      var startIndex;\n      var startOffset;\n      var endIndex;\n      var dataLen = mergedData.length;\n      for (var i = 0; i < dataLen; i += 1) {\n        var _item = mergedData[i];\n        var key = getKey(_item);\n        var cacheHeight = heights.get(key);\n        var currentItemBottom = itemTop + (cacheHeight === undefined ? itemHeight : cacheHeight);\n\n        // Check item top in the range\n        if (currentItemBottom >= offsetTop && startIndex === undefined) {\n          startIndex = i;\n          startOffset = itemTop;\n        }\n\n        // Check item bottom in the range. We will render additional one item for motion usage\n        if (currentItemBottom > offsetTop + height && endIndex === undefined) {\n          endIndex = i;\n        }\n        itemTop = currentItemBottom;\n      }\n\n      // When scrollTop at the end but data cut to small count will reach this\n      if (startIndex === undefined) {\n        startIndex = 0;\n        startOffset = 0;\n        endIndex = Math.ceil(height / itemHeight);\n      }\n      if (endIndex === undefined) {\n        endIndex = mergedData.length - 1;\n      }\n\n      // Give cache to improve scroll experience\n      endIndex = Math.min(endIndex + 1, mergedData.length - 1);\n      return {\n        scrollHeight: itemTop,\n        start: startIndex,\n        end: endIndex,\n        offset: startOffset\n      };\n    }, [inVirtual, useVirtual, offsetTop, mergedData, heightUpdatedMark, height]),\n    scrollHeight = _React$useMemo.scrollHeight,\n    start = _React$useMemo.start,\n    end = _React$useMemo.end,\n    fillerOffset = _React$useMemo.offset;\n  rangeRef.current.start = start;\n  rangeRef.current.end = end;\n\n  // When scroll up, first visible item get real height may not same as `itemHeight`,\n  // Which will make scroll jump.\n  // Let's sync scroll top to avoid jump\n  React.useLayoutEffect(function () {\n    var changedRecord = heights.getRecord();\n    if (changedRecord.size === 1) {\n      var recordKey = Array.from(changedRecord.keys())[0];\n      var prevCacheHeight = changedRecord.get(recordKey);\n\n      // Quick switch data may cause `start` not in `mergedData` anymore\n      var startItem = mergedData[start];\n      if (startItem && prevCacheHeight === undefined) {\n        var startIndexKey = getKey(startItem);\n        if (startIndexKey === recordKey) {\n          var realStartHeight = heights.get(recordKey);\n          var diffHeight = realStartHeight - itemHeight;\n          syncScrollTop(function (ori) {\n            return ori + diffHeight;\n          });\n        }\n      }\n    }\n    heights.resetRecord();\n  }, [scrollHeight]);\n\n  // ================================= Size =================================\n  var _React$useState = React.useState({\n      width: 0,\n      height: height\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    size = _React$useState2[0],\n    setSize = _React$useState2[1];\n  var onHolderResize = function onHolderResize(sizeInfo) {\n    setSize({\n      width: sizeInfo.offsetWidth,\n      height: sizeInfo.offsetHeight\n    });\n  };\n\n  // Hack on scrollbar to enable flash call\n  var verticalScrollBarRef = useRef();\n  var horizontalScrollBarRef = useRef();\n  var horizontalScrollBarSpinSize = React.useMemo(function () {\n    return getSpinSize(size.width, scrollWidth);\n  }, [size.width, scrollWidth]);\n  var verticalScrollBarSpinSize = React.useMemo(function () {\n    return getSpinSize(size.height, scrollHeight);\n  }, [size.height, scrollHeight]);\n\n  // =============================== In Range ===============================\n  var maxScrollHeight = scrollHeight - height;\n  var maxScrollHeightRef = useRef(maxScrollHeight);\n  maxScrollHeightRef.current = maxScrollHeight;\n  function keepInRange(newScrollTop) {\n    var newTop = newScrollTop;\n    if (!Number.isNaN(maxScrollHeightRef.current)) {\n      newTop = Math.min(newTop, maxScrollHeightRef.current);\n    }\n    newTop = Math.max(newTop, 0);\n    return newTop;\n  }\n  var isScrollAtTop = offsetTop <= 0;\n  var isScrollAtBottom = offsetTop >= maxScrollHeight;\n  var isScrollAtLeft = offsetLeft <= 0;\n  var isScrollAtRight = offsetLeft >= scrollWidth;\n  var originScroll = useOriginScroll(isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight);\n\n  // ================================ Scroll ================================\n  var getVirtualScrollInfo = function getVirtualScrollInfo() {\n    return {\n      x: isRTL ? -offsetLeft : offsetLeft,\n      y: offsetTop\n    };\n  };\n  var lastVirtualScrollInfoRef = useRef(getVirtualScrollInfo());\n  var triggerScroll = useEvent(function (params) {\n    if (onVirtualScroll) {\n      var nextInfo = _objectSpread(_objectSpread({}, getVirtualScrollInfo()), params);\n\n      // Trigger when offset changed\n      if (lastVirtualScrollInfoRef.current.x !== nextInfo.x || lastVirtualScrollInfoRef.current.y !== nextInfo.y) {\n        onVirtualScroll(nextInfo);\n        lastVirtualScrollInfoRef.current = nextInfo;\n      }\n    }\n  });\n  function onScrollBar(newScrollOffset, horizontal) {\n    var newOffset = newScrollOffset;\n    if (horizontal) {\n      flushSync(function () {\n        setOffsetLeft(newOffset);\n      });\n      triggerScroll();\n    } else {\n      syncScrollTop(newOffset);\n    }\n  }\n\n  // When data size reduce. It may trigger native scroll event back to fit scroll position\n  function onFallbackScroll(e) {\n    var newScrollTop = e.currentTarget.scrollTop;\n    if (newScrollTop !== offsetTop) {\n      syncScrollTop(newScrollTop);\n    }\n\n    // Trigger origin onScroll\n    onScroll === null || onScroll === void 0 || onScroll(e);\n    triggerScroll();\n  }\n  var keepInHorizontalRange = function keepInHorizontalRange(nextOffsetLeft) {\n    var tmpOffsetLeft = nextOffsetLeft;\n    var max = !!scrollWidth ? scrollWidth - size.width : 0;\n    tmpOffsetLeft = Math.max(tmpOffsetLeft, 0);\n    tmpOffsetLeft = Math.min(tmpOffsetLeft, max);\n    return tmpOffsetLeft;\n  };\n  var onWheelDelta = useEvent(function (offsetXY, fromHorizontal) {\n    if (fromHorizontal) {\n      flushSync(function () {\n        setOffsetLeft(function (left) {\n          var nextOffsetLeft = left + (isRTL ? -offsetXY : offsetXY);\n          return keepInHorizontalRange(nextOffsetLeft);\n        });\n      });\n      triggerScroll();\n    } else {\n      syncScrollTop(function (top) {\n        var newTop = top + offsetXY;\n        return newTop;\n      });\n    }\n  });\n\n  // Since this added in global,should use ref to keep update\n  var _useFrameWheel = useFrameWheel(useVirtual, isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight, !!scrollWidth, onWheelDelta),\n    _useFrameWheel2 = _slicedToArray(_useFrameWheel, 2),\n    onRawWheel = _useFrameWheel2[0],\n    onFireFoxScroll = _useFrameWheel2[1];\n\n  // Mobile touch move\n  useMobileTouchMove(useVirtual, componentRef, function (isHorizontal, delta, smoothOffset, e) {\n    var event = e;\n    if (originScroll(isHorizontal, delta, smoothOffset)) {\n      return false;\n    }\n\n    // Fix nest List trigger TouchMove event\n    if (!event || !event._virtualHandled) {\n      if (event) {\n        event._virtualHandled = true;\n      }\n      onRawWheel({\n        preventDefault: function preventDefault() {},\n        deltaX: isHorizontal ? delta : 0,\n        deltaY: isHorizontal ? 0 : delta\n      });\n      return true;\n    }\n    return false;\n  });\n\n  // MouseDown drag for scroll\n  useScrollDrag(inVirtual, componentRef, function (offset) {\n    syncScrollTop(function (top) {\n      return top + offset;\n    });\n  });\n  useLayoutEffect(function () {\n    // Firefox only\n    function onMozMousePixelScroll(e) {\n      // scrolling at top/bottom limit\n      var scrollingUpAtTop = isScrollAtTop && e.detail < 0;\n      var scrollingDownAtBottom = isScrollAtBottom && e.detail > 0;\n      if (useVirtual && !scrollingUpAtTop && !scrollingDownAtBottom) {\n        e.preventDefault();\n      }\n    }\n    var componentEle = componentRef.current;\n    componentEle.addEventListener('wheel', onRawWheel, {\n      passive: false\n    });\n    componentEle.addEventListener('DOMMouseScroll', onFireFoxScroll, {\n      passive: true\n    });\n    componentEle.addEventListener('MozMousePixelScroll', onMozMousePixelScroll, {\n      passive: false\n    });\n    return function () {\n      componentEle.removeEventListener('wheel', onRawWheel);\n      componentEle.removeEventListener('DOMMouseScroll', onFireFoxScroll);\n      componentEle.removeEventListener('MozMousePixelScroll', onMozMousePixelScroll);\n    };\n  }, [useVirtual, isScrollAtTop, isScrollAtBottom]);\n\n  // Sync scroll left\n  useLayoutEffect(function () {\n    if (scrollWidth) {\n      var newOffsetLeft = keepInHorizontalRange(offsetLeft);\n      setOffsetLeft(newOffsetLeft);\n      triggerScroll({\n        x: newOffsetLeft\n      });\n    }\n  }, [size.width, scrollWidth]);\n\n  // ================================= Ref ==================================\n  var delayHideScrollBar = function delayHideScrollBar() {\n    var _verticalScrollBarRef, _horizontalScrollBarR;\n    (_verticalScrollBarRef = verticalScrollBarRef.current) === null || _verticalScrollBarRef === void 0 || _verticalScrollBarRef.delayHidden();\n    (_horizontalScrollBarR = horizontalScrollBarRef.current) === null || _horizontalScrollBarR === void 0 || _horizontalScrollBarR.delayHidden();\n  };\n  var _scrollTo = useScrollTo(componentRef, mergedData, heights, itemHeight, getKey, function () {\n    return collectHeight(true);\n  }, syncScrollTop, delayHideScrollBar);\n  React.useImperativeHandle(ref, function () {\n    return {\n      nativeElement: containerRef.current,\n      getScrollInfo: getVirtualScrollInfo,\n      scrollTo: function scrollTo(config) {\n        function isPosScroll(arg) {\n          return arg && _typeof(arg) === 'object' && ('left' in arg || 'top' in arg);\n        }\n        if (isPosScroll(config)) {\n          // Scroll X\n          if (config.left !== undefined) {\n            setOffsetLeft(keepInHorizontalRange(config.left));\n          }\n\n          // Scroll Y\n          _scrollTo(config.top);\n        } else {\n          _scrollTo(config);\n        }\n      }\n    };\n  });\n\n  // ================================ Effect ================================\n  /** We need told outside that some list not rendered */\n  useLayoutEffect(function () {\n    if (onVisibleChange) {\n      var renderList = mergedData.slice(start, end + 1);\n      onVisibleChange(renderList, mergedData);\n    }\n  }, [start, end, mergedData]);\n\n  // ================================ Extra =================================\n  var getSize = useGetSize(mergedData, getKey, heights, itemHeight);\n  var extraContent = extraRender === null || extraRender === void 0 ? void 0 : extraRender({\n    start: start,\n    end: end,\n    virtual: inVirtual,\n    offsetX: offsetLeft,\n    offsetY: fillerOffset,\n    rtl: isRTL,\n    getSize: getSize\n  });\n\n  // ================================ Render ================================\n  var listChildren = useChildren(mergedData, start, end, scrollWidth, offsetLeft, setInstanceRef, children, sharedConfig);\n  var componentStyle = null;\n  if (height) {\n    componentStyle = _objectSpread(_defineProperty({}, fullHeight ? 'height' : 'maxHeight', height), ScrollStyle);\n    if (useVirtual) {\n      componentStyle.overflowY = 'hidden';\n      if (scrollWidth) {\n        componentStyle.overflowX = 'hidden';\n      }\n      if (scrollMoving) {\n        componentStyle.pointerEvents = 'none';\n      }\n    }\n  }\n  var containerProps = {};\n  if (isRTL) {\n    containerProps.dir = 'rtl';\n  }\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: containerRef,\n    style: _objectSpread(_objectSpread({}, style), {}, {\n      position: 'relative'\n    }),\n    className: mergedClassName\n  }, containerProps, restProps), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onHolderResize\n  }, /*#__PURE__*/React.createElement(Component, {\n    className: \"\".concat(prefixCls, \"-holder\"),\n    style: componentStyle,\n    ref: componentRef,\n    onScroll: onFallbackScroll,\n    onMouseEnter: delayHideScrollBar\n  }, /*#__PURE__*/React.createElement(Filler, {\n    prefixCls: prefixCls,\n    height: scrollHeight,\n    offsetX: offsetLeft,\n    offsetY: fillerOffset,\n    scrollWidth: scrollWidth,\n    onInnerResize: collectHeight,\n    ref: fillerInnerRef,\n    innerProps: innerProps,\n    rtl: isRTL,\n    extra: extraContent\n  }, listChildren))), inVirtual && scrollHeight > height && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: verticalScrollBarRef,\n    prefixCls: prefixCls,\n    scrollOffset: offsetTop,\n    scrollRange: scrollHeight,\n    rtl: isRTL,\n    onScroll: onScrollBar,\n    onStartMove: onScrollbarStartMove,\n    onStopMove: onScrollbarStopMove,\n    spinSize: verticalScrollBarSpinSize,\n    containerSize: size.height,\n    style: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBar,\n    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.verticalScrollBarThumb,\n    showScrollBar: showScrollBar\n  }), inVirtual && scrollWidth > size.width && /*#__PURE__*/React.createElement(ScrollBar, {\n    ref: horizontalScrollBarRef,\n    prefixCls: prefixCls,\n    scrollOffset: offsetLeft,\n    scrollRange: scrollWidth,\n    rtl: isRTL,\n    onScroll: onScrollBar,\n    onStartMove: onScrollbarStartMove,\n    onStopMove: onScrollbarStopMove,\n    spinSize: horizontalScrollBarSpinSize,\n    containerSize: size.width,\n    horizontal: true,\n    style: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBar,\n    thumbStyle: styles === null || styles === void 0 ? void 0 : styles.horizontalScrollBarThumb,\n    showScrollBar: showScrollBar\n  }));\n}\nvar List = /*#__PURE__*/React.forwardRef(RawList);\nList.displayName = 'List';\nexport default List;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,eAAe,CAAC;AACtR,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,WAAW,QAAQ,uBAAuB;AACnD,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,WAAW,GAAG;EAChBC,SAAS,EAAE,MAAM;EACjBC,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAClC,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,gBAAgB;IAC9EE,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,iBAAiB,GAAGP,KAAK,CAACQ,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACpEE,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,IAAI,GAAGV,KAAK,CAACU,IAAI;IACjBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,OAAO,GAAGZ,KAAK,CAACY,OAAO;IACvBC,OAAO,GAAGb,KAAK,CAACa,OAAO;IACvBC,SAAS,GAAGd,KAAK,CAACc,SAAS;IAC3BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,gBAAgB,GAAGhB,KAAK,CAACiB,SAAS;IAClCC,SAAS,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEG,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ;IACzBC,eAAe,GAAGpB,KAAK,CAACoB,eAAe;IACvCC,eAAe,GAAGrB,KAAK,CAACqB,eAAe;IACvCC,UAAU,GAAGtB,KAAK,CAACsB,UAAU;IAC7BC,WAAW,GAAGvB,KAAK,CAACuB,WAAW;IAC/BC,MAAM,GAAGxB,KAAK,CAACwB,MAAM;IACrBC,oBAAoB,GAAGzB,KAAK,CAAC0B,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,oBAAoB;IACnFE,SAAS,GAAGtD,wBAAwB,CAAC2B,KAAK,EAAE1B,SAAS,CAAC;;EAExD;EACA,IAAIsD,MAAM,GAAGjD,KAAK,CAACkD,WAAW,CAAC,UAAUC,IAAI,EAAE;IAC7C,IAAI,OAAOlB,OAAO,KAAK,UAAU,EAAE;MACjC,OAAOA,OAAO,CAACkB,IAAI,CAAC;IACtB;IACA,OAAOA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAClB,OAAO,CAAC;EAClE,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;;EAEb;EACA,IAAImB,WAAW,GAAG3C,UAAU,CAACwC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAC9CI,YAAY,GAAG5D,cAAc,CAAC2D,WAAW,EAAE,CAAC,CAAC;IAC7CE,cAAc,GAAGD,YAAY,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,YAAY,CAAC,CAAC,CAAC;IAC/BG,OAAO,GAAGH,YAAY,CAAC,CAAC,CAAC;IACzBI,iBAAiB,GAAGJ,YAAY,CAAC,CAAC,CAAC;;EAErC;EACA,IAAIK,UAAU,GAAG,CAAC,EAAExB,OAAO,KAAK,KAAK,IAAIR,MAAM,IAAIC,UAAU,CAAC;EAC9D,IAAIgC,eAAe,GAAG3D,KAAK,CAAC4D,OAAO,CAAC,YAAY;IAC9C,OAAOC,MAAM,CAACC,MAAM,CAACN,OAAO,CAACO,IAAI,CAAC,CAACC,MAAM,CAAC,UAAUC,KAAK,EAAEC,IAAI,EAAE;MAC/D,OAAOD,KAAK,GAAGC,IAAI;IACrB,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAACV,OAAO,CAACW,EAAE,EAAEX,OAAO,CAACO,IAAI,CAAC,CAAC;EAC9B,IAAIK,SAAS,GAAGV,UAAU,IAAI3B,IAAI,KAAKsC,IAAI,CAACC,GAAG,CAAC3C,UAAU,GAAGI,IAAI,CAACwC,MAAM,EAAEZ,eAAe,CAAC,GAAGjC,MAAM,IAAI,CAAC,CAACU,WAAW,CAAC;EACrH,IAAIoC,KAAK,GAAGrC,SAAS,KAAK,KAAK;EAC/B,IAAIsC,eAAe,GAAG7E,UAAU,CAAC4B,SAAS,EAAEhC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACkF,MAAM,CAAClD,SAAS,EAAE,MAAM,CAAC,EAAEgD,KAAK,CAAC,EAAE/C,SAAS,CAAC;EAChH,IAAIkD,UAAU,GAAG5C,IAAI,IAAIf,UAAU;EACnC,IAAI4D,YAAY,GAAG3E,MAAM,CAAC,CAAC;EAC3B,IAAI4E,cAAc,GAAG5E,MAAM,CAAC,CAAC;EAC7B,IAAI6E,YAAY,GAAG7E,MAAM,CAAC,CAAC;;EAE3B;;EAEA,IAAI8E,SAAS,GAAG7E,QAAQ,CAAC,CAAC,CAAC;IACzB8E,UAAU,GAAGvF,cAAc,CAACsF,SAAS,EAAE,CAAC,CAAC;IACzCE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9B,IAAIG,UAAU,GAAGjF,QAAQ,CAAC,CAAC,CAAC;IAC1BkF,UAAU,GAAG3F,cAAc,CAAC0F,UAAU,EAAE,CAAC,CAAC;IAC1CE,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC/B,IAAIG,UAAU,GAAGrF,QAAQ,CAAC,KAAK,CAAC;IAC9BsF,UAAU,GAAG/F,cAAc,CAAC8F,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EACjC,IAAIG,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzDD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EACD,IAAIE,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;IACvDF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EACD,IAAIG,YAAY,GAAG;IACjB5C,MAAM,EAAEA;EACV,CAAC;;EAED;EACA,SAAS6C,aAAaA,CAACC,MAAM,EAAE;IAC7Bb,YAAY,CAAC,UAAUc,MAAM,EAAE;MAC7B,IAAIC,KAAK;MACT,IAAI,OAAOF,MAAM,KAAK,UAAU,EAAE;QAChCE,KAAK,GAAGF,MAAM,CAACC,MAAM,CAAC;MACxB,CAAC,MAAM;QACLC,KAAK,GAAGF,MAAM;MAChB;MACA,IAAIG,UAAU,GAAGC,WAAW,CAACF,KAAK,CAAC;MACnCrB,YAAY,CAACwB,OAAO,CAACC,SAAS,GAAGH,UAAU;MAC3C,OAAOA,UAAU;IACnB,CAAC,CAAC;EACJ;;EAEA;EACA;EACA,IAAII,QAAQ,GAAGrG,MAAM,CAAC;IACpBsG,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE7B,UAAU,CAACJ;EAClB,CAAC,CAAC;EACF,IAAIkC,WAAW,GAAGxG,MAAM,CAAC,CAAC;EAC1B,IAAIyG,YAAY,GAAGpG,WAAW,CAACqE,UAAU,EAAE1B,MAAM,CAAC;IAChD0D,aAAa,GAAGlH,cAAc,CAACiH,YAAY,EAAE,CAAC,CAAC;IAC/CE,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC;EAC7BF,WAAW,CAACL,OAAO,GAAGQ,QAAQ;;EAE9B;EACA,IAAIC,cAAc,GAAG7G,KAAK,CAAC4D,OAAO,CAAC,YAAY;MAC3C,IAAI,CAACF,UAAU,EAAE;QACf,OAAO;UACLoD,YAAY,EAAEC,SAAS;UACvBR,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE7B,UAAU,CAACJ,MAAM,GAAG,CAAC;UAC1ByC,MAAM,EAAED;QACV,CAAC;MACH;;MAEA;MACA,IAAI,CAAC3C,SAAS,EAAE;QACd,IAAI6C,qBAAqB;QACzB,OAAO;UACLH,YAAY,EAAE,CAAC,CAACG,qBAAqB,GAAGpC,cAAc,CAACuB,OAAO,MAAM,IAAI,IAAIa,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,YAAY,KAAK,CAAC;UAChKX,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE7B,UAAU,CAACJ,MAAM,GAAG,CAAC;UAC1ByC,MAAM,EAAED;QACV,CAAC;MACH;MACA,IAAII,OAAO,GAAG,CAAC;MACf,IAAIC,UAAU;MACd,IAAIC,WAAW;MACf,IAAIC,QAAQ;MACZ,IAAIC,OAAO,GAAG5C,UAAU,CAACJ,MAAM;MAC/B,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,EAAEC,CAAC,IAAI,CAAC,EAAE;QACnC,IAAIC,KAAK,GAAG9C,UAAU,CAAC6C,CAAC,CAAC;QACzB,IAAIE,GAAG,GAAGzE,MAAM,CAACwE,KAAK,CAAC;QACvB,IAAIE,WAAW,GAAGnE,OAAO,CAACoE,GAAG,CAACF,GAAG,CAAC;QAClC,IAAIG,iBAAiB,GAAGV,OAAO,IAAIQ,WAAW,KAAKZ,SAAS,GAAGpF,UAAU,GAAGgG,WAAW,CAAC;;QAExF;QACA,IAAIE,iBAAiB,IAAI5C,SAAS,IAAImC,UAAU,KAAKL,SAAS,EAAE;UAC9DK,UAAU,GAAGI,CAAC;UACdH,WAAW,GAAGF,OAAO;QACvB;;QAEA;QACA,IAAIU,iBAAiB,GAAG5C,SAAS,GAAGvD,MAAM,IAAI4F,QAAQ,KAAKP,SAAS,EAAE;UACpEO,QAAQ,GAAGE,CAAC;QACd;QACAL,OAAO,GAAGU,iBAAiB;MAC7B;;MAEA;MACA,IAAIT,UAAU,KAAKL,SAAS,EAAE;QAC5BK,UAAU,GAAG,CAAC;QACdC,WAAW,GAAG,CAAC;QACfC,QAAQ,GAAGjD,IAAI,CAACyD,IAAI,CAACpG,MAAM,GAAGC,UAAU,CAAC;MAC3C;MACA,IAAI2F,QAAQ,KAAKP,SAAS,EAAE;QAC1BO,QAAQ,GAAG3C,UAAU,CAACJ,MAAM,GAAG,CAAC;MAClC;;MAEA;MACA+C,QAAQ,GAAGjD,IAAI,CAAC0D,GAAG,CAACT,QAAQ,GAAG,CAAC,EAAE3C,UAAU,CAACJ,MAAM,GAAG,CAAC,CAAC;MACxD,OAAO;QACLuC,YAAY,EAAEK,OAAO;QACrBZ,KAAK,EAAEa,UAAU;QACjBZ,GAAG,EAAEc,QAAQ;QACbN,MAAM,EAAEK;MACV,CAAC;IACH,CAAC,EAAE,CAACjD,SAAS,EAAEV,UAAU,EAAEuB,SAAS,EAAEN,UAAU,EAAElB,iBAAiB,EAAE/B,MAAM,CAAC,CAAC;IAC7EoF,YAAY,GAAGD,cAAc,CAACC,YAAY;IAC1CP,KAAK,GAAGM,cAAc,CAACN,KAAK;IAC5BC,GAAG,GAAGK,cAAc,CAACL,GAAG;IACxBwB,YAAY,GAAGnB,cAAc,CAACG,MAAM;EACtCV,QAAQ,CAACF,OAAO,CAACG,KAAK,GAAGA,KAAK;EAC9BD,QAAQ,CAACF,OAAO,CAACI,GAAG,GAAGA,GAAG;;EAE1B;EACA;EACA;EACAxG,KAAK,CAACD,eAAe,CAAC,YAAY;IAChC,IAAIkI,aAAa,GAAGzE,OAAO,CAAC0E,SAAS,CAAC,CAAC;IACvC,IAAID,aAAa,CAACE,IAAI,KAAK,CAAC,EAAE;MAC5B,IAAIC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACL,aAAa,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnD,IAAIC,eAAe,GAAGP,aAAa,CAACL,GAAG,CAACQ,SAAS,CAAC;;MAElD;MACA,IAAIK,SAAS,GAAG9D,UAAU,CAAC4B,KAAK,CAAC;MACjC,IAAIkC,SAAS,IAAID,eAAe,KAAKzB,SAAS,EAAE;QAC9C,IAAI2B,aAAa,GAAGzF,MAAM,CAACwF,SAAS,CAAC;QACrC,IAAIC,aAAa,KAAKN,SAAS,EAAE;UAC/B,IAAIO,eAAe,GAAGnF,OAAO,CAACoE,GAAG,CAACQ,SAAS,CAAC;UAC5C,IAAIQ,UAAU,GAAGD,eAAe,GAAGhH,UAAU;UAC7CmE,aAAa,CAAC,UAAU+C,GAAG,EAAE;YAC3B,OAAOA,GAAG,GAAGD,UAAU;UACzB,CAAC,CAAC;QACJ;MACF;IACF;IACApF,OAAO,CAACsF,WAAW,CAAC,CAAC;EACvB,CAAC,EAAE,CAAChC,YAAY,CAAC,CAAC;;EAElB;EACA,IAAIiC,eAAe,GAAG/I,KAAK,CAACE,QAAQ,CAAC;MACjC8I,KAAK,EAAE,CAAC;MACRtH,MAAM,EAAEA;IACV,CAAC,CAAC;IACFuH,gBAAgB,GAAGxJ,cAAc,CAACsJ,eAAe,EAAE,CAAC,CAAC;IACrDZ,IAAI,GAAGc,gBAAgB,CAAC,CAAC,CAAC;IAC1BC,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAC/B,IAAIE,cAAc,GAAG,SAASA,cAAcA,CAACC,QAAQ,EAAE;IACrDF,OAAO,CAAC;MACNF,KAAK,EAAEI,QAAQ,CAACC,WAAW;MAC3B3H,MAAM,EAAE0H,QAAQ,CAAClC;IACnB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIoC,oBAAoB,GAAGrJ,MAAM,CAAC,CAAC;EACnC,IAAIsJ,sBAAsB,GAAGtJ,MAAM,CAAC,CAAC;EACrC,IAAIuJ,2BAA2B,GAAGxJ,KAAK,CAAC4D,OAAO,CAAC,YAAY;IAC1D,OAAO7C,WAAW,CAACoH,IAAI,CAACa,KAAK,EAAE5G,WAAW,CAAC;EAC7C,CAAC,EAAE,CAAC+F,IAAI,CAACa,KAAK,EAAE5G,WAAW,CAAC,CAAC;EAC7B,IAAIqH,yBAAyB,GAAGzJ,KAAK,CAAC4D,OAAO,CAAC,YAAY;IACxD,OAAO7C,WAAW,CAACoH,IAAI,CAACzG,MAAM,EAAEoF,YAAY,CAAC;EAC/C,CAAC,EAAE,CAACqB,IAAI,CAACzG,MAAM,EAAEoF,YAAY,CAAC,CAAC;;EAE/B;EACA,IAAI4C,eAAe,GAAG5C,YAAY,GAAGpF,MAAM;EAC3C,IAAIiI,kBAAkB,GAAG1J,MAAM,CAACyJ,eAAe,CAAC;EAChDC,kBAAkB,CAACvD,OAAO,GAAGsD,eAAe;EAC5C,SAASvD,WAAWA,CAACyD,YAAY,EAAE;IACjC,IAAI7D,MAAM,GAAG6D,YAAY;IACzB,IAAI,CAACC,MAAM,CAACC,KAAK,CAACH,kBAAkB,CAACvD,OAAO,CAAC,EAAE;MAC7CL,MAAM,GAAG1B,IAAI,CAAC0D,GAAG,CAAChC,MAAM,EAAE4D,kBAAkB,CAACvD,OAAO,CAAC;IACvD;IACAL,MAAM,GAAG1B,IAAI,CAACC,GAAG,CAACyB,MAAM,EAAE,CAAC,CAAC;IAC5B,OAAOA,MAAM;EACf;EACA,IAAIgE,aAAa,GAAG9E,SAAS,IAAI,CAAC;EAClC,IAAI+E,gBAAgB,GAAG/E,SAAS,IAAIyE,eAAe;EACnD,IAAIO,cAAc,GAAG5E,UAAU,IAAI,CAAC;EACpC,IAAI6E,eAAe,GAAG7E,UAAU,IAAIjD,WAAW;EAC/C,IAAI+H,YAAY,GAAGxJ,eAAe,CAACoJ,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,eAAe,CAAC;;EAEpG;EACA,IAAIE,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IACzD,OAAO;MACLC,CAAC,EAAE7F,KAAK,GAAG,CAACa,UAAU,GAAGA,UAAU;MACnCiF,CAAC,EAAErF;IACL,CAAC;EACH,CAAC;EACD,IAAIsF,wBAAwB,GAAGtK,MAAM,CAACmK,oBAAoB,CAAC,CAAC,CAAC;EAC7D,IAAII,aAAa,GAAG1K,QAAQ,CAAC,UAAU2K,MAAM,EAAE;IAC7C,IAAIhI,eAAe,EAAE;MACnB,IAAIiI,QAAQ,GAAGnL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6K,oBAAoB,CAAC,CAAC,CAAC,EAAEK,MAAM,CAAC;;MAE/E;MACA,IAAIF,wBAAwB,CAACnE,OAAO,CAACiE,CAAC,KAAKK,QAAQ,CAACL,CAAC,IAAIE,wBAAwB,CAACnE,OAAO,CAACkE,CAAC,KAAKI,QAAQ,CAACJ,CAAC,EAAE;QAC1G7H,eAAe,CAACiI,QAAQ,CAAC;QACzBH,wBAAwB,CAACnE,OAAO,GAAGsE,QAAQ;MAC7C;IACF;EACF,CAAC,CAAC;EACF,SAASC,WAAWA,CAACC,eAAe,EAAEC,UAAU,EAAE;IAChD,IAAIC,SAAS,GAAGF,eAAe;IAC/B,IAAIC,UAAU,EAAE;MACd1K,SAAS,CAAC,YAAY;QACpBmF,aAAa,CAACwF,SAAS,CAAC;MAC1B,CAAC,CAAC;MACFN,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACL1E,aAAa,CAACgF,SAAS,CAAC;IAC1B;EACF;;EAEA;EACA,SAASC,gBAAgBA,CAACC,CAAC,EAAE;IAC3B,IAAIpB,YAAY,GAAGoB,CAAC,CAACC,aAAa,CAAC5E,SAAS;IAC5C,IAAIuD,YAAY,KAAK3E,SAAS,EAAE;MAC9Ba,aAAa,CAAC8D,YAAY,CAAC;IAC7B;;IAEA;IACApH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACwI,CAAC,CAAC;IACvDR,aAAa,CAAC,CAAC;EACjB;EACA,IAAIU,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,cAAc,EAAE;IACzE,IAAIC,aAAa,GAAGD,cAAc;IAClC,IAAI7G,GAAG,GAAG,CAAC,CAAClC,WAAW,GAAGA,WAAW,GAAG+F,IAAI,CAACa,KAAK,GAAG,CAAC;IACtDoC,aAAa,GAAG/G,IAAI,CAACC,GAAG,CAAC8G,aAAa,EAAE,CAAC,CAAC;IAC1CA,aAAa,GAAG/G,IAAI,CAAC0D,GAAG,CAACqD,aAAa,EAAE9G,GAAG,CAAC;IAC5C,OAAO8G,aAAa;EACtB,CAAC;EACD,IAAIC,YAAY,GAAGvL,QAAQ,CAAC,UAAUwL,QAAQ,EAAEC,cAAc,EAAE;IAC9D,IAAIA,cAAc,EAAE;MAClBpL,SAAS,CAAC,YAAY;QACpBmF,aAAa,CAAC,UAAUkG,IAAI,EAAE;UAC5B,IAAIL,cAAc,GAAGK,IAAI,IAAIhH,KAAK,GAAG,CAAC8G,QAAQ,GAAGA,QAAQ,CAAC;UAC1D,OAAOJ,qBAAqB,CAACC,cAAc,CAAC;QAC9C,CAAC,CAAC;MACJ,CAAC,CAAC;MACFX,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM;MACL1E,aAAa,CAAC,UAAU2F,GAAG,EAAE;QAC3B,IAAI1F,MAAM,GAAG0F,GAAG,GAAGH,QAAQ;QAC3B,OAAOvF,MAAM;MACf,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;;EAEF;EACA,IAAI2F,cAAc,GAAGnL,aAAa,CAACmD,UAAU,EAAEqG,aAAa,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,eAAe,EAAE,CAAC,CAAC9H,WAAW,EAAEiJ,YAAY,CAAC;IAC3IM,eAAe,GAAGlM,cAAc,CAACiM,cAAc,EAAE,CAAC,CAAC;IACnDE,UAAU,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC/BE,eAAe,GAAGF,eAAe,CAAC,CAAC,CAAC;;EAEtC;EACAjL,kBAAkB,CAACgD,UAAU,EAAEkB,YAAY,EAAE,UAAUkH,YAAY,EAAEC,KAAK,EAAEC,YAAY,EAAEhB,CAAC,EAAE;IAC3F,IAAIiB,KAAK,GAAGjB,CAAC;IACb,IAAIb,YAAY,CAAC2B,YAAY,EAAEC,KAAK,EAAEC,YAAY,CAAC,EAAE;MACnD,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAACC,KAAK,IAAI,CAACA,KAAK,CAACC,eAAe,EAAE;MACpC,IAAID,KAAK,EAAE;QACTA,KAAK,CAACC,eAAe,GAAG,IAAI;MAC9B;MACAN,UAAU,CAAC;QACTO,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG,CAAC,CAAC;QAC5CC,MAAM,EAAEN,YAAY,GAAGC,KAAK,GAAG,CAAC;QAChCM,MAAM,EAAEP,YAAY,GAAG,CAAC,GAAGC;MAC7B,CAAC,CAAC;MACF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC;;EAEF;EACAnL,aAAa,CAACwD,SAAS,EAAEQ,YAAY,EAAE,UAAUoC,MAAM,EAAE;IACvDlB,aAAa,CAAC,UAAU2F,GAAG,EAAE;MAC3B,OAAOA,GAAG,GAAGzE,MAAM;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC;EACFjH,eAAe,CAAC,YAAY;IAC1B;IACA,SAASuM,qBAAqBA,CAACtB,CAAC,EAAE;MAChC;MACA,IAAIuB,gBAAgB,GAAGxC,aAAa,IAAIiB,CAAC,CAACwB,MAAM,GAAG,CAAC;MACpD,IAAIC,qBAAqB,GAAGzC,gBAAgB,IAAIgB,CAAC,CAACwB,MAAM,GAAG,CAAC;MAC5D,IAAI9I,UAAU,IAAI,CAAC6I,gBAAgB,IAAI,CAACE,qBAAqB,EAAE;QAC7DzB,CAAC,CAACmB,cAAc,CAAC,CAAC;MACpB;IACF;IACA,IAAIO,YAAY,GAAG9H,YAAY,CAACwB,OAAO;IACvCsG,YAAY,CAACC,gBAAgB,CAAC,OAAO,EAAEf,UAAU,EAAE;MACjDgB,OAAO,EAAE;IACX,CAAC,CAAC;IACFF,YAAY,CAACC,gBAAgB,CAAC,gBAAgB,EAAEd,eAAe,EAAE;MAC/De,OAAO,EAAE;IACX,CAAC,CAAC;IACFF,YAAY,CAACC,gBAAgB,CAAC,qBAAqB,EAAEL,qBAAqB,EAAE;MAC1EM,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,YAAY;MACjBF,YAAY,CAACG,mBAAmB,CAAC,OAAO,EAAEjB,UAAU,CAAC;MACrDc,YAAY,CAACG,mBAAmB,CAAC,gBAAgB,EAAEhB,eAAe,CAAC;MACnEa,YAAY,CAACG,mBAAmB,CAAC,qBAAqB,EAAEP,qBAAqB,CAAC;IAChF,CAAC;EACH,CAAC,EAAE,CAAC5I,UAAU,EAAEqG,aAAa,EAAEC,gBAAgB,CAAC,CAAC;;EAEjD;EACAjK,eAAe,CAAC,YAAY;IAC1B,IAAIqC,WAAW,EAAE;MACf,IAAI0K,aAAa,GAAG5B,qBAAqB,CAAC7F,UAAU,CAAC;MACrDC,aAAa,CAACwH,aAAa,CAAC;MAC5BtC,aAAa,CAAC;QACZH,CAAC,EAAEyC;MACL,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3E,IAAI,CAACa,KAAK,EAAE5G,WAAW,CAAC,CAAC;;EAE7B;EACA,IAAI2K,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrD,IAAIC,qBAAqB,EAAEC,qBAAqB;IAChD,CAACD,qBAAqB,GAAG1D,oBAAoB,CAAClD,OAAO,MAAM,IAAI,IAAI4G,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACE,WAAW,CAAC,CAAC;IAC1I,CAACD,qBAAqB,GAAG1D,sBAAsB,CAACnD,OAAO,MAAM,IAAI,IAAI6G,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,WAAW,CAAC,CAAC;EAC9I,CAAC;EACD,IAAIC,SAAS,GAAGtM,WAAW,CAAC+D,YAAY,EAAED,UAAU,EAAEnB,OAAO,EAAE7B,UAAU,EAAEsB,MAAM,EAAE,YAAY;IAC7F,OAAOM,aAAa,CAAC,IAAI,CAAC;EAC5B,CAAC,EAAEuC,aAAa,EAAEiH,kBAAkB,CAAC;EACrC/M,KAAK,CAACoN,mBAAmB,CAAC9L,GAAG,EAAE,YAAY;IACzC,OAAO;MACL+L,aAAa,EAAEvI,YAAY,CAACsB,OAAO;MACnCkH,aAAa,EAAElD,oBAAoB;MACnCmD,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAE;QAClC,SAASC,WAAWA,CAACC,GAAG,EAAE;UACxB,OAAOA,GAAG,IAAIpO,OAAO,CAACoO,GAAG,CAAC,KAAK,QAAQ,KAAK,MAAM,IAAIA,GAAG,IAAI,KAAK,IAAIA,GAAG,CAAC;QAC5E;QACA,IAAID,WAAW,CAACD,MAAM,CAAC,EAAE;UACvB;UACA,IAAIA,MAAM,CAAChC,IAAI,KAAKzE,SAAS,EAAE;YAC7BzB,aAAa,CAAC4F,qBAAqB,CAACsC,MAAM,CAAChC,IAAI,CAAC,CAAC;UACnD;;UAEA;UACA2B,SAAS,CAACK,MAAM,CAAC/B,GAAG,CAAC;QACvB,CAAC,MAAM;UACL0B,SAAS,CAACK,MAAM,CAAC;QACnB;MACF;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA;EACAzN,eAAe,CAAC,YAAY;IAC1B,IAAI2C,eAAe,EAAE;MACnB,IAAIiL,UAAU,GAAGhJ,UAAU,CAACiJ,KAAK,CAACrH,KAAK,EAAEC,GAAG,GAAG,CAAC,CAAC;MACjD9D,eAAe,CAACiL,UAAU,EAAEhJ,UAAU,CAAC;IACzC;EACF,CAAC,EAAE,CAAC4B,KAAK,EAAEC,GAAG,EAAE7B,UAAU,CAAC,CAAC;;EAE5B;EACA,IAAIkJ,OAAO,GAAGrN,UAAU,CAACmE,UAAU,EAAE1B,MAAM,EAAEO,OAAO,EAAE7B,UAAU,CAAC;EACjE,IAAImM,YAAY,GAAGlL,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC;IACvF2D,KAAK,EAAEA,KAAK;IACZC,GAAG,EAAEA,GAAG;IACRtE,OAAO,EAAEkC,SAAS;IAClB2J,OAAO,EAAE1I,UAAU;IACnB2I,OAAO,EAAEhG,YAAY;IACrBiG,GAAG,EAAEzJ,KAAK;IACVqJ,OAAO,EAAEA;EACX,CAAC,CAAC;;EAEF;EACA,IAAIK,YAAY,GAAG7N,WAAW,CAACsE,UAAU,EAAE4B,KAAK,EAAEC,GAAG,EAAEpE,WAAW,EAAEiD,UAAU,EAAE/B,cAAc,EAAEtB,QAAQ,EAAE6D,YAAY,CAAC;EACvH,IAAIsI,cAAc,GAAG,IAAI;EACzB,IAAIzM,MAAM,EAAE;IACVyM,cAAc,GAAG5O,aAAa,CAACC,eAAe,CAAC,CAAC,CAAC,EAAEqC,UAAU,GAAG,QAAQ,GAAG,WAAW,EAAEH,MAAM,CAAC,EAAET,WAAW,CAAC;IAC7G,IAAIyC,UAAU,EAAE;MACdyK,cAAc,CAACjN,SAAS,GAAG,QAAQ;MACnC,IAAIkB,WAAW,EAAE;QACf+L,cAAc,CAACC,SAAS,GAAG,QAAQ;MACrC;MACA,IAAI3I,YAAY,EAAE;QAChB0I,cAAc,CAACE,aAAa,GAAG,MAAM;MACvC;IACF;EACF;EACA,IAAIC,cAAc,GAAG,CAAC,CAAC;EACvB,IAAI9J,KAAK,EAAE;IACT8J,cAAc,CAACC,GAAG,GAAG,KAAK;EAC5B;EACA,OAAO,aAAavO,KAAK,CAACwO,aAAa,CAAC,KAAK,EAAEnP,QAAQ,CAAC;IACtDiC,GAAG,EAAEwD,YAAY;IACjBhD,KAAK,EAAEvC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjD2M,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFhN,SAAS,EAAEgD;EACb,CAAC,EAAE6J,cAAc,EAAEtL,SAAS,CAAC,EAAE,aAAahD,KAAK,CAACwO,aAAa,CAAC3O,cAAc,EAAE;IAC9E6O,QAAQ,EAAEvF;EACZ,CAAC,EAAE,aAAanJ,KAAK,CAACwO,aAAa,CAACjM,SAAS,EAAE;IAC7Cd,SAAS,EAAE,EAAE,CAACiD,MAAM,CAAClD,SAAS,EAAE,SAAS,CAAC;IAC1CM,KAAK,EAAEqM,cAAc;IACrB7M,GAAG,EAAEsD,YAAY;IACjBpC,QAAQ,EAAEuI,gBAAgB;IAC1B4D,YAAY,EAAE5B;EAChB,CAAC,EAAE,aAAa/M,KAAK,CAACwO,aAAa,CAACpO,MAAM,EAAE;IAC1CoB,SAAS,EAAEA,SAAS;IACpBE,MAAM,EAAEoF,YAAY;IACpBiH,OAAO,EAAE1I,UAAU;IACnB2I,OAAO,EAAEhG,YAAY;IACrB5F,WAAW,EAAEA,WAAW;IACxBwM,aAAa,EAAErL,aAAa;IAC5BjC,GAAG,EAAEuD,cAAc;IACnBlC,UAAU,EAAEA,UAAU;IACtBsL,GAAG,EAAEzJ,KAAK;IACVqK,KAAK,EAAEf;EACT,CAAC,EAAEI,YAAY,CAAC,CAAC,CAAC,EAAE9J,SAAS,IAAI0C,YAAY,GAAGpF,MAAM,IAAI,aAAa1B,KAAK,CAACwO,aAAa,CAAC1N,SAAS,EAAE;IACpGQ,GAAG,EAAEgI,oBAAoB;IACzB9H,SAAS,EAAEA,SAAS;IACpBsN,YAAY,EAAE7J,SAAS;IACvB8J,WAAW,EAAEjI,YAAY;IACzBmH,GAAG,EAAEzJ,KAAK;IACVhC,QAAQ,EAAEmI,WAAW;IACrBqE,WAAW,EAAErJ,oBAAoB;IACjCsJ,UAAU,EAAErJ,mBAAmB;IAC/BsJ,QAAQ,EAAEzF,yBAAyB;IACnC0F,aAAa,EAAEhH,IAAI,CAACzG,MAAM;IAC1BI,KAAK,EAAEe,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACuM,iBAAiB;IAC/EC,UAAU,EAAExM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACyM,sBAAsB;IACzFvM,aAAa,EAAEA;EACjB,CAAC,CAAC,EAAEqB,SAAS,IAAIhC,WAAW,GAAG+F,IAAI,CAACa,KAAK,IAAI,aAAahJ,KAAK,CAACwO,aAAa,CAAC1N,SAAS,EAAE;IACvFQ,GAAG,EAAEiI,sBAAsB;IAC3B/H,SAAS,EAAEA,SAAS;IACpBsN,YAAY,EAAEzJ,UAAU;IACxB0J,WAAW,EAAE3M,WAAW;IACxB6L,GAAG,EAAEzJ,KAAK;IACVhC,QAAQ,EAAEmI,WAAW;IACrBqE,WAAW,EAAErJ,oBAAoB;IACjCsJ,UAAU,EAAErJ,mBAAmB;IAC/BsJ,QAAQ,EAAE1F,2BAA2B;IACrC2F,aAAa,EAAEhH,IAAI,CAACa,KAAK;IACzB6B,UAAU,EAAE,IAAI;IAChB/I,KAAK,EAAEe,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC0M,mBAAmB;IACjFF,UAAU,EAAExM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2M,wBAAwB;IAC3FzM,aAAa,EAAEA;EACjB,CAAC,CAAC,CAAC;AACL;AACA,IAAI0M,IAAI,GAAG,aAAazP,KAAK,CAAC0P,UAAU,CAACtO,OAAO,CAAC;AACjDqO,IAAI,CAACE,WAAW,GAAG,MAAM;AACzB,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}