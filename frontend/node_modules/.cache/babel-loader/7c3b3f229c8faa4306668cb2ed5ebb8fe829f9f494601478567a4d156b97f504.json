{"ast": null, "code": "/* eslint-disable no-param-reassign */\nimport { removeCSS, updateCSS } from \"./Dom/dynamicCSS\";\nvar cached;\nfunction measureScrollbarSize(ele) {\n  var randomId = \"rc-scrollbar-measure-\".concat(Math.random().toString(36).substring(7));\n  var measureEle = document.createElement('div');\n  measureEle.id = randomId;\n\n  // Create Style\n  var measureStyle = measureEle.style;\n  measureStyle.position = 'absolute';\n  measureStyle.left = '0';\n  measureStyle.top = '0';\n  measureStyle.width = '100px';\n  measureStyle.height = '100px';\n  measureStyle.overflow = 'scroll';\n\n  // Clone Style if needed\n  var fallbackWidth;\n  var fallbackHeight;\n  if (ele) {\n    var targetStyle = getComputedStyle(ele);\n    measureStyle.scrollbarColor = targetStyle.scrollbarColor;\n    measureStyle.scrollbarWidth = targetStyle.scrollbarWidth;\n\n    // Set Webkit style\n    var webkitScrollbarStyle = getComputedStyle(ele, '::-webkit-scrollbar');\n    var width = parseInt(webkitScrollbarStyle.width, 10);\n    var height = parseInt(webkitScrollbarStyle.height, 10);\n\n    // Try wrap to handle CSP case\n    try {\n      var widthStyle = width ? \"width: \".concat(webkitScrollbarStyle.width, \";\") : '';\n      var heightStyle = height ? \"height: \".concat(webkitScrollbarStyle.height, \";\") : '';\n      updateCSS(\"\\n#\".concat(randomId, \"::-webkit-scrollbar {\\n\").concat(widthStyle, \"\\n\").concat(heightStyle, \"\\n}\"), randomId);\n    } catch (e) {\n      // Can't wrap, just log error\n      console.error(e);\n\n      // Get from style directly\n      fallbackWidth = width;\n      fallbackHeight = height;\n    }\n  }\n  document.body.appendChild(measureEle);\n\n  // Measure. Get fallback style if provided\n  var scrollWidth = ele && fallbackWidth && !isNaN(fallbackWidth) ? fallbackWidth : measureEle.offsetWidth - measureEle.clientWidth;\n  var scrollHeight = ele && fallbackHeight && !isNaN(fallbackHeight) ? fallbackHeight : measureEle.offsetHeight - measureEle.clientHeight;\n\n  // Clean up\n  document.body.removeChild(measureEle);\n  removeCSS(randomId);\n  return {\n    width: scrollWidth,\n    height: scrollHeight\n  };\n}\nexport default function getScrollBarSize(fresh) {\n  if (typeof document === 'undefined') {\n    return 0;\n  }\n  if (fresh || cached === undefined) {\n    cached = measureScrollbarSize();\n  }\n  return cached.width;\n}\nexport function getTargetScrollBarSize(target) {\n  if (typeof document === 'undefined' || !target || !(target instanceof Element)) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  return measureScrollbarSize(target);\n}", "map": {"version": 3, "names": ["removeCSS", "updateCSS", "cached", "measureScrollbarSize", "ele", "randomId", "concat", "Math", "random", "toString", "substring", "measureEle", "document", "createElement", "id", "measureStyle", "style", "position", "left", "top", "width", "height", "overflow", "fallback<PERSON><PERSON><PERSON>", "fallbackHeight", "targetStyle", "getComputedStyle", "scrollbarColor", "scrollbarWidth", "webkitScrollbarStyle", "parseInt", "widthStyle", "heightStyle", "e", "console", "error", "body", "append<PERSON><PERSON><PERSON>", "scrollWidth", "isNaN", "offsetWidth", "clientWidth", "scrollHeight", "offsetHeight", "clientHeight", "<PERSON><PERSON><PERSON><PERSON>", "getScrollBarSize", "fresh", "undefined", "getTargetScrollBarSize", "target", "Element"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-util/es/getScrollBarSize.js"], "sourcesContent": ["/* eslint-disable no-param-reassign */\nimport { removeCSS, updateCSS } from \"./Dom/dynamicCSS\";\nvar cached;\nfunction measureScrollbarSize(ele) {\n  var randomId = \"rc-scrollbar-measure-\".concat(Math.random().toString(36).substring(7));\n  var measureEle = document.createElement('div');\n  measureEle.id = randomId;\n\n  // Create Style\n  var measureStyle = measureEle.style;\n  measureStyle.position = 'absolute';\n  measureStyle.left = '0';\n  measureStyle.top = '0';\n  measureStyle.width = '100px';\n  measureStyle.height = '100px';\n  measureStyle.overflow = 'scroll';\n\n  // Clone Style if needed\n  var fallbackWidth;\n  var fallbackHeight;\n  if (ele) {\n    var targetStyle = getComputedStyle(ele);\n    measureStyle.scrollbarColor = targetStyle.scrollbarColor;\n    measureStyle.scrollbarWidth = targetStyle.scrollbarWidth;\n\n    // Set Webkit style\n    var webkitScrollbarStyle = getComputedStyle(ele, '::-webkit-scrollbar');\n    var width = parseInt(webkitScrollbarStyle.width, 10);\n    var height = parseInt(webkitScrollbarStyle.height, 10);\n\n    // Try wrap to handle CSP case\n    try {\n      var widthStyle = width ? \"width: \".concat(webkitScrollbarStyle.width, \";\") : '';\n      var heightStyle = height ? \"height: \".concat(webkitScrollbarStyle.height, \";\") : '';\n      updateCSS(\"\\n#\".concat(randomId, \"::-webkit-scrollbar {\\n\").concat(widthStyle, \"\\n\").concat(heightStyle, \"\\n}\"), randomId);\n    } catch (e) {\n      // Can't wrap, just log error\n      console.error(e);\n\n      // Get from style directly\n      fallbackWidth = width;\n      fallbackHeight = height;\n    }\n  }\n  document.body.appendChild(measureEle);\n\n  // Measure. Get fallback style if provided\n  var scrollWidth = ele && fallbackWidth && !isNaN(fallbackWidth) ? fallbackWidth : measureEle.offsetWidth - measureEle.clientWidth;\n  var scrollHeight = ele && fallbackHeight && !isNaN(fallbackHeight) ? fallbackHeight : measureEle.offsetHeight - measureEle.clientHeight;\n\n  // Clean up\n  document.body.removeChild(measureEle);\n  removeCSS(randomId);\n  return {\n    width: scrollWidth,\n    height: scrollHeight\n  };\n}\nexport default function getScrollBarSize(fresh) {\n  if (typeof document === 'undefined') {\n    return 0;\n  }\n  if (fresh || cached === undefined) {\n    cached = measureScrollbarSize();\n  }\n  return cached.width;\n}\nexport function getTargetScrollBarSize(target) {\n  if (typeof document === 'undefined' || !target || !(target instanceof Element)) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  return measureScrollbarSize(target);\n}"], "mappings": "AAAA;AACA,SAASA,SAAS,EAAEC,SAAS,QAAQ,kBAAkB;AACvD,IAAIC,MAAM;AACV,SAASC,oBAAoBA,CAACC,GAAG,EAAE;EACjC,IAAIC,QAAQ,GAAG,uBAAuB,CAACC,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;EACtF,IAAIC,UAAU,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC9CF,UAAU,CAACG,EAAE,GAAGT,QAAQ;;EAExB;EACA,IAAIU,YAAY,GAAGJ,UAAU,CAACK,KAAK;EACnCD,YAAY,CAACE,QAAQ,GAAG,UAAU;EAClCF,YAAY,CAACG,IAAI,GAAG,GAAG;EACvBH,YAAY,CAACI,GAAG,GAAG,GAAG;EACtBJ,YAAY,CAACK,KAAK,GAAG,OAAO;EAC5BL,YAAY,CAACM,MAAM,GAAG,OAAO;EAC7BN,YAAY,CAACO,QAAQ,GAAG,QAAQ;;EAEhC;EACA,IAAIC,aAAa;EACjB,IAAIC,cAAc;EAClB,IAAIpB,GAAG,EAAE;IACP,IAAIqB,WAAW,GAAGC,gBAAgB,CAACtB,GAAG,CAAC;IACvCW,YAAY,CAACY,cAAc,GAAGF,WAAW,CAACE,cAAc;IACxDZ,YAAY,CAACa,cAAc,GAAGH,WAAW,CAACG,cAAc;;IAExD;IACA,IAAIC,oBAAoB,GAAGH,gBAAgB,CAACtB,GAAG,EAAE,qBAAqB,CAAC;IACvE,IAAIgB,KAAK,GAAGU,QAAQ,CAACD,oBAAoB,CAACT,KAAK,EAAE,EAAE,CAAC;IACpD,IAAIC,MAAM,GAAGS,QAAQ,CAACD,oBAAoB,CAACR,MAAM,EAAE,EAAE,CAAC;;IAEtD;IACA,IAAI;MACF,IAAIU,UAAU,GAAGX,KAAK,GAAG,SAAS,CAACd,MAAM,CAACuB,oBAAoB,CAACT,KAAK,EAAE,GAAG,CAAC,GAAG,EAAE;MAC/E,IAAIY,WAAW,GAAGX,MAAM,GAAG,UAAU,CAACf,MAAM,CAACuB,oBAAoB,CAACR,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE;MACnFpB,SAAS,CAAC,KAAK,CAACK,MAAM,CAACD,QAAQ,EAAE,yBAAyB,CAAC,CAACC,MAAM,CAACyB,UAAU,EAAE,IAAI,CAAC,CAACzB,MAAM,CAAC0B,WAAW,EAAE,KAAK,CAAC,EAAE3B,QAAQ,CAAC;IAC5H,CAAC,CAAC,OAAO4B,CAAC,EAAE;MACV;MACAC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;;MAEhB;MACAV,aAAa,GAAGH,KAAK;MACrBI,cAAc,GAAGH,MAAM;IACzB;EACF;EACAT,QAAQ,CAACwB,IAAI,CAACC,WAAW,CAAC1B,UAAU,CAAC;;EAErC;EACA,IAAI2B,WAAW,GAAGlC,GAAG,IAAImB,aAAa,IAAI,CAACgB,KAAK,CAAChB,aAAa,CAAC,GAAGA,aAAa,GAAGZ,UAAU,CAAC6B,WAAW,GAAG7B,UAAU,CAAC8B,WAAW;EACjI,IAAIC,YAAY,GAAGtC,GAAG,IAAIoB,cAAc,IAAI,CAACe,KAAK,CAACf,cAAc,CAAC,GAAGA,cAAc,GAAGb,UAAU,CAACgC,YAAY,GAAGhC,UAAU,CAACiC,YAAY;;EAEvI;EACAhC,QAAQ,CAACwB,IAAI,CAACS,WAAW,CAAClC,UAAU,CAAC;EACrCX,SAAS,CAACK,QAAQ,CAAC;EACnB,OAAO;IACLe,KAAK,EAAEkB,WAAW;IAClBjB,MAAM,EAAEqB;EACV,CAAC;AACH;AACA,eAAe,SAASI,gBAAgBA,CAACC,KAAK,EAAE;EAC9C,IAAI,OAAOnC,QAAQ,KAAK,WAAW,EAAE;IACnC,OAAO,CAAC;EACV;EACA,IAAImC,KAAK,IAAI7C,MAAM,KAAK8C,SAAS,EAAE;IACjC9C,MAAM,GAAGC,oBAAoB,CAAC,CAAC;EACjC;EACA,OAAOD,MAAM,CAACkB,KAAK;AACrB;AACA,OAAO,SAAS6B,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,IAAI,OAAOtC,QAAQ,KAAK,WAAW,IAAI,CAACsC,MAAM,IAAI,EAAEA,MAAM,YAAYC,OAAO,CAAC,EAAE;IAC9E,OAAO;MACL/B,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;EACH;EACA,OAAOlB,oBAAoB,CAAC+C,MAAM,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}