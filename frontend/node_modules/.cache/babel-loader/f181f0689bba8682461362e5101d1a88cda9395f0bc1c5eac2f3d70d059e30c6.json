{"ast": null, "code": "export default function constants(x) {\n  return function () {\n    return x;\n  };\n}", "map": {"version": 3, "names": ["constants", "x"], "sources": ["/home/<USER>/itai/frontend/node_modules/d3-scale/src/constant.js"], "sourcesContent": ["export default function constants(x) {\n  return function() {\n    return x;\n  };\n}\n"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,CAAC,EAAE;EACnC,OAAO,YAAW;IAChB,OAAOA,CAAC;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}