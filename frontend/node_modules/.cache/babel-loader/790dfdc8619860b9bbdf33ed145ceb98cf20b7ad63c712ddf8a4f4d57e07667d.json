{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport * as React from 'react';\nexport default function FooterRow(_ref) {\n  var children = _ref.children,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"tr\", props, children);\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_excluded", "React", "FooterRow", "_ref", "children", "props", "createElement"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-table/es/Footer/Row.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"];\nimport * as React from 'react';\nexport default function FooterRow(_ref) {\n  var children = _ref.children,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"tr\", props, children);\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,KAAK,GAAGN,wBAAwB,CAACI,IAAI,EAAEH,SAAS,CAAC;EACnD,OAAO,aAAaC,KAAK,CAACK,aAAa,CAAC,IAAI,EAAED,KAAK,EAAED,QAAQ,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}