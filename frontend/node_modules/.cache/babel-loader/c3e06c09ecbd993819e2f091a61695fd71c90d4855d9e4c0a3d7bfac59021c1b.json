{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"noData\", \"columns\", \"flattenColumns\", \"colWidths\", \"columCount\", \"stickyOffsets\", \"direction\", \"fixHeader\", \"stickyTopOffset\", \"stickyBottomOffset\", \"stickyClassName\", \"onScroll\", \"maxContentScroll\", \"children\"];\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport { fillRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport ColGroup from \"../ColGroup\";\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nfunction useColumnWidth(colWidths, columCount) {\n  return useMemo(function () {\n    var cloneColumns = [];\n    for (var i = 0; i < columCount; i += 1) {\n      var val = colWidths[i];\n      if (val !== undefined) {\n        cloneColumns[i] = val;\n      } else {\n        return null;\n      }\n    }\n    return cloneColumns;\n  }, [colWidths.join('_'), columCount]);\n}\nvar FixedHolder = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var className = props.className,\n    noData = props.noData,\n    columns = props.columns,\n    flattenColumns = props.flattenColumns,\n    colWidths = props.colWidths,\n    columCount = props.columCount,\n    stickyOffsets = props.stickyOffsets,\n    direction = props.direction,\n    fixHeader = props.fixHeader,\n    stickyTopOffset = props.stickyTopOffset,\n    stickyBottomOffset = props.stickyBottomOffset,\n    stickyClassName = props.stickyClassName,\n    onScroll = props.onScroll,\n    maxContentScroll = props.maxContentScroll,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _useContext = useContext(TableContext, ['prefixCls', 'scrollbarSize', 'isSticky', 'getComponent']),\n    prefixCls = _useContext.prefixCls,\n    scrollbarSize = _useContext.scrollbarSize,\n    isSticky = _useContext.isSticky,\n    getComponent = _useContext.getComponent;\n  var TableComponent = getComponent(['header', 'table'], 'table');\n  var combinationScrollBarSize = isSticky && !fixHeader ? 0 : scrollbarSize;\n\n  // Pass wheel to scroll event\n  var scrollRef = React.useRef(null);\n  var setScrollRef = React.useCallback(function (element) {\n    fillRef(ref, element);\n    fillRef(scrollRef, element);\n  }, []);\n  React.useEffect(function () {\n    function onWheel(e) {\n      var _ref = e,\n        currentTarget = _ref.currentTarget,\n        deltaX = _ref.deltaX;\n      if (deltaX) {\n        onScroll({\n          currentTarget: currentTarget,\n          scrollLeft: currentTarget.scrollLeft + deltaX\n        });\n        e.preventDefault();\n      }\n    }\n    var scrollEle = scrollRef.current;\n    scrollEle === null || scrollEle === void 0 || scrollEle.addEventListener('wheel', onWheel, {\n      passive: false\n    });\n    return function () {\n      scrollEle === null || scrollEle === void 0 || scrollEle.removeEventListener('wheel', onWheel);\n    };\n  }, []);\n\n  // Check if all flattenColumns has width\n  var allFlattenColumnsWithWidth = React.useMemo(function () {\n    return flattenColumns.every(function (column) {\n      return column.width;\n    });\n  }, [flattenColumns]);\n\n  // Add scrollbar column\n  var lastColumn = flattenColumns[flattenColumns.length - 1];\n  var ScrollBarColumn = {\n    fixed: lastColumn ? lastColumn.fixed : null,\n    scrollbar: true,\n    onHeaderCell: function onHeaderCell() {\n      return {\n        className: \"\".concat(prefixCls, \"-cell-scrollbar\")\n      };\n    }\n  };\n  var columnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(columns), [ScrollBarColumn]) : columns;\n  }, [combinationScrollBarSize, columns]);\n  var flattenColumnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(flattenColumns), [ScrollBarColumn]) : flattenColumns;\n  }, [combinationScrollBarSize, flattenColumns]);\n\n  // Calculate the sticky offsets\n  var headerStickyOffsets = useMemo(function () {\n    var right = stickyOffsets.right,\n      left = stickyOffsets.left;\n    return _objectSpread(_objectSpread({}, stickyOffsets), {}, {\n      left: direction === 'rtl' ? [].concat(_toConsumableArray(left.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]) : left,\n      right: direction === 'rtl' ? right : [].concat(_toConsumableArray(right.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]),\n      isSticky: isSticky\n    });\n  }, [combinationScrollBarSize, stickyOffsets, isSticky]);\n  var mergedColumnWidth = useColumnWidth(colWidths, columCount);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: _objectSpread({\n      overflow: 'hidden'\n    }, isSticky ? {\n      top: stickyTopOffset,\n      bottom: stickyBottomOffset\n    } : {}),\n    ref: setScrollRef,\n    className: classNames(className, _defineProperty({}, stickyClassName, !!stickyClassName))\n  }, /*#__PURE__*/React.createElement(TableComponent, {\n    style: {\n      tableLayout: 'fixed',\n      visibility: noData || mergedColumnWidth ? null : 'hidden'\n    }\n  }, (!noData || !maxContentScroll || allFlattenColumnsWithWidth) && /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: mergedColumnWidth ? [].concat(_toConsumableArray(mergedColumnWidth), [combinationScrollBarSize]) : [],\n    columCount: columCount + 1,\n    columns: flattenColumnsWithScrollbar\n  }), children(_objectSpread(_objectSpread({}, restProps), {}, {\n    stickyOffsets: headerStickyOffsets,\n    columns: columnsWithScrollbar,\n    flattenColumns: flattenColumnsWithScrollbar\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  FixedHolder.displayName = 'FixedHolder';\n}\n\n/** Return a table in div as fixed element which contains sticky info */\n// export default responseImmutable(FixedHolder);\nexport default /*#__PURE__*/React.memo(FixedHolder);", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_toConsumableArray", "_objectWithoutProperties", "_excluded", "useContext", "classNames", "fillRef", "React", "useMemo", "ColGroup", "TableContext", "devRenderTimes", "useColumnWidth", "col<PERSON><PERSON><PERSON>", "columCount", "cloneColumns", "i", "val", "undefined", "join", "FixedHolder", "forwardRef", "props", "ref", "process", "env", "NODE_ENV", "className", "noData", "columns", "flattenColumns", "stickyOffsets", "direction", "fixHeader", "stickyTopOffset", "stickyBottomOffset", "stickyClassName", "onScroll", "maxContentScroll", "children", "restProps", "_useContext", "prefixCls", "scrollbarSize", "isSticky", "getComponent", "TableComponent", "combinationScrollBarSize", "scrollRef", "useRef", "setScrollRef", "useCallback", "element", "useEffect", "onWheel", "e", "_ref", "currentTarget", "deltaX", "scrollLeft", "preventDefault", "scrollEle", "current", "addEventListener", "passive", "removeEventListener", "allFlattenColumnsWithWidth", "every", "column", "width", "lastColumn", "length", "ScrollBarColumn", "fixed", "scrollbar", "onHeaderCell", "concat", "columnsWithScrollbar", "flattenColumnsWithScrollbar", "headerStickyOffsets", "right", "left", "map", "mergedColumnWidth", "createElement", "style", "overflow", "top", "bottom", "tableLayout", "visibility", "displayName", "memo"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-table/es/FixedHolder/index.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"noData\", \"columns\", \"flattenColumns\", \"colWidths\", \"columCount\", \"stickyOffsets\", \"direction\", \"fixHeader\", \"stickyTopOffset\", \"stickyBottomOffset\", \"stickyClassName\", \"onScroll\", \"maxContentScroll\", \"children\"];\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport { fillRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport ColGroup from \"../ColGroup\";\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nfunction useColumnWidth(colWidths, columCount) {\n  return useMemo(function () {\n    var cloneColumns = [];\n    for (var i = 0; i < columCount; i += 1) {\n      var val = colWidths[i];\n      if (val !== undefined) {\n        cloneColumns[i] = val;\n      } else {\n        return null;\n      }\n    }\n    return cloneColumns;\n  }, [colWidths.join('_'), columCount]);\n}\nvar FixedHolder = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var className = props.className,\n    noData = props.noData,\n    columns = props.columns,\n    flattenColumns = props.flattenColumns,\n    colWidths = props.colWidths,\n    columCount = props.columCount,\n    stickyOffsets = props.stickyOffsets,\n    direction = props.direction,\n    fixHeader = props.fixHeader,\n    stickyTopOffset = props.stickyTopOffset,\n    stickyBottomOffset = props.stickyBottomOffset,\n    stickyClassName = props.stickyClassName,\n    onScroll = props.onScroll,\n    maxContentScroll = props.maxContentScroll,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _useContext = useContext(TableContext, ['prefixCls', 'scrollbarSize', 'isSticky', 'getComponent']),\n    prefixCls = _useContext.prefixCls,\n    scrollbarSize = _useContext.scrollbarSize,\n    isSticky = _useContext.isSticky,\n    getComponent = _useContext.getComponent;\n  var TableComponent = getComponent(['header', 'table'], 'table');\n  var combinationScrollBarSize = isSticky && !fixHeader ? 0 : scrollbarSize;\n\n  // Pass wheel to scroll event\n  var scrollRef = React.useRef(null);\n  var setScrollRef = React.useCallback(function (element) {\n    fillRef(ref, element);\n    fillRef(scrollRef, element);\n  }, []);\n  React.useEffect(function () {\n    function onWheel(e) {\n      var _ref = e,\n        currentTarget = _ref.currentTarget,\n        deltaX = _ref.deltaX;\n      if (deltaX) {\n        onScroll({\n          currentTarget: currentTarget,\n          scrollLeft: currentTarget.scrollLeft + deltaX\n        });\n        e.preventDefault();\n      }\n    }\n    var scrollEle = scrollRef.current;\n    scrollEle === null || scrollEle === void 0 || scrollEle.addEventListener('wheel', onWheel, {\n      passive: false\n    });\n    return function () {\n      scrollEle === null || scrollEle === void 0 || scrollEle.removeEventListener('wheel', onWheel);\n    };\n  }, []);\n\n  // Check if all flattenColumns has width\n  var allFlattenColumnsWithWidth = React.useMemo(function () {\n    return flattenColumns.every(function (column) {\n      return column.width;\n    });\n  }, [flattenColumns]);\n\n  // Add scrollbar column\n  var lastColumn = flattenColumns[flattenColumns.length - 1];\n  var ScrollBarColumn = {\n    fixed: lastColumn ? lastColumn.fixed : null,\n    scrollbar: true,\n    onHeaderCell: function onHeaderCell() {\n      return {\n        className: \"\".concat(prefixCls, \"-cell-scrollbar\")\n      };\n    }\n  };\n  var columnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(columns), [ScrollBarColumn]) : columns;\n  }, [combinationScrollBarSize, columns]);\n  var flattenColumnsWithScrollbar = useMemo(function () {\n    return combinationScrollBarSize ? [].concat(_toConsumableArray(flattenColumns), [ScrollBarColumn]) : flattenColumns;\n  }, [combinationScrollBarSize, flattenColumns]);\n\n  // Calculate the sticky offsets\n  var headerStickyOffsets = useMemo(function () {\n    var right = stickyOffsets.right,\n      left = stickyOffsets.left;\n    return _objectSpread(_objectSpread({}, stickyOffsets), {}, {\n      left: direction === 'rtl' ? [].concat(_toConsumableArray(left.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]) : left,\n      right: direction === 'rtl' ? right : [].concat(_toConsumableArray(right.map(function (width) {\n        return width + combinationScrollBarSize;\n      })), [0]),\n      isSticky: isSticky\n    });\n  }, [combinationScrollBarSize, stickyOffsets, isSticky]);\n  var mergedColumnWidth = useColumnWidth(colWidths, columCount);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: _objectSpread({\n      overflow: 'hidden'\n    }, isSticky ? {\n      top: stickyTopOffset,\n      bottom: stickyBottomOffset\n    } : {}),\n    ref: setScrollRef,\n    className: classNames(className, _defineProperty({}, stickyClassName, !!stickyClassName))\n  }, /*#__PURE__*/React.createElement(TableComponent, {\n    style: {\n      tableLayout: 'fixed',\n      visibility: noData || mergedColumnWidth ? null : 'hidden'\n    }\n  }, (!noData || !maxContentScroll || allFlattenColumnsWithWidth) && /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: mergedColumnWidth ? [].concat(_toConsumableArray(mergedColumnWidth), [combinationScrollBarSize]) : [],\n    columCount: columCount + 1,\n    columns: flattenColumnsWithScrollbar\n  }), children(_objectSpread(_objectSpread({}, restProps), {}, {\n    stickyOffsets: headerStickyOffsets,\n    columns: columnsWithScrollbar,\n    flattenColumns: flattenColumnsWithScrollbar\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  FixedHolder.displayName = 'FixedHolder';\n}\n\n/** Return a table in div as fixed element which contains sticky info */\n// export default responseImmutable(FixedHolder);\nexport default /*#__PURE__*/React.memo(FixedHolder);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,UAAU,EAAE,kBAAkB,EAAE,UAAU,CAAC;AAClP,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,OAAO,QAAQ,gBAAgB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,cAAcA,CAACC,SAAS,EAAEC,UAAU,EAAE;EAC7C,OAAON,OAAO,CAAC,YAAY;IACzB,IAAIO,YAAY,GAAG,EAAE;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,IAAI,CAAC,EAAE;MACtC,IAAIC,GAAG,GAAGJ,SAAS,CAACG,CAAC,CAAC;MACtB,IAAIC,GAAG,KAAKC,SAAS,EAAE;QACrBH,YAAY,CAACC,CAAC,CAAC,GAAGC,GAAG;MACvB,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IACA,OAAOF,YAAY;EACrB,CAAC,EAAE,CAACF,SAAS,CAACM,IAAI,CAAC,GAAG,CAAC,EAAEL,UAAU,CAAC,CAAC;AACvC;AACA,IAAIM,WAAW,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACpE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCf,cAAc,CAACW,KAAK,CAAC;EACvB;EACA,IAAIK,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC7BC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,cAAc,GAAGR,KAAK,CAACQ,cAAc;IACrCjB,SAAS,GAAGS,KAAK,CAACT,SAAS;IAC3BC,UAAU,GAAGQ,KAAK,CAACR,UAAU;IAC7BiB,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,eAAe,GAAGZ,KAAK,CAACY,eAAe;IACvCC,kBAAkB,GAAGb,KAAK,CAACa,kBAAkB;IAC7CC,eAAe,GAAGd,KAAK,CAACc,eAAe;IACvCC,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzBC,gBAAgB,GAAGhB,KAAK,CAACgB,gBAAgB;IACzCC,QAAQ,GAAGjB,KAAK,CAACiB,QAAQ;IACzBC,SAAS,GAAGtC,wBAAwB,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACxD,IAAIsC,WAAW,GAAGrC,UAAU,CAACM,YAAY,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;IACpGgC,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,aAAa,GAAGF,WAAW,CAACE,aAAa;IACzCC,QAAQ,GAAGH,WAAW,CAACG,QAAQ;IAC/BC,YAAY,GAAGJ,WAAW,CAACI,YAAY;EACzC,IAAIC,cAAc,GAAGD,YAAY,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;EAC/D,IAAIE,wBAAwB,GAAGH,QAAQ,IAAI,CAACX,SAAS,GAAG,CAAC,GAAGU,aAAa;;EAEzE;EACA,IAAIK,SAAS,GAAGzC,KAAK,CAAC0C,MAAM,CAAC,IAAI,CAAC;EAClC,IAAIC,YAAY,GAAG3C,KAAK,CAAC4C,WAAW,CAAC,UAAUC,OAAO,EAAE;IACtD9C,OAAO,CAACiB,GAAG,EAAE6B,OAAO,CAAC;IACrB9C,OAAO,CAAC0C,SAAS,EAAEI,OAAO,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EACN7C,KAAK,CAAC8C,SAAS,CAAC,YAAY;IAC1B,SAASC,OAAOA,CAACC,CAAC,EAAE;MAClB,IAAIC,IAAI,GAAGD,CAAC;QACVE,aAAa,GAAGD,IAAI,CAACC,aAAa;QAClCC,MAAM,GAAGF,IAAI,CAACE,MAAM;MACtB,IAAIA,MAAM,EAAE;QACVrB,QAAQ,CAAC;UACPoB,aAAa,EAAEA,aAAa;UAC5BE,UAAU,EAAEF,aAAa,CAACE,UAAU,GAAGD;QACzC,CAAC,CAAC;QACFH,CAAC,CAACK,cAAc,CAAC,CAAC;MACpB;IACF;IACA,IAAIC,SAAS,GAAGb,SAAS,CAACc,OAAO;IACjCD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACE,gBAAgB,CAAC,OAAO,EAAET,OAAO,EAAE;MACzFU,OAAO,EAAE;IACX,CAAC,CAAC;IACF,OAAO,YAAY;MACjBH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACI,mBAAmB,CAAC,OAAO,EAAEX,OAAO,CAAC;IAC/F,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIY,0BAA0B,GAAG3D,KAAK,CAACC,OAAO,CAAC,YAAY;IACzD,OAAOsB,cAAc,CAACqC,KAAK,CAAC,UAAUC,MAAM,EAAE;MAC5C,OAAOA,MAAM,CAACC,KAAK;IACrB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,cAAc,CAAC,CAAC;;EAEpB;EACA,IAAIwC,UAAU,GAAGxC,cAAc,CAACA,cAAc,CAACyC,MAAM,GAAG,CAAC,CAAC;EAC1D,IAAIC,eAAe,GAAG;IACpBC,KAAK,EAAEH,UAAU,GAAGA,UAAU,CAACG,KAAK,GAAG,IAAI;IAC3CC,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;MACpC,OAAO;QACLhD,SAAS,EAAE,EAAE,CAACiD,MAAM,CAAClC,SAAS,EAAE,iBAAiB;MACnD,CAAC;IACH;EACF,CAAC;EACD,IAAImC,oBAAoB,GAAGrE,OAAO,CAAC,YAAY;IAC7C,OAAOuC,wBAAwB,GAAG,EAAE,CAAC6B,MAAM,CAAC3E,kBAAkB,CAAC4B,OAAO,CAAC,EAAE,CAAC2C,eAAe,CAAC,CAAC,GAAG3C,OAAO;EACvG,CAAC,EAAE,CAACkB,wBAAwB,EAAElB,OAAO,CAAC,CAAC;EACvC,IAAIiD,2BAA2B,GAAGtE,OAAO,CAAC,YAAY;IACpD,OAAOuC,wBAAwB,GAAG,EAAE,CAAC6B,MAAM,CAAC3E,kBAAkB,CAAC6B,cAAc,CAAC,EAAE,CAAC0C,eAAe,CAAC,CAAC,GAAG1C,cAAc;EACrH,CAAC,EAAE,CAACiB,wBAAwB,EAAEjB,cAAc,CAAC,CAAC;;EAE9C;EACA,IAAIiD,mBAAmB,GAAGvE,OAAO,CAAC,YAAY;IAC5C,IAAIwE,KAAK,GAAGjD,aAAa,CAACiD,KAAK;MAC7BC,IAAI,GAAGlD,aAAa,CAACkD,IAAI;IAC3B,OAAOjF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+B,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACzDkD,IAAI,EAAEjD,SAAS,KAAK,KAAK,GAAG,EAAE,CAAC4C,MAAM,CAAC3E,kBAAkB,CAACgF,IAAI,CAACC,GAAG,CAAC,UAAUb,KAAK,EAAE;QACjF,OAAOA,KAAK,GAAGtB,wBAAwB;MACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGkC,IAAI;MAChBD,KAAK,EAAEhD,SAAS,KAAK,KAAK,GAAGgD,KAAK,GAAG,EAAE,CAACJ,MAAM,CAAC3E,kBAAkB,CAAC+E,KAAK,CAACE,GAAG,CAAC,UAAUb,KAAK,EAAE;QAC3F,OAAOA,KAAK,GAAGtB,wBAAwB;MACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACTH,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACG,wBAAwB,EAAEhB,aAAa,EAAEa,QAAQ,CAAC,CAAC;EACvD,IAAIuC,iBAAiB,GAAGvE,cAAc,CAACC,SAAS,EAAEC,UAAU,CAAC;EAC7D,OAAO,aAAaP,KAAK,CAAC6E,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAErF,aAAa,CAAC;MACnBsF,QAAQ,EAAE;IACZ,CAAC,EAAE1C,QAAQ,GAAG;MACZ2C,GAAG,EAAErD,eAAe;MACpBsD,MAAM,EAAErD;IACV,CAAC,GAAG,CAAC,CAAC,CAAC;IACPZ,GAAG,EAAE2B,YAAY;IACjBvB,SAAS,EAAEtB,UAAU,CAACsB,SAAS,EAAE5B,eAAe,CAAC,CAAC,CAAC,EAAEqC,eAAe,EAAE,CAAC,CAACA,eAAe,CAAC;EAC1F,CAAC,EAAE,aAAa7B,KAAK,CAAC6E,aAAa,CAACtC,cAAc,EAAE;IAClDuC,KAAK,EAAE;MACLI,WAAW,EAAE,OAAO;MACpBC,UAAU,EAAE9D,MAAM,IAAIuD,iBAAiB,GAAG,IAAI,GAAG;IACnD;EACF,CAAC,EAAE,CAAC,CAACvD,MAAM,IAAI,CAACU,gBAAgB,IAAI4B,0BAA0B,KAAK,aAAa3D,KAAK,CAAC6E,aAAa,CAAC3E,QAAQ,EAAE;IAC5GI,SAAS,EAAEsE,iBAAiB,GAAG,EAAE,CAACP,MAAM,CAAC3E,kBAAkB,CAACkF,iBAAiB,CAAC,EAAE,CAACpC,wBAAwB,CAAC,CAAC,GAAG,EAAE;IAChHjC,UAAU,EAAEA,UAAU,GAAG,CAAC;IAC1Be,OAAO,EAAEiD;EACX,CAAC,CAAC,EAAEvC,QAAQ,CAACvC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3DT,aAAa,EAAEgD,mBAAmB;IAClClD,OAAO,EAAEgD,oBAAoB;IAC7B/C,cAAc,EAAEgD;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACF,IAAItD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCN,WAAW,CAACuE,WAAW,GAAG,aAAa;AACzC;;AAEA;AACA;AACA,eAAe,aAAapF,KAAK,CAACqF,IAAI,CAACxE,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}