{"ast": null, "code": "export default function cumsum(values, valueof) {\n  var sum = 0,\n    index = 0;\n  return Float64Array.from(values, valueof === undefined ? v => sum += +v || 0 : v => sum += +valueof(v, index++, values) || 0);\n}", "map": {"version": 3, "names": ["cumsum", "values", "valueof", "sum", "index", "Float64Array", "from", "undefined", "v"], "sources": ["/home/<USER>/itai/node_modules/d3-array/src/cumsum.js"], "sourcesContent": ["export default function cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}"], "mappings": "AAAA,eAAe,SAASA,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC9C,IAAIC,GAAG,GAAG,CAAC;IAAEC,KAAK,GAAG,CAAC;EACtB,OAAOC,YAAY,CAACC,IAAI,CAACL,MAAM,EAAEC,OAAO,KAAKK,SAAS,GAClDC,CAAC,IAAKL,GAAG,IAAI,CAACK,CAAC,IAAI,CAAE,GACrBA,CAAC,IAAKL,GAAG,IAAI,CAACD,OAAO,CAACM,CAAC,EAAEJ,KAAK,EAAE,EAAEH,MAAM,CAAC,IAAI,CAAE,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}