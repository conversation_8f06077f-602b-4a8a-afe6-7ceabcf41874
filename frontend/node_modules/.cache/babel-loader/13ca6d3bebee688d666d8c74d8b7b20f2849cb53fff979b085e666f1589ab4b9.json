{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Space, Tag, Modal, Form, Input, Select, Switch, message, Popconfirm, Card, Row, Col, Statistic } from 'antd';\nimport { serverApi } from '../../services/api';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, StopOutlined, ReloadOutlined, EyeOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst ServerManagement = () => {\n  _s();\n  const [servers, setServers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingServer, setEditingServer] = useState(null);\n  const [commandModalVisible, setCommandModalVisible] = useState(false);\n  const [selectedServer, setSelectedServer] = useState(null);\n  const [form] = Form.useForm();\n  const [commandForm] = Form.useForm();\n\n  // 加载服务器数据\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      const data = await serverApi.getServers();\n      setServers(data.map(server => ({\n        ...server,\n        status: Math.random() > 0.5 ? 'online' : 'offline' // 模拟状态\n      })));\n    } catch (error) {\n      console.error('加载服务器列表失败:', error);\n      message.error('加载服务器列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadServers();\n  }, []);\n  const columns = [{\n    title: '服务器名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: record.status === 'online' ? 'green' : record.status === 'offline' ? 'red' : 'orange',\n        children: record.status === 'online' ? '在线' : record.status === 'offline' ? '离线' : '连接中'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '地址',\n    dataIndex: 'host',\n    key: 'host',\n    render: (text, record) => `${text}:${record.port}`\n  }, {\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '认证方式',\n    dataIndex: 'auth_type',\n    key: 'auth_type',\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: text === 'password' ? 'blue' : 'green',\n      children: text === 'password' ? '密码' : '密钥'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '标签',\n    dataIndex: 'tags',\n    key: 'tags',\n    render: tags => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: tags === null || tags === void 0 ? void 0 : tags.map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: tag\n      }, tag, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 13\n      }, this))\n    }, void 0, false)\n  }, {\n    title: '监控状态',\n    key: 'monitoring',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        color: record.monitoring_enabled ? 'green' : 'default',\n        children: record.monitoring_enabled ? '已启用' : '已禁用'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: record.alert_enabled ? 'orange' : 'default',\n        children: record.alert_enabled ? '告警开启' : '告警关闭'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewServer(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditServer(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: record.monitoring_enabled ? /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 47\n        }, this) : /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 66\n        }, this),\n        onClick: () => handleToggleMonitoring(record),\n        children: record.monitoring_enabled ? '停止监控' : '开始监控'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleExecuteCommand(record),\n        children: \"\\u6267\\u884C\\u547D\\u4EE4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u53F0\\u670D\\u52A1\\u5668\\u5417\\uFF1F\",\n        onConfirm: () => handleDeleteServer(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this)\n  }];\n  const handleAddServer = () => {\n    setEditingServer(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditServer = server => {\n    setEditingServer(server);\n    form.setFieldsValue(server);\n    setModalVisible(true);\n  };\n  const handleViewServer = async server => {\n    try {\n      var _server$tags;\n      // 测试连接状态\n      const connectionResult = await serverApi.testConnection(server.id);\n      Modal.info({\n        title: `服务器详情 - ${server.name}`,\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 16\n            }, this), \" \", server.host, \":\", server.port]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u7528\\u6237\\u540D:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 16\n            }, this), \" \", server.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BA4\\u8BC1\\u65B9\\u5F0F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 16\n            }, this), \" \", server.auth_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u63CF\\u8FF0:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 16\n            }, this), \" \", server.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6807\\u7B7E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 16\n            }, this), \" \", (_server$tags = server.tags) === null || _server$tags === void 0 ? void 0 : _server$tags.join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8FDE\\u63A5\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 16\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: connectionResult.success ? 'green' : 'red',\n              children: connectionResult.success ? '连接正常' : '连接失败'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), connectionResult.success && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5EF6\\u8FDF:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 18\n            }, this), \" \", connectionResult.latency, \"ms\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 16\n            }, this), \" \", new Date(server.created_at).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this),\n        width: 600\n      });\n    } catch (error) {\n      message.error('获取服务器详情失败');\n    }\n  };\n  const handleDeleteServer = async id => {\n    try {\n      await serverApi.deleteServer(id);\n      setServers(servers.filter(s => s.id !== id));\n      message.success('服务器删除成功');\n    } catch (error) {\n      message.error('删除服务器失败');\n    }\n  };\n  const handleToggleMonitoring = async server => {\n    try {\n      if (server.monitoring_enabled) {\n        await serverApi.stopMonitoring(server.id);\n      } else {\n        await serverApi.startMonitoring(server.id);\n      }\n      const updatedServers = servers.map(s => s.id === server.id ? {\n        ...s,\n        monitoring_enabled: !s.monitoring_enabled\n      } : s);\n      setServers(updatedServers);\n      message.success(`${server.name} 监控已${server.monitoring_enabled ? '停止' : '启动'}`);\n    } catch (error) {\n      message.error('切换监控状态失败');\n    }\n  };\n  const handleExecuteCommand = server => {\n    setSelectedServer(server);\n    commandForm.resetFields();\n    setCommandModalVisible(true);\n  };\n  const handleCommandOk = async () => {\n    try {\n      const values = await commandForm.validateFields();\n      const result = await serverApi.executeCommand(selectedServer.id, values);\n      Modal.info({\n        title: '命令执行结果',\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9000\\u51FA\\u7801:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 16\n            }, this), \" \", result.exit_code]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6267\\u884C\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 16\n            }, this), \" \", result.duration, \"\\u79D2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8F93\\u51FA:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 8,\n                borderRadius: 4,\n                maxHeight: 200,\n                overflow: 'auto'\n              },\n              children: result.stdout\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), result.stderr && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9519\\u8BEF:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                background: '#fff2f0',\n                padding: 8,\n                borderRadius: 4,\n                maxHeight: 200,\n                overflow: 'auto',\n                color: 'red'\n              },\n              children: result.stderr\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this),\n        width: 800\n      });\n      setCommandModalVisible(false);\n      commandForm.resetFields();\n    } catch (error) {\n      message.error('命令执行失败');\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingServer) {\n        // 更新服务器\n        await serverApi.updateServer(editingServer.id, values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器更新成功');\n      } else {\n        // 创建新服务器\n        await serverApi.createServer(values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器添加成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error(editingServer ? '更新服务器失败' : '添加服务器失败');\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n    form.resetFields();\n  };\n  const onlineCount = servers.filter(s => s.status === 'online').length;\n  const totalCount = servers.length;\n  const monitoringCount = servers.filter(s => s.monitoring_enabled).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u670D\\u52A1\\u5668\\u6570\",\n            value: totalCount,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u670D\\u52A1\\u5668\",\n            value: onlineCount,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u76D1\\u63A7\\u4E2D\",\n            value: monitoringCount,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u7387\",\n            value: totalCount > 0 ? Math.round(onlineCount / totalCount * 100) : 0,\n            suffix: \"%\",\n            valueStyle: {\n              color: onlineCount / totalCount > 0.8 ? '#52c41a' : '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u670D\\u52A1\\u5668\\u5217\\u8868\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 27\n          }, this),\n          onClick: () => setLoading(true),\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 42\n          }, this),\n          onClick: handleAddServer,\n          children: \"\\u6DFB\\u52A0\\u670D\\u52A1\\u5668\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: servers,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingServer ? '编辑服务器' : '添加服务器',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          port: 22,\n          auth_type: 'password',\n          monitoring_enabled: true,\n          alert_enabled: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u670D\\u52A1\\u5668\\u540D\\u79F0\",\n              name: \"name\",\n              rules: [{\n                required: true,\n                message: '请输入服务器名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u670D\\u52A1\\u5668\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u670D\\u52A1\\u5668\\u5730\\u5740\",\n              name: \"host\",\n              rules: [{\n                required: true,\n                message: '请输入服务器地址'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\\u6216\\u57DF\\u540D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u7AEF\\u53E3\",\n              name: \"port\",\n              rules: [{\n                required: true,\n                message: '请输入端口号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u7528\\u6237\\u540D\",\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"root\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u8BA4\\u8BC1\\u65B9\\u5F0F\",\n              name: \"auth_type\",\n              rules: [{\n                required: true,\n                message: '请选择认证方式'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"password\",\n                  children: \"\\u5BC6\\u7801\\u8BA4\\u8BC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"key\",\n                  children: \"\\u5BC6\\u94A5\\u8BA4\\u8BC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u63CF\\u8FF0\",\n          name: \"description\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u670D\\u52A1\\u5668\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u542F\\u7528\\u76D1\\u63A7\",\n              name: \"monitoring_enabled\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u542F\\u7528\\u544A\\u8B66\",\n              name: \"alert_enabled\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `执行命令 - ${selectedServer === null || selectedServer === void 0 ? void 0 : selectedServer.name}`,\n      open: commandModalVisible,\n      onOk: handleCommandOk,\n      onCancel: () => setCommandModalVisible(false),\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: commandForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u547D\\u4EE4\",\n          name: \"command\",\n          rules: [{\n            required: true,\n            message: '请输入要执行的命令'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 3,\n            placeholder: \"\\u4F8B\\u5982: ls -la\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5DE5\\u4F5C\\u76EE\\u5F55\",\n          name: \"working_dir\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4F8B\\u5982: /home/<USER>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u73AF\\u5883\\u53D8\\u91CF\",\n          name: \"env_vars\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 2,\n            placeholder: \"\\u4F8B\\u5982: PATH=/usr/bin:$PATH\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u8D85\\u65F6\\u65F6\\u95F4(\\u79D2)\",\n          name: \"timeout\",\n          initialValue: 300,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 5\n  }, this);\n};\n_s(ServerManagement, \"ZFP59UYYaOx6AEcl/Jb6xcdNGrA=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = ServerManagement;\nconst handleCommandOk = async () => {\n  try {\n    const values = await commandForm.validateFields();\n    const result = await serverApi.executeCommand(selectedServer.id, values);\n    Modal.info({\n      title: '命令执行结果',\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u9000\\u51FA\\u7801:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 14\n          }, this), \" \", result.exit_code]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6267\\u884C\\u65F6\\u95F4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 14\n          }, this), \" \", result.duration, \"\\u79D2\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8F93\\u51FA:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            style: {\n              background: '#f5f5f5',\n              padding: 8,\n              borderRadius: 4,\n              maxHeight: 200,\n              overflow: 'auto'\n            },\n            children: result.stdout\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this), result.stderr && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u9519\\u8BEF:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            style: {\n              background: '#fff2f0',\n              padding: 8,\n              borderRadius: 4,\n              maxHeight: 200,\n              overflow: 'auto',\n              color: 'red'\n            },\n            children: result.stderr\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 9\n      }, this),\n      width: 800\n    });\n    setCommandModalVisible(false);\n    commandForm.resetFields();\n  } catch (error) {\n    message.error('命令执行失败');\n  }\n};\nexport default ServerManagement;\nvar _c;\n$RefreshReg$(_c, \"ServerManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "Switch", "message", "Popconfirm", "Card", "Row", "Col", "Statistic", "serverApi", "PlusOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "StopOutlined", "ReloadOutlined", "EyeOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "ServerManagement", "_s", "servers", "setServers", "loading", "setLoading", "modalVisible", "setModalVisible", "editingServer", "setEditingServer", "commandModalVisible", "setCommandModalVisible", "selectedServer", "setSelectedServer", "form", "useForm", "commandForm", "loadServers", "data", "getServers", "map", "server", "status", "Math", "random", "error", "console", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "port", "tags", "tag", "_", "monitoring_enabled", "alert_enabled", "size", "type", "icon", "onClick", "handleViewServer", "handleEditServer", "handleToggleMonitoring", "handleExecuteCommand", "onConfirm", "handleDeleteServer", "id", "okText", "cancelText", "danger", "handleAddServer", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_server$tags", "connectionResult", "testConnection", "info", "name", "content", "host", "username", "auth_type", "description", "join", "success", "latency", "Date", "created_at", "toLocaleString", "width", "deleteServer", "filter", "s", "stopMonitoring", "startMonitoring", "updatedServers", "handleCommandOk", "values", "validateFields", "result", "executeCommand", "exit_code", "duration", "style", "marginTop", "background", "padding", "borderRadius", "maxHeight", "overflow", "stdout", "stderr", "handleModalOk", "updateServer", "createServer", "handleModalCancel", "onlineCount", "length", "totalCount", "monitoringCount", "gutter", "marginBottom", "span", "value", "valueStyle", "round", "suffix", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onOk", "onCancel", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "TextArea", "rows", "valuePropName", "initialValue", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Switch,\n  message,\n  Popconfirm,\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Spin,\n  Alert,\n} from 'antd';\nimport { serverApi } from '../../services/api';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  StopOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\ninterface Server {\n  id: number;\n  name: string;\n  host: string;\n  port: number;\n  username: string;\n  auth_type: string;\n  description?: string;\n  tags?: string[];\n  monitoring_enabled: boolean;\n  alert_enabled: boolean;\n  is_active: boolean;\n  status?: 'online' | 'offline' | 'connecting';\n  created_at: string;\n  updated_at: string;\n}\n\nconst { Option } = Select;\n\nconst ServerManagement: React.FC = () => {\n  const [servers, setServers] = useState<Server[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingServer, setEditingServer] = useState<Server | null>(null);\n  const [commandModalVisible, setCommandModalVisible] = useState(false);\n  const [selectedServer, setSelectedServer] = useState<Server | null>(null);\n  const [form] = Form.useForm();\n  const [commandForm] = Form.useForm();\n\n  // 加载服务器数据\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      const data = await serverApi.getServers();\n      setServers(data.map((server: any) => ({\n        ...server,\n        status: Math.random() > 0.5 ? 'online' : 'offline' // 模拟状态\n      })));\n    } catch (error) {\n      console.error('加载服务器列表失败:', error);\n      message.error('加载服务器列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadServers();\n  }, []);\n\n  const columns: ColumnsType<Server> = [\n    {\n      title: '服务器名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <span>{text}</span>\n          <Tag color={record.status === 'online' ? 'green' : record.status === 'offline' ? 'red' : 'orange'}>\n            {record.status === 'online' ? '在线' : record.status === 'offline' ? '离线' : '连接中'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '地址',\n      dataIndex: 'host',\n      key: 'host',\n      render: (text, record) => `${text}:${record.port}`,\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '认证方式',\n      dataIndex: 'auth_type',\n      key: 'auth_type',\n      render: (text) => (\n        <Tag color={text === 'password' ? 'blue' : 'green'}>\n          {text === 'password' ? '密码' : '密钥'}\n        </Tag>\n      ),\n    },\n    {\n      title: '标签',\n      dataIndex: 'tags',\n      key: 'tags',\n      render: (tags: string[]) => (\n        <>\n          {tags?.map((tag) => (\n            <Tag key={tag} color=\"blue\">\n              {tag}\n            </Tag>\n          ))}\n        </>\n      ),\n    },\n    {\n      title: '监控状态',\n      key: 'monitoring',\n      render: (_, record) => (\n        <Space>\n          <Tag color={record.monitoring_enabled ? 'green' : 'default'}>\n            {record.monitoring_enabled ? '已启用' : '已禁用'}\n          </Tag>\n          <Tag color={record.alert_enabled ? 'orange' : 'default'}>\n            {record.alert_enabled ? '告警开启' : '告警关闭'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"link\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewServer(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditServer(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            icon={record.monitoring_enabled ? <StopOutlined /> : <PlayCircleOutlined />}\n            onClick={() => handleToggleMonitoring(record)}\n          >\n            {record.monitoring_enabled ? '停止监控' : '开始监控'}\n          </Button>\n          <Button\n            type=\"link\"\n            onClick={() => handleExecuteCommand(record)}\n          >\n            执行命令\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这台服务器吗？\"\n            onConfirm={() => handleDeleteServer(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const handleAddServer = () => {\n    setEditingServer(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditServer = (server: Server) => {\n    setEditingServer(server);\n    form.setFieldsValue(server);\n    setModalVisible(true);\n  };\n\n  const handleViewServer = async (server: Server) => {\n    try {\n      // 测试连接状态\n      const connectionResult = await serverApi.testConnection(server.id);\n\n      Modal.info({\n        title: `服务器详情 - ${server.name}`,\n        content: (\n          <div>\n            <p><strong>地址:</strong> {server.host}:{server.port}</p>\n            <p><strong>用户名:</strong> {server.username}</p>\n            <p><strong>认证方式:</strong> {server.auth_type}</p>\n            <p><strong>描述:</strong> {server.description}</p>\n            <p><strong>标签:</strong> {server.tags?.join(', ')}</p>\n            <p><strong>连接状态:</strong>\n              <Tag color={(connectionResult as any).success ? 'green' : 'red'}>\n                {(connectionResult as any).success ? '连接正常' : '连接失败'}\n              </Tag>\n            </p>\n            {(connectionResult as any).success && (\n              <p><strong>延迟:</strong> {(connectionResult as any).latency}ms</p>\n            )}\n            <p><strong>创建时间:</strong> {new Date(server.created_at).toLocaleString()}</p>\n          </div>\n        ),\n        width: 600,\n      });\n    } catch (error) {\n      message.error('获取服务器详情失败');\n    }\n  };\n\n  const handleDeleteServer = async (id: number) => {\n    try {\n      await serverApi.deleteServer(id);\n      setServers(servers.filter(s => s.id !== id));\n      message.success('服务器删除成功');\n    } catch (error) {\n      message.error('删除服务器失败');\n    }\n  };\n\n  const handleToggleMonitoring = async (server: Server) => {\n    try {\n      if (server.monitoring_enabled) {\n        await serverApi.stopMonitoring(server.id);\n      } else {\n        await serverApi.startMonitoring(server.id);\n      }\n\n      const updatedServers = servers.map(s =>\n        s.id === server.id\n          ? { ...s, monitoring_enabled: !s.monitoring_enabled }\n          : s\n      );\n      setServers(updatedServers);\n      message.success(\n        `${server.name} 监控已${server.monitoring_enabled ? '停止' : '启动'}`\n      );\n    } catch (error) {\n      message.error('切换监控状态失败');\n    }\n  };\n\n  const handleExecuteCommand = (server: Server) => {\n    setSelectedServer(server);\n    commandForm.resetFields();\n    setCommandModalVisible(true);\n  };\n\n  const handleCommandOk = async () => {\n    try {\n      const values = await commandForm.validateFields();\n      const result = await serverApi.executeCommand(selectedServer!.id, values);\n\n      Modal.info({\n        title: '命令执行结果',\n        content: (\n          <div>\n            <p><strong>退出码:</strong> {(result as any).exit_code}</p>\n            <p><strong>执行时间:</strong> {(result as any).duration}秒</p>\n            <div style={{ marginTop: 16 }}>\n              <strong>输出:</strong>\n              <pre style={{\n                background: '#f5f5f5',\n                padding: 8,\n                borderRadius: 4,\n                maxHeight: 200,\n                overflow: 'auto'\n              }}>\n                {(result as any).stdout}\n              </pre>\n            </div>\n            {(result as any).stderr && (\n              <div style={{ marginTop: 16 }}>\n                <strong>错误:</strong>\n                <pre style={{\n                  background: '#fff2f0',\n                  padding: 8,\n                  borderRadius: 4,\n                  maxHeight: 200,\n                  overflow: 'auto',\n                  color: 'red'\n                }}>\n                  {(result as any).stderr}\n                </pre>\n              </div>\n            )}\n          </div>\n        ),\n        width: 800,\n      });\n\n      setCommandModalVisible(false);\n      commandForm.resetFields();\n    } catch (error) {\n      message.error('命令执行失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingServer) {\n        // 更新服务器\n        await serverApi.updateServer(editingServer.id, values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器更新成功');\n      } else {\n        // 创建新服务器\n        await serverApi.createServer(values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器添加成功');\n      }\n      \n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error(editingServer ? '更新服务器失败' : '添加服务器失败');\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n    form.resetFields();\n  };\n\n  const onlineCount = servers.filter(s => s.status === 'online').length;\n  const totalCount = servers.length;\n  const monitoringCount = servers.filter(s => s.monitoring_enabled).length;\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总服务器数\"\n              value={totalCount}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"在线服务器\"\n              value={onlineCount}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"监控中\"\n              value={monitoringCount}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"在线率\"\n              value={totalCount > 0 ? Math.round((onlineCount / totalCount) * 100) : 0}\n              suffix=\"%\"\n              valueStyle={{ color: onlineCount / totalCount > 0.8 ? '#52c41a' : '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card\n        title=\"服务器列表\"\n        extra={\n          <Space>\n            <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>\n              刷新\n            </Button>\n            <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddServer}>\n              添加服务器\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={servers}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingServer ? '编辑服务器' : '添加服务器'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            port: 22,\n            auth_type: 'password',\n            monitoring_enabled: true,\n            alert_enabled: true,\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                label=\"服务器名称\"\n                name=\"name\"\n                rules={[{ required: true, message: '请输入服务器名称' }]}\n              >\n                <Input placeholder=\"请输入服务器名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label=\"服务器地址\"\n                name=\"host\"\n                rules={[{ required: true, message: '请输入服务器地址' }]}\n              >\n                <Input placeholder=\"请输入IP地址或域名\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                label=\"端口\"\n                name=\"port\"\n                rules={[{ required: true, message: '请输入端口号' }]}\n              >\n                <Input type=\"number\" placeholder=\"22\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                label=\"用户名\"\n                name=\"username\"\n                rules={[{ required: true, message: '请输入用户名' }]}\n              >\n                <Input placeholder=\"root\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                label=\"认证方式\"\n                name=\"auth_type\"\n                rules={[{ required: true, message: '请选择认证方式' }]}\n              >\n                <Select>\n                  <Option value=\"password\">密码认证</Option>\n                  <Option value=\"key\">密钥认证</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            label=\"描述\"\n            name=\"description\"\n          >\n            <Input.TextArea rows={3} placeholder=\"请输入服务器描述\" />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                label=\"启用监控\"\n                name=\"monitoring_enabled\"\n                valuePropName=\"checked\"\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label=\"启用告警\"\n                name=\"alert_enabled\"\n                valuePropName=\"checked\"\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n\n      <Modal\n        title={`执行命令 - ${selectedServer?.name}`}\n        open={commandModalVisible}\n        onOk={handleCommandOk}\n        onCancel={() => setCommandModalVisible(false)}\n        width={800}\n      >\n        <Form\n          form={commandForm}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            label=\"命令\"\n            name=\"command\"\n            rules={[{ required: true, message: '请输入要执行的命令' }]}\n          >\n            <Input.TextArea\n              rows={3}\n              placeholder=\"例如: ls -la\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            label=\"工作目录\"\n            name=\"working_dir\"\n          >\n            <Input placeholder=\"例如: /home/<USER>\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"环境变量\"\n            name=\"env_vars\"\n          >\n            <Input.TextArea\n              rows={2}\n              placeholder=\"例如: PATH=/usr/bin:$PATH\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            label=\"超时时间(秒)\"\n            name=\"timeout\"\n            initialValue={300}\n          >\n            <Input type=\"number\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nconst handleCommandOk = async () => {\n  try {\n    const values = await commandForm.validateFields();\n    const result = await serverApi.executeCommand(selectedServer!.id, values);\n\n    Modal.info({\n      title: '命令执行结果',\n      content: (\n        <div>\n          <p><strong>退出码:</strong> {(result as any).exit_code}</p>\n          <p><strong>执行时间:</strong> {(result as any).duration}秒</p>\n          <div style={{ marginTop: 16 }}>\n            <strong>输出:</strong>\n            <pre style={{\n              background: '#f5f5f5',\n              padding: 8,\n              borderRadius: 4,\n              maxHeight: 200,\n              overflow: 'auto'\n            }}>\n              {(result as any).stdout}\n            </pre>\n          </div>\n          {(result as any).stderr && (\n            <div style={{ marginTop: 16 }}>\n              <strong>错误:</strong>\n              <pre style={{\n                background: '#fff2f0',\n                padding: 8,\n                borderRadius: 4,\n                maxHeight: 200,\n                overflow: 'auto',\n                color: 'red'\n              }}>\n                {(result as any).stderr}\n              </pre>\n            </div>\n          )}\n        </div>\n      ),\n      width: 800,\n    });\n\n    setCommandModalVisible(false);\n    commandForm.resetFields();\n  } catch (error) {\n    message.error('命令执行失败');\n  }\n};\n\nexport default ServerManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,QAGJ,MAAM;AACb,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,WAAW,QACN,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAoB3B,MAAM;EAAEC;AAAO,CAAC,GAAGpB,MAAM;AAEzB,MAAMqB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC4C,IAAI,CAAC,GAAGrC,IAAI,CAACsC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,CAAC,GAAGvC,IAAI,CAACsC,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMa,IAAI,GAAG,MAAM/B,SAAS,CAACgC,UAAU,CAAC,CAAC;MACzChB,UAAU,CAACe,IAAI,CAACE,GAAG,CAAEC,MAAW,KAAM;QACpC,GAAGA,MAAM;QACTC,MAAM,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,SAAS,CAAC;MACrD,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC5C,OAAO,CAAC4C,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlC,SAAS,CAAC,MAAM;IACd8C,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,OAA4B,GAAG,CACnC;IACEC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBrC,OAAA,CAACtB,KAAK;MAAA4D,QAAA,gBACJtC,OAAA;QAAAsC,QAAA,EAAOF;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnB1C,OAAA,CAACrB,GAAG;QAACgE,KAAK,EAAEN,MAAM,CAACX,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAGW,MAAM,CAACX,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,QAAS;QAAAY,QAAA,EAC/FD,MAAM,CAACX,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAGW,MAAM,CAACX,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK,GAAGD,IAAI,IAAIC,MAAM,CAACO,IAAI;EAClD,CAAC,EACD;IACEZ,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGC,IAAI,iBACXpC,OAAA,CAACrB,GAAG;MAACgE,KAAK,EAAEP,IAAI,KAAK,UAAU,GAAG,MAAM,GAAG,OAAQ;MAAAE,QAAA,EAChDF,IAAI,KAAK,UAAU,GAAG,IAAI,GAAG;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGU,IAAc,iBACrB7C,OAAA,CAAAE,SAAA;MAAAoC,QAAA,EACGO,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAErB,GAAG,CAAEsB,GAAG,iBACb9C,OAAA,CAACrB,GAAG;QAAWgE,KAAK,EAAC,MAAM;QAAAL,QAAA,EACxBQ;MAAG,GADIA,GAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CACN;IAAC,gBACF;EAEN,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACY,CAAC,EAAEV,MAAM,kBAChBrC,OAAA,CAACtB,KAAK;MAAA4D,QAAA,gBACJtC,OAAA,CAACrB,GAAG;QAACgE,KAAK,EAAEN,MAAM,CAACW,kBAAkB,GAAG,OAAO,GAAG,SAAU;QAAAV,QAAA,EACzDD,MAAM,CAACW,kBAAkB,GAAG,KAAK,GAAG;MAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACN1C,OAAA,CAACrB,GAAG;QAACgE,KAAK,EAAEN,MAAM,CAACY,aAAa,GAAG,QAAQ,GAAG,SAAU;QAAAX,QAAA,EACrDD,MAAM,CAACY,aAAa,GAAG,MAAM,GAAG;MAAM;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACY,CAAC,EAAEV,MAAM,kBAChBrC,OAAA,CAACtB,KAAK;MAACwE,IAAI,EAAC,QAAQ;MAAAZ,QAAA,gBAClBtC,OAAA,CAACvB,MAAM;QACL0E,IAAI,EAAC,MAAM;QACXC,IAAI,eAAEpD,OAAA,CAACF,WAAW;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBW,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAACjB,MAAM,CAAE;QAAAC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1C,OAAA,CAACvB,MAAM;QACL0E,IAAI,EAAC,MAAM;QACXC,IAAI,eAAEpD,OAAA,CAACP,YAAY;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBW,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAAClB,MAAM,CAAE;QAAAC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1C,OAAA,CAACvB,MAAM;QACL0E,IAAI,EAAC,MAAM;QACXC,IAAI,EAAEf,MAAM,CAACW,kBAAkB,gBAAGhD,OAAA,CAACJ,YAAY;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG1C,OAAA,CAACL,kBAAkB;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5EW,OAAO,EAAEA,CAAA,KAAMG,sBAAsB,CAACnB,MAAM,CAAE;QAAAC,QAAA,EAE7CD,MAAM,CAACW,kBAAkB,GAAG,MAAM,GAAG;MAAM;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACT1C,OAAA,CAACvB,MAAM;QACL0E,IAAI,EAAC,MAAM;QACXE,OAAO,EAAEA,CAAA,KAAMI,oBAAoB,CAACpB,MAAM,CAAE;QAAAC,QAAA,EAC7C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1C,OAAA,CAACd,UAAU;QACT8C,KAAK,EAAC,0EAAc;QACpB0B,SAAS,EAAEA,CAAA,KAAMC,kBAAkB,CAACtB,MAAM,CAACuB,EAAE,CAAE;QAC/CC,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAxB,QAAA,eAEftC,OAAA,CAACvB,MAAM;UAAC0E,IAAI,EAAC,MAAM;UAACY,MAAM;UAACX,IAAI,eAAEpD,OAAA,CAACN,cAAc;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMsB,eAAe,GAAGA,CAAA,KAAM;IAC5BnD,gBAAgB,CAAC,IAAI,CAAC;IACtBK,IAAI,CAAC+C,WAAW,CAAC,CAAC;IAClBtD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4C,gBAAgB,GAAI9B,MAAc,IAAK;IAC3CZ,gBAAgB,CAACY,MAAM,CAAC;IACxBP,IAAI,CAACgD,cAAc,CAACzC,MAAM,CAAC;IAC3Bd,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM2C,gBAAgB,GAAG,MAAO7B,MAAc,IAAK;IACjD,IAAI;MAAA,IAAA0C,YAAA;MACF;MACA,MAAMC,gBAAgB,GAAG,MAAM7E,SAAS,CAAC8E,cAAc,CAAC5C,MAAM,CAACmC,EAAE,CAAC;MAElEhF,KAAK,CAAC0F,IAAI,CAAC;QACTtC,KAAK,EAAE,WAAWP,MAAM,CAAC8C,IAAI,EAAE;QAC/BC,OAAO,eACLxE,OAAA;UAAAsC,QAAA,gBACEtC,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAACgD,IAAI,EAAC,GAAC,EAAChD,MAAM,CAACmB,IAAI;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvD1C,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAACiD,QAAQ;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9C1C,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAACkD,SAAS;UAAA;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChD1C,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACjB,MAAM,CAACmD,WAAW;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChD1C,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,GAAAyB,YAAA,GAAC1C,MAAM,CAACoB,IAAI,cAAAsB,YAAA,uBAAXA,YAAA,CAAaU,IAAI,CAAC,IAAI,CAAC;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD1C,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvB1C,OAAA,CAACrB,GAAG;cAACgE,KAAK,EAAGyB,gBAAgB,CAASU,OAAO,GAAG,OAAO,GAAG,KAAM;cAAAxC,QAAA,EAC5D8B,gBAAgB,CAASU,OAAO,GAAG,MAAM,GAAG;YAAM;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACF0B,gBAAgB,CAASU,OAAO,iBAChC9E,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAE0B,gBAAgB,CAASW,OAAO,EAAC,IAAE;UAAA;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACjE,eACD1C,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIsC,IAAI,CAACvD,MAAM,CAACwD,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CACN;QACDyC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,WAAW,CAAC;IAC5B;EACF,CAAC;EAED,MAAM8B,kBAAkB,GAAG,MAAOC,EAAU,IAAK;IAC/C,IAAI;MACF,MAAMrE,SAAS,CAAC6F,YAAY,CAACxB,EAAE,CAAC;MAChCrD,UAAU,CAACD,OAAO,CAAC+E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC1B,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC5C3E,OAAO,CAAC6F,OAAO,CAAC,SAAS,CAAC;IAC5B,CAAC,CAAC,OAAOjD,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,SAAS,CAAC;IAC1B;EACF,CAAC;EAED,MAAM2B,sBAAsB,GAAG,MAAO/B,MAAc,IAAK;IACvD,IAAI;MACF,IAAIA,MAAM,CAACuB,kBAAkB,EAAE;QAC7B,MAAMzD,SAAS,CAACgG,cAAc,CAAC9D,MAAM,CAACmC,EAAE,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMrE,SAAS,CAACiG,eAAe,CAAC/D,MAAM,CAACmC,EAAE,CAAC;MAC5C;MAEA,MAAM6B,cAAc,GAAGnF,OAAO,CAACkB,GAAG,CAAC8D,CAAC,IAClCA,CAAC,CAAC1B,EAAE,KAAKnC,MAAM,CAACmC,EAAE,GACd;QAAE,GAAG0B,CAAC;QAAEtC,kBAAkB,EAAE,CAACsC,CAAC,CAACtC;MAAmB,CAAC,GACnDsC,CACN,CAAC;MACD/E,UAAU,CAACkF,cAAc,CAAC;MAC1BxG,OAAO,CAAC6F,OAAO,CACb,GAAGrD,MAAM,CAAC8C,IAAI,OAAO9C,MAAM,CAACuB,kBAAkB,GAAG,IAAI,GAAG,IAAI,EAC9D,CAAC;IACH,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAM4B,oBAAoB,GAAIhC,MAAc,IAAK;IAC/CR,iBAAiB,CAACQ,MAAM,CAAC;IACzBL,WAAW,CAAC6C,WAAW,CAAC,CAAC;IACzBlD,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM2E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvE,WAAW,CAACwE,cAAc,CAAC,CAAC;MACjD,MAAMC,MAAM,GAAG,MAAMtG,SAAS,CAACuG,cAAc,CAAC9E,cAAc,CAAE4C,EAAE,EAAE+B,MAAM,CAAC;MAEzE/G,KAAK,CAAC0F,IAAI,CAAC;QACTtC,KAAK,EAAE,QAAQ;QACfwC,OAAO,eACLxE,OAAA;UAAAsC,QAAA,gBACEtC,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAEmD,MAAM,CAASE,SAAS;UAAA;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD1C,OAAA;YAAAsC,QAAA,gBAAGtC,OAAA;cAAAsC,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAEmD,MAAM,CAASG,QAAQ,EAAC,QAAC;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzD1C,OAAA;YAAKiG,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAG,CAAE;YAAA5D,QAAA,gBAC5BtC,OAAA;cAAAsC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpB1C,OAAA;cAAKiG,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,CAAC;gBACVC,YAAY,EAAE,CAAC;gBACfC,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE;cACZ,CAAE;cAAAjE,QAAA,EACEuD,MAAM,CAASW;YAAM;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACJmD,MAAM,CAASY,MAAM,iBACrBzG,OAAA;YAAKiG,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAG,CAAE;YAAA5D,QAAA,gBAC5BtC,OAAA;cAAAsC,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpB1C,OAAA;cAAKiG,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,CAAC;gBACVC,YAAY,EAAE,CAAC;gBACfC,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE,MAAM;gBAChB5D,KAAK,EAAE;cACT,CAAE;cAAAL,QAAA,EACEuD,MAAM,CAASY;YAAM;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACDyC,KAAK,EAAE;MACT,CAAC,CAAC;MAEFpE,sBAAsB,CAAC,KAAK,CAAC;MAC7BK,WAAW,CAAC6C,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAM6E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMf,MAAM,GAAG,MAAMzE,IAAI,CAAC0E,cAAc,CAAC,CAAC;MAE1C,IAAIhF,aAAa,EAAE;QACjB;QACA,MAAMrB,SAAS,CAACoH,YAAY,CAAC/F,aAAa,CAACgD,EAAE,EAAE+B,MAAM,CAAC;QACtD,MAAMtE,WAAW,CAAC,CAAC,CAAC,CAAC;QACrBpC,OAAO,CAAC6F,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAC,MAAM;QACL;QACA,MAAMvF,SAAS,CAACqH,YAAY,CAACjB,MAAM,CAAC;QACpC,MAAMtE,WAAW,CAAC,CAAC,CAAC,CAAC;QACrBpC,OAAO,CAAC6F,OAAO,CAAC,SAAS,CAAC;MAC5B;MAEAnE,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAAC+C,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B5C,OAAO,CAAC4C,KAAK,CAACjB,aAAa,GAAG,SAAS,GAAG,SAAS,CAAC;IACtD;EACF,CAAC;EAED,MAAMiG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlG,eAAe,CAAC,KAAK,CAAC;IACtBO,IAAI,CAAC+C,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,MAAM6C,WAAW,GAAGxG,OAAO,CAAC+E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,MAAM,KAAK,QAAQ,CAAC,CAACqF,MAAM;EACrE,MAAMC,UAAU,GAAG1G,OAAO,CAACyG,MAAM;EACjC,MAAME,eAAe,GAAG3G,OAAO,CAAC+E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtC,kBAAkB,CAAC,CAAC+D,MAAM;EAExE,oBACE/G,OAAA;IAAAsC,QAAA,gBACEtC,OAAA,CAACZ,GAAG;MAAC8H,MAAM,EAAE,EAAG;MAACjB,KAAK,EAAE;QAAEkB,YAAY,EAAE;MAAG,CAAE;MAAA7E,QAAA,gBAC3CtC,OAAA,CAACX,GAAG;QAAC+H,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,gCAAO;YACbqF,KAAK,EAAEL,UAAW;YAClBM,UAAU,EAAE;cAAE3E,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAAC+H,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,gCAAO;YACbqF,KAAK,EAAEP,WAAY;YACnBQ,UAAU,EAAE;cAAE3E,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAAC+H,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,oBAAK;YACXqF,KAAK,EAAEJ,eAAgB;YACvBK,UAAU,EAAE;cAAE3E,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAAC+H,IAAI,EAAE,CAAE;QAAA9E,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,oBAAK;YACXqF,KAAK,EAAEL,UAAU,GAAG,CAAC,GAAGrF,IAAI,CAAC4F,KAAK,CAAET,WAAW,GAAGE,UAAU,GAAI,GAAG,CAAC,GAAG,CAAE;YACzEQ,MAAM,EAAC,GAAG;YACVF,UAAU,EAAE;cAAE3E,KAAK,EAAEmE,WAAW,GAAGE,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1C,OAAA,CAACb,IAAI;MACH6C,KAAK,EAAC,gCAAO;MACbyF,KAAK,eACHzH,OAAA,CAACtB,KAAK;QAAA4D,QAAA,gBACJtC,OAAA,CAACvB,MAAM;UAAC2E,IAAI,eAAEpD,OAAA,CAACH,cAAc;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACW,OAAO,EAAEA,CAAA,KAAM5C,UAAU,CAAC,IAAI,CAAE;UAAA6B,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA,CAACvB,MAAM;UAAC0E,IAAI,EAAC,SAAS;UAACC,IAAI,eAAEpD,OAAA,CAACR,YAAY;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACW,OAAO,EAAEW,eAAgB;UAAA1B,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAEDtC,OAAA,CAACxB,KAAK;QACJuD,OAAO,EAAEA,OAAQ;QACjB2F,UAAU,EAAEpH,OAAQ;QACpBqH,MAAM,EAAC,IAAI;QACXnH,OAAO,EAAEA,OAAQ;QACjBoH,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAAzF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP1C,OAAA,CAACpB,KAAK;MACJoD,KAAK,EAAEpB,aAAa,GAAG,OAAO,GAAG,OAAQ;MACzCsH,IAAI,EAAExH,YAAa;MACnByH,IAAI,EAAEzB,aAAc;MACpB0B,QAAQ,EAAEvB,iBAAkB;MAC5B1B,KAAK,EAAE,GAAI;MAAA7C,QAAA,eAEXtC,OAAA,CAACnB,IAAI;QACHqC,IAAI,EAAEA,IAAK;QACXmH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb1F,IAAI,EAAE,EAAE;UACR+B,SAAS,EAAE,UAAU;UACrB3B,kBAAkB,EAAE,IAAI;UACxBC,aAAa,EAAE;QACjB,CAAE;QAAAX,QAAA,gBAEFtC,OAAA,CAACZ,GAAG;UAAC8H,MAAM,EAAE,EAAG;UAAA5E,QAAA,gBACdtC,OAAA,CAACX,GAAG;YAAC+H,IAAI,EAAE,EAAG;YAAA9E,QAAA,eACZtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbjE,IAAI,EAAC,MAAM;cACXkE,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzJ,OAAO,EAAE;cAAW,CAAC,CAAE;cAAAqD,QAAA,eAEjDtC,OAAA,CAAClB,KAAK;gBAAC6J,WAAW,EAAC;cAAU;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1C,OAAA,CAACX,GAAG;YAAC+H,IAAI,EAAE,EAAG;YAAA9E,QAAA,eACZtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbjE,IAAI,EAAC,MAAM;cACXkE,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzJ,OAAO,EAAE;cAAW,CAAC,CAAE;cAAAqD,QAAA,eAEjDtC,OAAA,CAAClB,KAAK;gBAAC6J,WAAW,EAAC;cAAY;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA,CAACZ,GAAG;UAAC8H,MAAM,EAAE,EAAG;UAAA5E,QAAA,gBACdtC,OAAA,CAACX,GAAG;YAAC+H,IAAI,EAAE,CAAE;YAAA9E,QAAA,eACXtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;cACRC,KAAK,EAAC,cAAI;cACVjE,IAAI,EAAC,MAAM;cACXkE,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzJ,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAqD,QAAA,eAE/CtC,OAAA,CAAClB,KAAK;gBAACqE,IAAI,EAAC,QAAQ;gBAACwF,WAAW,EAAC;cAAI;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1C,OAAA,CAACX,GAAG;YAAC+H,IAAI,EAAE,CAAE;YAAA9E,QAAA,eACXtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXjE,IAAI,EAAC,UAAU;cACfkE,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzJ,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAqD,QAAA,eAE/CtC,OAAA,CAAClB,KAAK;gBAAC6J,WAAW,EAAC;cAAM;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1C,OAAA,CAACX,GAAG;YAAC+H,IAAI,EAAE,CAAE;YAAA9E,QAAA,eACXtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZjE,IAAI,EAAC,WAAW;cAChBkE,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEzJ,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqD,QAAA,eAEhDtC,OAAA,CAACjB,MAAM;gBAAAuD,QAAA,gBACLtC,OAAA,CAACG,MAAM;kBAACkH,KAAK,EAAC,UAAU;kBAAA/E,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC1C,OAAA,CAACG,MAAM;kBAACkH,KAAK,EAAC,KAAK;kBAAA/E,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA,CAACnB,IAAI,CAAC0J,IAAI;UACRC,KAAK,EAAC,cAAI;UACVjE,IAAI,EAAC,aAAa;UAAAjC,QAAA,eAElBtC,OAAA,CAAClB,KAAK,CAAC8J,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACF,WAAW,EAAC;UAAU;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAEZ1C,OAAA,CAACZ,GAAG;UAAC8H,MAAM,EAAE,EAAG;UAAA5E,QAAA,gBACdtC,OAAA,CAACX,GAAG;YAAC+H,IAAI,EAAE,EAAG;YAAA9E,QAAA,eACZtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZjE,IAAI,EAAC,oBAAoB;cACzBuE,aAAa,EAAC,SAAS;cAAAxG,QAAA,eAEvBtC,OAAA,CAAChB,MAAM;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1C,OAAA,CAACX,GAAG;YAAC+H,IAAI,EAAE,EAAG;YAAA9E,QAAA,eACZtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZjE,IAAI,EAAC,eAAe;cACpBuE,aAAa,EAAC,SAAS;cAAAxG,QAAA,eAEvBtC,OAAA,CAAChB,MAAM;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAER1C,OAAA,CAACpB,KAAK;MACJoD,KAAK,EAAE,UAAUhB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEuD,IAAI,EAAG;MACxC2D,IAAI,EAAEpH,mBAAoB;MAC1BqH,IAAI,EAAEzC,eAAgB;MACtB0C,QAAQ,EAAEA,CAAA,KAAMrH,sBAAsB,CAAC,KAAK,CAAE;MAC9CoE,KAAK,EAAE,GAAI;MAAA7C,QAAA,eAEXtC,OAAA,CAACnB,IAAI;QACHqC,IAAI,EAAEE,WAAY;QAClBiH,MAAM,EAAC,UAAU;QAAA/F,QAAA,gBAEjBtC,OAAA,CAACnB,IAAI,CAAC0J,IAAI;UACRC,KAAK,EAAC,cAAI;UACVjE,IAAI,EAAC,SAAS;UACdkE,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzJ,OAAO,EAAE;UAAY,CAAC,CAAE;UAAAqD,QAAA,eAElDtC,OAAA,CAAClB,KAAK,CAAC8J,QAAQ;YACbC,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAY;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1C,OAAA,CAACnB,IAAI,CAAC0J,IAAI;UACRC,KAAK,EAAC,0BAAM;UACZjE,IAAI,EAAC,aAAa;UAAAjC,QAAA,eAElBtC,OAAA,CAAClB,KAAK;YAAC6J,WAAW,EAAC;UAAgB;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEZ1C,OAAA,CAACnB,IAAI,CAAC0J,IAAI;UACRC,KAAK,EAAC,0BAAM;UACZjE,IAAI,EAAC,UAAU;UAAAjC,QAAA,eAEftC,OAAA,CAAClB,KAAK,CAAC8J,QAAQ;YACbC,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAyB;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1C,OAAA,CAACnB,IAAI,CAAC0J,IAAI;UACRC,KAAK,EAAC,kCAAS;UACfjE,IAAI,EAAC,SAAS;UACdwE,YAAY,EAAE,GAAI;UAAAzG,QAAA,eAElBtC,OAAA,CAAClB,KAAK;YAACqE,IAAI,EAAC;UAAQ;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrC,EAAA,CA9gBID,gBAA0B;EAAA,QAOfvB,IAAI,CAACsC,OAAO,EACLtC,IAAI,CAACsC,OAAO;AAAA;AAAA6H,EAAA,GAR9B5I,gBAA0B;AAghBhC,MAAMsF,eAAe,GAAG,MAAAA,CAAA,KAAY;EAClC,IAAI;IACF,MAAMC,MAAM,GAAG,MAAMvE,WAAW,CAACwE,cAAc,CAAC,CAAC;IACjD,MAAMC,MAAM,GAAG,MAAMtG,SAAS,CAACuG,cAAc,CAAC9E,cAAc,CAAE4C,EAAE,EAAE+B,MAAM,CAAC;IAEzE/G,KAAK,CAAC0F,IAAI,CAAC;MACTtC,KAAK,EAAE,QAAQ;MACfwC,OAAO,eACLxE,OAAA;QAAAsC,QAAA,gBACEtC,OAAA;UAAAsC,QAAA,gBAAGtC,OAAA;YAAAsC,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAEmD,MAAM,CAASE,SAAS;QAAA;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxD1C,OAAA;UAAAsC,QAAA,gBAAGtC,OAAA;YAAAsC,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAEmD,MAAM,CAASG,QAAQ,EAAC,QAAC;QAAA;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzD1C,OAAA;UAAKiG,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAG,CAAE;UAAA5D,QAAA,gBAC5BtC,OAAA;YAAAsC,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpB1C,OAAA;YAAKiG,KAAK,EAAE;cACVE,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,CAAC;cACVC,YAAY,EAAE,CAAC;cACfC,SAAS,EAAE,GAAG;cACdC,QAAQ,EAAE;YACZ,CAAE;YAAAjE,QAAA,EACEuD,MAAM,CAASW;UAAM;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACJmD,MAAM,CAASY,MAAM,iBACrBzG,OAAA;UAAKiG,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAG,CAAE;UAAA5D,QAAA,gBAC5BtC,OAAA;YAAAsC,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpB1C,OAAA;YAAKiG,KAAK,EAAE;cACVE,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,CAAC;cACVC,YAAY,EAAE,CAAC;cACfC,SAAS,EAAE,GAAG;cACdC,QAAQ,EAAE,MAAM;cAChB5D,KAAK,EAAE;YACT,CAAE;YAAAL,QAAA,EACEuD,MAAM,CAASY;UAAM;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACDyC,KAAK,EAAE;IACT,CAAC,CAAC;IAEFpE,sBAAsB,CAAC,KAAK,CAAC;IAC7BK,WAAW,CAAC6C,WAAW,CAAC,CAAC;EAC3B,CAAC,CAAC,OAAOpC,KAAK,EAAE;IACd5C,OAAO,CAAC4C,KAAK,CAAC,QAAQ,CAAC;EACzB;AACF,CAAC;AAED,eAAezB,gBAAgB;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}