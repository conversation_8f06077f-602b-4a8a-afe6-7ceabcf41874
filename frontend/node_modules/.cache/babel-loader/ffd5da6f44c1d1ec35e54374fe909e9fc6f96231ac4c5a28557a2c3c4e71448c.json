{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useSyncState(defaultState, onChange) {\n  var stateRef = React.useRef(defaultState);\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  function setState(updater) {\n    var newValue = typeof updater === 'function' ? updater(stateRef.current) : updater;\n    if (newValue !== stateRef.current) {\n      onChange(newValue, stateRef.current);\n    }\n    stateRef.current = newValue;\n    forceUpdate({});\n  }\n  return [stateRef.current, setState];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useSyncState", "defaultState", "onChange", "stateRef", "useRef", "_React$useState", "useState", "_React$useState2", "forceUpdate", "setState", "updater", "newValue", "current"], "sources": ["/home/<USER>/itai/node_modules/rc-tabs/es/hooks/useSyncState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nexport default function useSyncState(defaultState, onChange) {\n  var stateRef = React.useRef(defaultState);\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    forceUpdate = _React$useState2[1];\n  function setState(updater) {\n    var newValue = typeof updater === 'function' ? updater(stateRef.current) : updater;\n    if (newValue !== stateRef.current) {\n      onChange(newValue, stateRef.current);\n    }\n    stateRef.current = newValue;\n    forceUpdate({});\n  }\n  return [stateRef.current, setState];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAEC,QAAQ,EAAE;EAC3D,IAAIC,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAACH,YAAY,CAAC;EACzC,IAAII,eAAe,GAAGN,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtCC,gBAAgB,GAAGT,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnC,SAASE,QAAQA,CAACC,OAAO,EAAE;IACzB,IAAIC,QAAQ,GAAG,OAAOD,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACP,QAAQ,CAACS,OAAO,CAAC,GAAGF,OAAO;IAClF,IAAIC,QAAQ,KAAKR,QAAQ,CAACS,OAAO,EAAE;MACjCV,QAAQ,CAACS,QAAQ,EAAER,QAAQ,CAACS,OAAO,CAAC;IACtC;IACAT,QAAQ,CAACS,OAAO,GAAGD,QAAQ;IAC3BH,WAAW,CAAC,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,CAACL,QAAQ,CAACS,OAAO,EAAEH,QAAQ,CAAC;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}