{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef, useState } from 'react';\nfunction getPosition(e) {\n  var obj = 'touches' in e ? e.touches[0] : e;\n  var scrollXOffset = document.documentElement.scrollLeft || document.body.scrollLeft || window.pageXOffset;\n  var scrollYOffset = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset;\n  return {\n    pageX: obj.pageX - scrollXOffset,\n    pageY: obj.pageY - scrollYOffset\n  };\n}\nfunction useColorDrag(props) {\n  var targetRef = props.targetRef,\n    containerRef = props.containerRef,\n    direction = props.direction,\n    onDragChange = props.onDragChange,\n    onDragChangeComplete = props.onDragChangeComplete,\n    calculate = props.calculate,\n    color = props.color,\n    disabledDrag = props.disabledDrag;\n  var _useState = useState({\n      x: 0,\n      y: 0\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    offsetValue = _useState2[0],\n    setOffsetValue = _useState2[1];\n  var mouseMoveRef = useRef(null);\n  var mouseUpRef = useRef(null);\n\n  // Always get position from `color`\n  useEffect(function () {\n    setOffsetValue(calculate());\n  }, [color]);\n  useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveRef.current);\n      document.removeEventListener('mouseup', mouseUpRef.current);\n      document.removeEventListener('touchmove', mouseMoveRef.current);\n      document.removeEventListener('touchend', mouseUpRef.current);\n      mouseMoveRef.current = null;\n      mouseUpRef.current = null;\n    };\n  }, []);\n  var updateOffset = function updateOffset(e) {\n    var _getPosition = getPosition(e),\n      pageX = _getPosition.pageX,\n      pageY = _getPosition.pageY;\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      rectX = _containerRef$current.x,\n      rectY = _containerRef$current.y,\n      width = _containerRef$current.width,\n      height = _containerRef$current.height;\n    var _targetRef$current$ge = targetRef.current.getBoundingClientRect(),\n      targetWidth = _targetRef$current$ge.width,\n      targetHeight = _targetRef$current$ge.height;\n    var centerOffsetX = targetWidth / 2;\n    var centerOffsetY = targetHeight / 2;\n    var offsetX = Math.max(0, Math.min(pageX - rectX, width)) - centerOffsetX;\n    var offsetY = Math.max(0, Math.min(pageY - rectY, height)) - centerOffsetY;\n    var calcOffset = {\n      x: offsetX,\n      y: direction === 'x' ? offsetValue.y : offsetY\n    };\n\n    // Exclusion of boundary cases\n    if (targetWidth === 0 && targetHeight === 0 || targetWidth !== targetHeight) {\n      return false;\n    }\n    onDragChange === null || onDragChange === void 0 || onDragChange(calcOffset);\n  };\n  var onDragMove = function onDragMove(e) {\n    e.preventDefault();\n    updateOffset(e);\n  };\n  var onDragStop = function onDragStop(e) {\n    e.preventDefault();\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    document.removeEventListener('touchmove', mouseMoveRef.current);\n    document.removeEventListener('touchend', mouseUpRef.current);\n    mouseMoveRef.current = null;\n    mouseUpRef.current = null;\n    onDragChangeComplete === null || onDragChangeComplete === void 0 || onDragChangeComplete();\n  };\n  var onDragStart = function onDragStart(e) {\n    // https://github.com/ant-design/ant-design/issues/43529\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    if (disabledDrag) {\n      return;\n    }\n    updateOffset(e);\n    document.addEventListener('mousemove', onDragMove);\n    document.addEventListener('mouseup', onDragStop);\n    document.addEventListener('touchmove', onDragMove);\n    document.addEventListener('touchend', onDragStop);\n    mouseMoveRef.current = onDragMove;\n    mouseUpRef.current = onDragStop;\n  };\n  return [offsetValue, onDragStart];\n}\nexport default useColorDrag;", "map": {"version": 3, "names": ["_slicedToArray", "useEffect", "useRef", "useState", "getPosition", "e", "obj", "touches", "scrollXOffset", "document", "documentElement", "scrollLeft", "body", "window", "pageXOffset", "scrollYOffset", "scrollTop", "pageYOffset", "pageX", "pageY", "useColorDrag", "props", "targetRef", "containerRef", "direction", "onDragChange", "onDragChangeComplete", "calculate", "color", "disabledDrag", "_useState", "x", "y", "_useState2", "offsetValue", "setOffsetValue", "mouseMoveRef", "mouseUpRef", "removeEventListener", "current", "updateOffset", "_getPosition", "_containerRef$current", "getBoundingClientRect", "rectX", "rectY", "width", "height", "_targetRef$current$ge", "targetWidth", "targetHeight", "centerOffsetX", "centerOffsetY", "offsetX", "Math", "max", "min", "offsetY", "calcOffset", "onDragMove", "preventDefault", "onDragStop", "onDragStart", "addEventListener"], "sources": ["/home/<USER>/itai/frontend/node_modules/@rc-component/color-picker/es/hooks/useColorDrag.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useRef, useState } from 'react';\nfunction getPosition(e) {\n  var obj = 'touches' in e ? e.touches[0] : e;\n  var scrollXOffset = document.documentElement.scrollLeft || document.body.scrollLeft || window.pageXOffset;\n  var scrollYOffset = document.documentElement.scrollTop || document.body.scrollTop || window.pageYOffset;\n  return {\n    pageX: obj.pageX - scrollXOffset,\n    pageY: obj.pageY - scrollYOffset\n  };\n}\nfunction useColorDrag(props) {\n  var targetRef = props.targetRef,\n    containerRef = props.containerRef,\n    direction = props.direction,\n    onDragChange = props.onDragChange,\n    onDragChangeComplete = props.onDragChangeComplete,\n    calculate = props.calculate,\n    color = props.color,\n    disabledDrag = props.disabledDrag;\n  var _useState = useState({\n      x: 0,\n      y: 0\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    offsetValue = _useState2[0],\n    setOffsetValue = _useState2[1];\n  var mouseMoveRef = useRef(null);\n  var mouseUpRef = useRef(null);\n\n  // Always get position from `color`\n  useEffect(function () {\n    setOffsetValue(calculate());\n  }, [color]);\n  useEffect(function () {\n    return function () {\n      document.removeEventListener('mousemove', mouseMoveRef.current);\n      document.removeEventListener('mouseup', mouseUpRef.current);\n      document.removeEventListener('touchmove', mouseMoveRef.current);\n      document.removeEventListener('touchend', mouseUpRef.current);\n      mouseMoveRef.current = null;\n      mouseUpRef.current = null;\n    };\n  }, []);\n  var updateOffset = function updateOffset(e) {\n    var _getPosition = getPosition(e),\n      pageX = _getPosition.pageX,\n      pageY = _getPosition.pageY;\n    var _containerRef$current = containerRef.current.getBoundingClientRect(),\n      rectX = _containerRef$current.x,\n      rectY = _containerRef$current.y,\n      width = _containerRef$current.width,\n      height = _containerRef$current.height;\n    var _targetRef$current$ge = targetRef.current.getBoundingClientRect(),\n      targetWidth = _targetRef$current$ge.width,\n      targetHeight = _targetRef$current$ge.height;\n    var centerOffsetX = targetWidth / 2;\n    var centerOffsetY = targetHeight / 2;\n    var offsetX = Math.max(0, Math.min(pageX - rectX, width)) - centerOffsetX;\n    var offsetY = Math.max(0, Math.min(pageY - rectY, height)) - centerOffsetY;\n    var calcOffset = {\n      x: offsetX,\n      y: direction === 'x' ? offsetValue.y : offsetY\n    };\n\n    // Exclusion of boundary cases\n    if (targetWidth === 0 && targetHeight === 0 || targetWidth !== targetHeight) {\n      return false;\n    }\n    onDragChange === null || onDragChange === void 0 || onDragChange(calcOffset);\n  };\n  var onDragMove = function onDragMove(e) {\n    e.preventDefault();\n    updateOffset(e);\n  };\n  var onDragStop = function onDragStop(e) {\n    e.preventDefault();\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    document.removeEventListener('touchmove', mouseMoveRef.current);\n    document.removeEventListener('touchend', mouseUpRef.current);\n    mouseMoveRef.current = null;\n    mouseUpRef.current = null;\n    onDragChangeComplete === null || onDragChangeComplete === void 0 || onDragChangeComplete();\n  };\n  var onDragStart = function onDragStart(e) {\n    // https://github.com/ant-design/ant-design/issues/43529\n    document.removeEventListener('mousemove', mouseMoveRef.current);\n    document.removeEventListener('mouseup', mouseUpRef.current);\n    if (disabledDrag) {\n      return;\n    }\n    updateOffset(e);\n    document.addEventListener('mousemove', onDragMove);\n    document.addEventListener('mouseup', onDragStop);\n    document.addEventListener('touchmove', onDragMove);\n    document.addEventListener('touchend', onDragStop);\n    mouseMoveRef.current = onDragMove;\n    mouseUpRef.current = onDragStop;\n  };\n  return [offsetValue, onDragStart];\n}\nexport default useColorDrag;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,WAAWA,CAACC,CAAC,EAAE;EACtB,IAAIC,GAAG,GAAG,SAAS,IAAID,CAAC,GAAGA,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,GAAGF,CAAC;EAC3C,IAAIG,aAAa,GAAGC,QAAQ,CAACC,eAAe,CAACC,UAAU,IAAIF,QAAQ,CAACG,IAAI,CAACD,UAAU,IAAIE,MAAM,CAACC,WAAW;EACzG,IAAIC,aAAa,GAAGN,QAAQ,CAACC,eAAe,CAACM,SAAS,IAAIP,QAAQ,CAACG,IAAI,CAACI,SAAS,IAAIH,MAAM,CAACI,WAAW;EACvG,OAAO;IACLC,KAAK,EAAEZ,GAAG,CAACY,KAAK,GAAGV,aAAa;IAChCW,KAAK,EAAEb,GAAG,CAACa,KAAK,GAAGJ;EACrB,CAAC;AACH;AACA,SAASK,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,oBAAoB,GAAGL,KAAK,CAACK,oBAAoB;IACjDC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,YAAY,GAAGR,KAAK,CAACQ,YAAY;EACnC,IAAIC,SAAS,GAAG3B,QAAQ,CAAC;MACrB4B,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC,CAAC;IACFC,UAAU,GAAGjC,cAAc,CAAC8B,SAAS,EAAE,CAAC,CAAC;IACzCI,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAChC,IAAIG,YAAY,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAImC,UAAU,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAD,SAAS,CAAC,YAAY;IACpBkC,cAAc,CAACR,SAAS,CAAC,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACC,KAAK,CAAC,CAAC;EACX3B,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBQ,QAAQ,CAAC6B,mBAAmB,CAAC,WAAW,EAAEF,YAAY,CAACG,OAAO,CAAC;MAC/D9B,QAAQ,CAAC6B,mBAAmB,CAAC,SAAS,EAAED,UAAU,CAACE,OAAO,CAAC;MAC3D9B,QAAQ,CAAC6B,mBAAmB,CAAC,WAAW,EAAEF,YAAY,CAACG,OAAO,CAAC;MAC/D9B,QAAQ,CAAC6B,mBAAmB,CAAC,UAAU,EAAED,UAAU,CAACE,OAAO,CAAC;MAC5DH,YAAY,CAACG,OAAO,GAAG,IAAI;MAC3BF,UAAU,CAACE,OAAO,GAAG,IAAI;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACnC,CAAC,EAAE;IAC1C,IAAIoC,YAAY,GAAGrC,WAAW,CAACC,CAAC,CAAC;MAC/Ba,KAAK,GAAGuB,YAAY,CAACvB,KAAK;MAC1BC,KAAK,GAAGsB,YAAY,CAACtB,KAAK;IAC5B,IAAIuB,qBAAqB,GAAGnB,YAAY,CAACgB,OAAO,CAACI,qBAAqB,CAAC,CAAC;MACtEC,KAAK,GAAGF,qBAAqB,CAACX,CAAC;MAC/Bc,KAAK,GAAGH,qBAAqB,CAACV,CAAC;MAC/Bc,KAAK,GAAGJ,qBAAqB,CAACI,KAAK;MACnCC,MAAM,GAAGL,qBAAqB,CAACK,MAAM;IACvC,IAAIC,qBAAqB,GAAG1B,SAAS,CAACiB,OAAO,CAACI,qBAAqB,CAAC,CAAC;MACnEM,WAAW,GAAGD,qBAAqB,CAACF,KAAK;MACzCI,YAAY,GAAGF,qBAAqB,CAACD,MAAM;IAC7C,IAAII,aAAa,GAAGF,WAAW,GAAG,CAAC;IACnC,IAAIG,aAAa,GAAGF,YAAY,GAAG,CAAC;IACpC,IAAIG,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACtC,KAAK,GAAG0B,KAAK,EAAEE,KAAK,CAAC,CAAC,GAAGK,aAAa;IACzE,IAAIM,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACrC,KAAK,GAAG0B,KAAK,EAAEE,MAAM,CAAC,CAAC,GAAGK,aAAa;IAC1E,IAAIM,UAAU,GAAG;MACf3B,CAAC,EAAEsB,OAAO;MACVrB,CAAC,EAAER,SAAS,KAAK,GAAG,GAAGU,WAAW,CAACF,CAAC,GAAGyB;IACzC,CAAC;;IAED;IACA,IAAIR,WAAW,KAAK,CAAC,IAAIC,YAAY,KAAK,CAAC,IAAID,WAAW,KAAKC,YAAY,EAAE;MAC3E,OAAO,KAAK;IACd;IACAzB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACiC,UAAU,CAAC;EAC9E,CAAC;EACD,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACtD,CAAC,EAAE;IACtCA,CAAC,CAACuD,cAAc,CAAC,CAAC;IAClBpB,YAAY,CAACnC,CAAC,CAAC;EACjB,CAAC;EACD,IAAIwD,UAAU,GAAG,SAASA,UAAUA,CAACxD,CAAC,EAAE;IACtCA,CAAC,CAACuD,cAAc,CAAC,CAAC;IAClBnD,QAAQ,CAAC6B,mBAAmB,CAAC,WAAW,EAAEF,YAAY,CAACG,OAAO,CAAC;IAC/D9B,QAAQ,CAAC6B,mBAAmB,CAAC,SAAS,EAAED,UAAU,CAACE,OAAO,CAAC;IAC3D9B,QAAQ,CAAC6B,mBAAmB,CAAC,WAAW,EAAEF,YAAY,CAACG,OAAO,CAAC;IAC/D9B,QAAQ,CAAC6B,mBAAmB,CAAC,UAAU,EAAED,UAAU,CAACE,OAAO,CAAC;IAC5DH,YAAY,CAACG,OAAO,GAAG,IAAI;IAC3BF,UAAU,CAACE,OAAO,GAAG,IAAI;IACzBb,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,IAAIA,oBAAoB,CAAC,CAAC;EAC5F,CAAC;EACD,IAAIoC,WAAW,GAAG,SAASA,WAAWA,CAACzD,CAAC,EAAE;IACxC;IACAI,QAAQ,CAAC6B,mBAAmB,CAAC,WAAW,EAAEF,YAAY,CAACG,OAAO,CAAC;IAC/D9B,QAAQ,CAAC6B,mBAAmB,CAAC,SAAS,EAAED,UAAU,CAACE,OAAO,CAAC;IAC3D,IAAIV,YAAY,EAAE;MAChB;IACF;IACAW,YAAY,CAACnC,CAAC,CAAC;IACfI,QAAQ,CAACsD,gBAAgB,CAAC,WAAW,EAAEJ,UAAU,CAAC;IAClDlD,QAAQ,CAACsD,gBAAgB,CAAC,SAAS,EAAEF,UAAU,CAAC;IAChDpD,QAAQ,CAACsD,gBAAgB,CAAC,WAAW,EAAEJ,UAAU,CAAC;IAClDlD,QAAQ,CAACsD,gBAAgB,CAAC,UAAU,EAAEF,UAAU,CAAC;IACjDzB,YAAY,CAACG,OAAO,GAAGoB,UAAU;IACjCtB,UAAU,CAACE,OAAO,GAAGsB,UAAU;EACjC,CAAC;EACD,OAAO,CAAC3B,WAAW,EAAE4B,WAAW,CAAC;AACnC;AACA,eAAe1C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}