{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TruckFilledSvg from \"@ant-design/icons-svg/es/asn/TruckFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TruckFilled = function TruckFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TruckFilledSvg\n  }));\n};\n\n/**![truck](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNjA4IDE5MmEzMiAzMiAwIDAxMzIgMzJ2MTYwaDE3NC44MWEzMiAzMiAwIDAxMjYuNjggMTQuMzNsMTEzLjE5IDE3MC44NGEzMiAzMiAwIDAxNS4zMiAxNy42OFY2NzJhMzIgMzIgMCAwMS0zMiAzMmgtOTZjMCA3MC43LTU3LjMgMTI4LTEyOCAxMjhzLTEyOC01Ny4zLTEyOC0xMjhIMzg0YzAgNzAuNy01Ny4zIDEyOC0xMjggMTI4cy0xMjgtNTcuMy0xMjgtMTI4SDk2YTMyIDMyIDAgMDEtMzItMzJWMjI0YTMyIDMyIDAgMDEzMi0zMnpNMjU2IDY0MGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwMjU2IDY0MG00NDggMGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwNzA0IDY0MG05My42My0xOTJINjQwdjE0NS4xMkExMjcuNDMgMTI3LjQzIDAgMDE3MDQgNTc2YzQ3LjM4IDAgODguNzUgMjUuNzQgMTEwLjg4IDY0SDg5NnYtNDMuNTJ6TTUwMCA0NDhIMzMyYTEyIDEyIDAgMDAtMTIgMTJ2NDBhMTIgMTIgMCAwMDEyIDEyaDE2OGExMiAxMiAwIDAwMTItMTJ2LTQwYTEyIDEyIDAgMDAtMTItMTJNMzA4IDMyMEgyMDRhMTIgMTIgMCAwMC0xMiAxMnY0MGExMiAxMiAwIDAwMTIgMTJoMTA0YTEyIDEyIDAgMDAxMi0xMnYtNDBhMTIgMTIgMCAwMC0xMi0xMiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TruckFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TruckFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TruckFilledSvg", "AntdIcon", "TruckFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/itai/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/TruckFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TruckFilledSvg from \"@ant-design/icons-svg/es/asn/TruckFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TruckFilled = function TruckFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TruckFilledSvg\n  }));\n};\n\n/**![truck](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNjA4IDE5MmEzMiAzMiAwIDAxMzIgMzJ2MTYwaDE3NC44MWEzMiAzMiAwIDAxMjYuNjggMTQuMzNsMTEzLjE5IDE3MC44NGEzMiAzMiAwIDAxNS4zMiAxNy42OFY2NzJhMzIgMzIgMCAwMS0zMiAzMmgtOTZjMCA3MC43LTU3LjMgMTI4LTEyOCAxMjhzLTEyOC01Ny4zLTEyOC0xMjhIMzg0YzAgNzAuNy01Ny4zIDEyOC0xMjggMTI4cy0xMjgtNTcuMy0xMjgtMTI4SDk2YTMyIDMyIDAgMDEtMzItMzJWMjI0YTMyIDMyIDAgMDEzMi0zMnpNMjU2IDY0MGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwMjU2IDY0MG00NDggMGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwNzA0IDY0MG05My42My0xOTJINjQwdjE0NS4xMkExMjcuNDMgMTI3LjQzIDAgMDE3MDQgNTc2YzQ3LjM4IDAgODguNzUgMjUuNzQgMTEwLjg4IDY0SDg5NnYtNDMuNTJ6TTUwMCA0NDhIMzMyYTEyIDEyIDAgMDAtMTIgMTJ2NDBhMTIgMTIgMCAwMDEyIDEyaDE2OGExMiAxMiAwIDAwMTItMTJ2LTQwYTEyIDEyIDAgMDAtMTItMTJNMzA4IDMyMEgyMDRhMTIgMTIgMCAwMC0xMiAxMnY0MGExMiAxMiAwIDAwMTIgMTJoMTA0YTEyIDEyIDAgMDAxMi0xMnYtNDBhMTIgMTIgMCAwMC0xMi0xMiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TruckFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TruckFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}