{"ast": null, "code": "import { point } from \"./basis.js\";\nfunction BasisOpen(context) {\n  this._context = context;\n}\nBasisOpen.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        var x0 = (this._x0 + 4 * this._x1 + x) / 6,\n          y0 = (this._y0 + 4 * this._y1 + y) / 6;\n        this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0);\n        break;\n      case 3:\n        this._point = 4;\n      // falls through\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\nexport default function (context) {\n  return new BasisOpen(context);\n}", "map": {"version": 3, "names": ["point", "BasisOpen", "context", "_context", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_x0", "_x1", "_y0", "_y1", "_point", "lineEnd", "closePath", "x", "y", "x0", "y0", "lineTo", "moveTo"], "sources": ["/home/<USER>/itai/frontend/node_modules/d3-shape/src/curve/basisOpen.js"], "sourcesContent": ["import {point} from \"./basis.js\";\n\nfunction BasisOpen(context) {\n  this._context = context;\n}\n\nBasisOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; var x0 = (this._x0 + 4 * this._x1 + x) / 6, y0 = (this._y0 + 4 * this._y1 + y) / 6; this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nexport default function(context) {\n  return new BasisOpen(context);\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAEhC,SAASC,SAASA,CAACC,OAAO,EAAE;EAC1B,IAAI,CAACC,QAAQ,GAAGD,OAAO;AACzB;AAEAD,SAAS,CAACG,SAAS,GAAG;EACpBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GACnB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGL,GAAG;IACzB,IAAI,CAACM,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,IAAI,CAACT,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACQ,MAAM,KAAK,CAAE,EAAE,IAAI,CAACX,QAAQ,CAACa,SAAS,CAAC,CAAC;IACpF,IAAI,CAACV,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDN,KAAK,EAAE,SAAAA,CAASiB,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IACd,QAAQ,IAAI,CAACJ,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAIK,EAAE,GAAG,CAAC,IAAI,CAACT,GAAG,GAAG,CAAC,GAAG,IAAI,CAACC,GAAG,GAAGM,CAAC,IAAI,CAAC;UAAEG,EAAE,GAAG,CAAC,IAAI,CAACR,GAAG,GAAG,CAAC,GAAG,IAAI,CAACC,GAAG,GAAGK,CAAC,IAAI,CAAC;QAAE,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACH,QAAQ,CAACkB,MAAM,CAACF,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAI,CAACjB,QAAQ,CAACmB,MAAM,CAACH,EAAE,EAAEC,EAAE,CAAC;QAAE;MACvL,KAAK,CAAC;QAAE,IAAI,CAACN,MAAM,GAAG,CAAC;MAAE;MACzB;QAASd,KAAK,CAAC,IAAI,EAAEiB,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IACA,IAAI,CAACR,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGM,CAAC;IACjC,IAAI,CAACL,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGK,CAAC;EACnC;AACF,CAAC;AAED,eAAe,UAAShB,OAAO,EAAE;EAC/B,OAAO,IAAID,SAAS,CAACC,OAAO,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}