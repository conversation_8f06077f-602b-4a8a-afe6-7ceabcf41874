{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport React from 'react';\nvar BEAT_LIMIT = 1000 * 60 * 10;\n\n/**\n * A helper class to map keys to values.\n * It supports both primitive keys and object keys.\n */\nvar ArrayKeyMap = /*#__PURE__*/function () {\n  function ArrayKeyMap() {\n    _classCallCheck(this, ArrayKeyMap);\n    _defineProperty(this, \"map\", new Map());\n    // Use WeakMap to avoid memory leak\n    _defineProperty(this, \"objectIDMap\", new WeakMap());\n    _defineProperty(this, \"nextID\", 0);\n    _defineProperty(this, \"lastAccessBeat\", new Map());\n    // We will clean up the cache when reach the limit\n    _defineProperty(this, \"accessBeat\", 0);\n  }\n  _createClass(ArrayKeyMap, [{\n    key: \"set\",\n    value: function set(keys, value) {\n      // New set will trigger clear\n      this.clear();\n\n      // Set logic\n      var compositeKey = this.getCompositeKey(keys);\n      this.map.set(compositeKey, value);\n      this.lastAccessBeat.set(compositeKey, Date.now());\n    }\n  }, {\n    key: \"get\",\n    value: function get(keys) {\n      var compositeKey = this.getCompositeKey(keys);\n      var cache = this.map.get(compositeKey);\n      this.lastAccessBeat.set(compositeKey, Date.now());\n      this.accessBeat += 1;\n      return cache;\n    }\n  }, {\n    key: \"getCompositeKey\",\n    value: function getCompositeKey(keys) {\n      var _this = this;\n      var ids = keys.map(function (key) {\n        if (key && _typeof(key) === 'object') {\n          return \"obj_\".concat(_this.getObjectID(key));\n        }\n        return \"\".concat(_typeof(key), \"_\").concat(key);\n      });\n      return ids.join('|');\n    }\n  }, {\n    key: \"getObjectID\",\n    value: function getObjectID(obj) {\n      if (this.objectIDMap.has(obj)) {\n        return this.objectIDMap.get(obj);\n      }\n      var id = this.nextID;\n      this.objectIDMap.set(obj, id);\n      this.nextID += 1;\n      return id;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      var _this2 = this;\n      if (this.accessBeat > 10000) {\n        var now = Date.now();\n        this.lastAccessBeat.forEach(function (beat, key) {\n          if (now - beat > BEAT_LIMIT) {\n            _this2.map.delete(key);\n            _this2.lastAccessBeat.delete(key);\n          }\n        });\n        this.accessBeat = 0;\n      }\n    }\n  }]);\n  return ArrayKeyMap;\n}();\nvar uniqueMap = new ArrayKeyMap();\n\n/**\n * Like `useMemo`, but this hook result will be shared across all instances.\n */\nfunction useUniqueMemo(memoFn, deps) {\n  return React.useMemo(function () {\n    var cachedValue = uniqueMap.get(deps);\n    if (cachedValue) {\n      return cachedValue;\n    }\n    var newValue = memoFn();\n    uniqueMap.set(deps, newValue);\n    return newValue;\n  }, deps);\n}\nexport default useUniqueMemo;", "map": {"version": 3, "names": ["_typeof", "_classCallCheck", "_createClass", "_defineProperty", "React", "BEAT_LIMIT", "ArrayKeyMap", "Map", "WeakMap", "key", "value", "set", "keys", "clear", "compositeKey", "getCompositeKey", "map", "lastAccessBeat", "Date", "now", "get", "cache", "accessBeat", "_this", "ids", "concat", "getObjectID", "join", "obj", "objectIDMap", "has", "id", "nextID", "_this2", "for<PERSON>ach", "beat", "delete", "uniqueMap", "useUniqueMemo", "memoFn", "deps", "useMemo", "cachedValue", "newValue"], "sources": ["/home/<USER>/itai/frontend/node_modules/@ant-design/cssinjs-utils/es/_util/hooks/useUniqueMemo.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport React from 'react';\nvar BEAT_LIMIT = 1000 * 60 * 10;\n\n/**\n * A helper class to map keys to values.\n * It supports both primitive keys and object keys.\n */\nvar ArrayKeyMap = /*#__PURE__*/function () {\n  function ArrayKeyMap() {\n    _classCallCheck(this, ArrayKeyMap);\n    _defineProperty(this, \"map\", new Map());\n    // Use WeakMap to avoid memory leak\n    _defineProperty(this, \"objectIDMap\", new WeakMap());\n    _defineProperty(this, \"nextID\", 0);\n    _defineProperty(this, \"lastAccessBeat\", new Map());\n    // We will clean up the cache when reach the limit\n    _defineProperty(this, \"accessBeat\", 0);\n  }\n  _createClass(ArrayKeyMap, [{\n    key: \"set\",\n    value: function set(keys, value) {\n      // New set will trigger clear\n      this.clear();\n\n      // Set logic\n      var compositeKey = this.getCompositeKey(keys);\n      this.map.set(compositeKey, value);\n      this.lastAccessBeat.set(compositeKey, Date.now());\n    }\n  }, {\n    key: \"get\",\n    value: function get(keys) {\n      var compositeKey = this.getCompositeKey(keys);\n      var cache = this.map.get(compositeKey);\n      this.lastAccessBeat.set(compositeKey, Date.now());\n      this.accessBeat += 1;\n      return cache;\n    }\n  }, {\n    key: \"getCompositeKey\",\n    value: function getCompositeKey(keys) {\n      var _this = this;\n      var ids = keys.map(function (key) {\n        if (key && _typeof(key) === 'object') {\n          return \"obj_\".concat(_this.getObjectID(key));\n        }\n        return \"\".concat(_typeof(key), \"_\").concat(key);\n      });\n      return ids.join('|');\n    }\n  }, {\n    key: \"getObjectID\",\n    value: function getObjectID(obj) {\n      if (this.objectIDMap.has(obj)) {\n        return this.objectIDMap.get(obj);\n      }\n      var id = this.nextID;\n      this.objectIDMap.set(obj, id);\n      this.nextID += 1;\n      return id;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      var _this2 = this;\n      if (this.accessBeat > 10000) {\n        var now = Date.now();\n        this.lastAccessBeat.forEach(function (beat, key) {\n          if (now - beat > BEAT_LIMIT) {\n            _this2.map.delete(key);\n            _this2.lastAccessBeat.delete(key);\n          }\n        });\n        this.accessBeat = 0;\n      }\n    }\n  }]);\n  return ArrayKeyMap;\n}();\nvar uniqueMap = new ArrayKeyMap();\n\n/**\n * Like `useMemo`, but this hook result will be shared across all instances.\n */\nfunction useUniqueMemo(memoFn, deps) {\n  return React.useMemo(function () {\n    var cachedValue = uniqueMap.get(deps);\n    if (cachedValue) {\n      return cachedValue;\n    }\n    var newValue = memoFn();\n    uniqueMap.set(deps, newValue);\n    return newValue;\n  }, deps);\n}\nexport default useUniqueMemo;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,IAAIC,UAAU,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;;AAE/B;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAa,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG;IACrBL,eAAe,CAAC,IAAI,EAAEK,WAAW,CAAC;IAClCH,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IACvC;IACAJ,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,IAAIK,OAAO,CAAC,CAAC,CAAC;IACnDL,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;IAClCA,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IAClD;IACAJ,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;EACxC;EACAD,YAAY,CAACI,WAAW,EAAE,CAAC;IACzBG,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACC,IAAI,EAAEF,KAAK,EAAE;MAC/B;MACA,IAAI,CAACG,KAAK,CAAC,CAAC;;MAEZ;MACA,IAAIC,YAAY,GAAG,IAAI,CAACC,eAAe,CAACH,IAAI,CAAC;MAC7C,IAAI,CAACI,GAAG,CAACL,GAAG,CAACG,YAAY,EAAEJ,KAAK,CAAC;MACjC,IAAI,CAACO,cAAc,CAACN,GAAG,CAACG,YAAY,EAAEI,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IACnD;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASU,GAAGA,CAACR,IAAI,EAAE;MACxB,IAAIE,YAAY,GAAG,IAAI,CAACC,eAAe,CAACH,IAAI,CAAC;MAC7C,IAAIS,KAAK,GAAG,IAAI,CAACL,GAAG,CAACI,GAAG,CAACN,YAAY,CAAC;MACtC,IAAI,CAACG,cAAc,CAACN,GAAG,CAACG,YAAY,EAAEI,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MACjD,IAAI,CAACG,UAAU,IAAI,CAAC;MACpB,OAAOD,KAAK;IACd;EACF,CAAC,EAAE;IACDZ,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,SAASK,eAAeA,CAACH,IAAI,EAAE;MACpC,IAAIW,KAAK,GAAG,IAAI;MAChB,IAAIC,GAAG,GAAGZ,IAAI,CAACI,GAAG,CAAC,UAAUP,GAAG,EAAE;QAChC,IAAIA,GAAG,IAAIT,OAAO,CAACS,GAAG,CAAC,KAAK,QAAQ,EAAE;UACpC,OAAO,MAAM,CAACgB,MAAM,CAACF,KAAK,CAACG,WAAW,CAACjB,GAAG,CAAC,CAAC;QAC9C;QACA,OAAO,EAAE,CAACgB,MAAM,CAACzB,OAAO,CAACS,GAAG,CAAC,EAAE,GAAG,CAAC,CAACgB,MAAM,CAAChB,GAAG,CAAC;MACjD,CAAC,CAAC;MACF,OAAOe,GAAG,CAACG,IAAI,CAAC,GAAG,CAAC;IACtB;EACF,CAAC,EAAE;IACDlB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASgB,WAAWA,CAACE,GAAG,EAAE;MAC/B,IAAI,IAAI,CAACC,WAAW,CAACC,GAAG,CAACF,GAAG,CAAC,EAAE;QAC7B,OAAO,IAAI,CAACC,WAAW,CAACT,GAAG,CAACQ,GAAG,CAAC;MAClC;MACA,IAAIG,EAAE,GAAG,IAAI,CAACC,MAAM;MACpB,IAAI,CAACH,WAAW,CAAClB,GAAG,CAACiB,GAAG,EAAEG,EAAE,CAAC;MAC7B,IAAI,CAACC,MAAM,IAAI,CAAC;MAChB,OAAOD,EAAE;IACX;EACF,CAAC,EAAE;IACDtB,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,SAASG,KAAKA,CAAA,EAAG;MACtB,IAAIoB,MAAM,GAAG,IAAI;MACjB,IAAI,IAAI,CAACX,UAAU,GAAG,KAAK,EAAE;QAC3B,IAAIH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACpB,IAAI,CAACF,cAAc,CAACiB,OAAO,CAAC,UAAUC,IAAI,EAAE1B,GAAG,EAAE;UAC/C,IAAIU,GAAG,GAAGgB,IAAI,GAAG9B,UAAU,EAAE;YAC3B4B,MAAM,CAACjB,GAAG,CAACoB,MAAM,CAAC3B,GAAG,CAAC;YACtBwB,MAAM,CAAChB,cAAc,CAACmB,MAAM,CAAC3B,GAAG,CAAC;UACnC;QACF,CAAC,CAAC;QACF,IAAI,CAACa,UAAU,GAAG,CAAC;MACrB;IACF;EACF,CAAC,CAAC,CAAC;EACH,OAAOhB,WAAW;AACpB,CAAC,CAAC,CAAC;AACH,IAAI+B,SAAS,GAAG,IAAI/B,WAAW,CAAC,CAAC;;AAEjC;AACA;AACA;AACA,SAASgC,aAAaA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACnC,OAAOpC,KAAK,CAACqC,OAAO,CAAC,YAAY;IAC/B,IAAIC,WAAW,GAAGL,SAAS,CAACjB,GAAG,CAACoB,IAAI,CAAC;IACrC,IAAIE,WAAW,EAAE;MACf,OAAOA,WAAW;IACpB;IACA,IAAIC,QAAQ,GAAGJ,MAAM,CAAC,CAAC;IACvBF,SAAS,CAAC1B,GAAG,CAAC6B,IAAI,EAAEG,QAAQ,CAAC;IAC7B,OAAOA,QAAQ;EACjB,CAAC,EAAEH,IAAI,CAAC;AACV;AACA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}