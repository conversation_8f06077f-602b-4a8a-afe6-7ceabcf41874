{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isObject(value) {\n  return value !== null && (typeof value === 'object' || typeof value === 'function');\n}\nexports.isObject = isObject;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isObject"], "sources": ["/home/<USER>/itai/frontend/node_modules/es-toolkit/dist/compat/predicate/isObject.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isObject(value) {\n    return value !== null && (typeof value === 'object' || typeof value === 'function');\n}\n\nexports.isObject = isObject;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,QAAQA,CAACD,KAAK,EAAE;EACrB,OAAOA,KAAK,KAAK,IAAI,KAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAU,CAAC;AACvF;AAEAH,OAAO,CAACI,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}