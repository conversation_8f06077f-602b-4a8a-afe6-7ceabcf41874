{"ast": null, "code": "\"use client\";\n\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport InputNumber from '../../input-number';\nconst ColorSteppers = ({\n  prefixCls,\n  min = 0,\n  max = 100,\n  value,\n  onChange,\n  className,\n  formatter\n}) => {\n  const colorSteppersPrefixCls = `${prefixCls}-steppers`;\n  const [internalValue, setInternalValue] = useState(0);\n  const stepValue = !Number.isNaN(value) ? value : internalValue;\n  return /*#__PURE__*/React.createElement(InputNumber, {\n    className: classNames(colorSteppersPrefixCls, className),\n    min: min,\n    max: max,\n    value: stepValue,\n    formatter: formatter,\n    size: \"small\",\n    onChange: step => {\n      setInternalValue(step || 0);\n      onChange === null || onChange === void 0 ? void 0 : onChange(step);\n    }\n  });\n};\nexport default ColorSteppers;", "map": {"version": 3, "names": ["React", "useState", "classNames", "InputNumber", "ColorSteppers", "prefixCls", "min", "max", "value", "onChange", "className", "formatter", "colorSteppersPrefixCls", "internalValue", "setInternalValue", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "createElement", "size", "step"], "sources": ["/home/<USER>/itai/frontend/node_modules/antd/es/color-picker/components/ColorSteppers.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport InputNumber from '../../input-number';\nconst ColorSteppers = ({\n  prefixCls,\n  min = 0,\n  max = 100,\n  value,\n  onChange,\n  className,\n  formatter\n}) => {\n  const colorSteppersPrefixCls = `${prefixCls}-steppers`;\n  const [internalValue, setInternalValue] = useState(0);\n  const stepValue = !Number.isNaN(value) ? value : internalValue;\n  return /*#__PURE__*/React.createElement(InputNumber, {\n    className: classNames(colorSteppersPrefixCls, className),\n    min: min,\n    max: max,\n    value: stepValue,\n    formatter: formatter,\n    size: \"small\",\n    onChange: step => {\n      setInternalValue(step || 0);\n      onChange === null || onChange === void 0 ? void 0 : onChange(step);\n    }\n  });\n};\nexport default ColorSteppers;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,MAAMC,aAAa,GAAGA,CAAC;EACrBC,SAAS;EACTC,GAAG,GAAG,CAAC;EACPC,GAAG,GAAG,GAAG;EACTC,KAAK;EACLC,QAAQ;EACRC,SAAS;EACTC;AACF,CAAC,KAAK;EACJ,MAAMC,sBAAsB,GAAG,GAAGP,SAAS,WAAW;EACtD,MAAM,CAACQ,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAMc,SAAS,GAAG,CAACC,MAAM,CAACC,KAAK,CAACT,KAAK,CAAC,GAAGA,KAAK,GAAGK,aAAa;EAC9D,OAAO,aAAab,KAAK,CAACkB,aAAa,CAACf,WAAW,EAAE;IACnDO,SAAS,EAAER,UAAU,CAACU,sBAAsB,EAAEF,SAAS,CAAC;IACxDJ,GAAG,EAAEA,GAAG;IACRC,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAEO,SAAS;IAChBJ,SAAS,EAAEA,SAAS;IACpBQ,IAAI,EAAE,OAAO;IACbV,QAAQ,EAAEW,IAAI,IAAI;MAChBN,gBAAgB,CAACM,IAAI,IAAI,CAAC,CAAC;MAC3BX,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACW,IAAI,CAAC;IACpE;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAehB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}