{"ast": null, "code": "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}", "map": {"version": 3, "names": ["flatten", "arrays", "array", "merge", "Array", "from"], "sources": ["/home/<USER>/itai/node_modules/d3-array/src/merge.js"], "sourcesContent": ["function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n"], "mappings": "AAAA,UAAUA,OAAOA,CAACC,MAAM,EAAE;EACxB,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;IAC1B,OAAOC,KAAK;EACd;AACF;AAEA,eAAe,SAASC,KAAKA,CAACF,MAAM,EAAE;EACpC,OAAOG,KAAK,CAACC,IAAI,CAACL,OAAO,CAACC,MAAM,CAAC,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}