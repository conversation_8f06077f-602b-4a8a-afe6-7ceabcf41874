{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { COMMON_PROPS } from \"../common\";\n/**\n * Merge props provided `items` or context collected images\n */\nexport default function usePreviewItems(items) {\n  // Context collection image data\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    images = _React$useState2[0],\n    setImages = _React$useState2[1];\n  var registerImage = React.useCallback(function (id, data) {\n    setImages(function (imgs) {\n      return _objectSpread(_objectSpread({}, imgs), {}, _defineProperty({}, id, data));\n    });\n    return function () {\n      setImages(function (imgs) {\n        var cloneImgs = _objectSpread({}, imgs);\n        delete cloneImgs[id];\n        return cloneImgs;\n      });\n    };\n  }, []);\n\n  // items\n  var mergedItems = React.useMemo(function () {\n    // use `items` first\n    if (items) {\n      return items.map(function (item) {\n        if (typeof item === 'string') {\n          return {\n            data: {\n              src: item\n            }\n          };\n        }\n        var data = {};\n        Object.keys(item).forEach(function (key) {\n          if (['src'].concat(_toConsumableArray(COMMON_PROPS)).includes(key)) {\n            data[key] = item[key];\n          }\n        });\n        return {\n          data: data\n        };\n      });\n    }\n\n    // use registered images secondly\n    return Object.keys(images).reduce(function (total, id) {\n      var _images$id = images[id],\n        canPreview = _images$id.canPreview,\n        data = _images$id.data;\n      if (canPreview) {\n        total.push({\n          data: data,\n          id: id\n        });\n      }\n      return total;\n    }, []);\n  }, [items, images]);\n  return [mergedItems, registerImage, !!items];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_defineProperty", "_objectSpread", "_slicedToArray", "React", "COMMON_PROPS", "usePreviewItems", "items", "_React$useState", "useState", "_React$useState2", "images", "setImages", "registerImage", "useCallback", "id", "data", "imgs", "cloneImgs", "mergedItems", "useMemo", "map", "item", "src", "Object", "keys", "for<PERSON>ach", "key", "concat", "includes", "reduce", "total", "_images$id", "canPreview", "push"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-image/es/hooks/usePreviewItems.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { COMMON_PROPS } from \"../common\";\n/**\n * Merge props provided `items` or context collected images\n */\nexport default function usePreviewItems(items) {\n  // Context collection image data\n  var _React$useState = React.useState({}),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    images = _React$useState2[0],\n    setImages = _React$useState2[1];\n  var registerImage = React.useCallback(function (id, data) {\n    setImages(function (imgs) {\n      return _objectSpread(_objectSpread({}, imgs), {}, _defineProperty({}, id, data));\n    });\n    return function () {\n      setImages(function (imgs) {\n        var cloneImgs = _objectSpread({}, imgs);\n        delete cloneImgs[id];\n        return cloneImgs;\n      });\n    };\n  }, []);\n\n  // items\n  var mergedItems = React.useMemo(function () {\n    // use `items` first\n    if (items) {\n      return items.map(function (item) {\n        if (typeof item === 'string') {\n          return {\n            data: {\n              src: item\n            }\n          };\n        }\n        var data = {};\n        Object.keys(item).forEach(function (key) {\n          if (['src'].concat(_toConsumableArray(COMMON_PROPS)).includes(key)) {\n            data[key] = item[key];\n          }\n        });\n        return {\n          data: data\n        };\n      });\n    }\n\n    // use registered images secondly\n    return Object.keys(images).reduce(function (total, id) {\n      var _images$id = images[id],\n        canPreview = _images$id.canPreview,\n        data = _images$id.data;\n      if (canPreview) {\n        total.push({\n          data: data,\n          id: id\n        });\n      }\n      return total;\n    }, []);\n  }, [items, images]);\n  return [mergedItems, registerImage, !!items];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,WAAW;AACxC;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC7C;EACA,IAAIC,eAAe,GAAGJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtCC,gBAAgB,GAAGP,cAAc,CAACK,eAAe,EAAE,CAAC,CAAC;IACrDG,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIG,aAAa,GAAGT,KAAK,CAACU,WAAW,CAAC,UAAUC,EAAE,EAAEC,IAAI,EAAE;IACxDJ,SAAS,CAAC,UAAUK,IAAI,EAAE;MACxB,OAAOf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,IAAI,CAAC,EAAE,CAAC,CAAC,EAAEhB,eAAe,CAAC,CAAC,CAAC,EAAEc,EAAE,EAAEC,IAAI,CAAC,CAAC;IAClF,CAAC,CAAC;IACF,OAAO,YAAY;MACjBJ,SAAS,CAAC,UAAUK,IAAI,EAAE;QACxB,IAAIC,SAAS,GAAGhB,aAAa,CAAC,CAAC,CAAC,EAAEe,IAAI,CAAC;QACvC,OAAOC,SAAS,CAACH,EAAE,CAAC;QACpB,OAAOG,SAAS;MAClB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIC,WAAW,GAAGf,KAAK,CAACgB,OAAO,CAAC,YAAY;IAC1C;IACA,IAAIb,KAAK,EAAE;MACT,OAAOA,KAAK,CAACc,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC/B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC5B,OAAO;YACLN,IAAI,EAAE;cACJO,GAAG,EAAED;YACP;UACF,CAAC;QACH;QACA,IAAIN,IAAI,GAAG,CAAC,CAAC;QACbQ,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;UACvC,IAAI,CAAC,KAAK,CAAC,CAACC,MAAM,CAAC5B,kBAAkB,CAACK,YAAY,CAAC,CAAC,CAACwB,QAAQ,CAACF,GAAG,CAAC,EAAE;YAClEX,IAAI,CAACW,GAAG,CAAC,GAAGL,IAAI,CAACK,GAAG,CAAC;UACvB;QACF,CAAC,CAAC;QACF,OAAO;UACLX,IAAI,EAAEA;QACR,CAAC;MACH,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOQ,MAAM,CAACC,IAAI,CAACd,MAAM,CAAC,CAACmB,MAAM,CAAC,UAAUC,KAAK,EAAEhB,EAAE,EAAE;MACrD,IAAIiB,UAAU,GAAGrB,MAAM,CAACI,EAAE,CAAC;QACzBkB,UAAU,GAAGD,UAAU,CAACC,UAAU;QAClCjB,IAAI,GAAGgB,UAAU,CAAChB,IAAI;MACxB,IAAIiB,UAAU,EAAE;QACdF,KAAK,CAACG,IAAI,CAAC;UACTlB,IAAI,EAAEA,IAAI;UACVD,EAAE,EAAEA;QACN,CAAC,CAAC;MACJ;MACA,OAAOgB,KAAK;IACd,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,EAAE,CAACxB,KAAK,EAAEI,MAAM,CAAC,CAAC;EACnB,OAAO,CAACQ,WAAW,EAAEN,aAAa,EAAE,CAAC,CAACN,KAAK,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}