{"ast": null, "code": "import ReactDOM from 'react-dom';\nexport default function addEventListenerWrap(target, eventType, cb, option) {\n  /* eslint camelcase: 2 */\n  var callback = ReactDOM.unstable_batchedUpdates ? function run(e) {\n    ReactDOM.unstable_batchedUpdates(cb, e);\n  } : cb;\n  if (target !== null && target !== void 0 && target.addEventListener) {\n    target.addEventListener(eventType, callback, option);\n  }\n  return {\n    remove: function remove() {\n      if (target !== null && target !== void 0 && target.removeEventListener) {\n        target.removeEventListener(eventType, callback, option);\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["ReactDOM", "addEventListenerWrap", "target", "eventType", "cb", "option", "callback", "unstable_batchedUpdates", "run", "e", "addEventListener", "remove", "removeEventListener"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-util/es/Dom/addEventListener.js"], "sourcesContent": ["import ReactDOM from 'react-dom';\nexport default function addEventListenerWrap(target, eventType, cb, option) {\n  /* eslint camelcase: 2 */\n  var callback = ReactDOM.unstable_batchedUpdates ? function run(e) {\n    ReactDOM.unstable_batchedUpdates(cb, e);\n  } : cb;\n  if (target !== null && target !== void 0 && target.addEventListener) {\n    target.addEventListener(eventType, callback, option);\n  }\n  return {\n    remove: function remove() {\n      if (target !== null && target !== void 0 && target.removeEventListener) {\n        target.removeEventListener(eventType, callback, option);\n      }\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,WAAW;AAChC,eAAe,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,EAAE,EAAEC,MAAM,EAAE;EAC1E;EACA,IAAIC,QAAQ,GAAGN,QAAQ,CAACO,uBAAuB,GAAG,SAASC,GAAGA,CAACC,CAAC,EAAE;IAChET,QAAQ,CAACO,uBAAuB,CAACH,EAAE,EAAEK,CAAC,CAAC;EACzC,CAAC,GAAGL,EAAE;EACN,IAAIF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACQ,gBAAgB,EAAE;IACnER,MAAM,CAACQ,gBAAgB,CAACP,SAAS,EAAEG,QAAQ,EAAED,MAAM,CAAC;EACtD;EACA,OAAO;IACLM,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;MACxB,IAAIT,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,IAAIA,MAAM,CAACU,mBAAmB,EAAE;QACtEV,MAAM,CAACU,mBAAmB,CAACT,SAAS,EAAEG,QAAQ,EAAED,MAAM,CAAC;MACzD;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}