{"ast": null, "code": "export default function permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}", "map": {"version": 3, "names": ["permute", "source", "keys", "Array", "from", "key"], "sources": ["/home/<USER>/itai/frontend/node_modules/d3-array/src/permute.js"], "sourcesContent": ["export default function permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n"], "mappings": "AAAA,eAAe,SAASA,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC5C,OAAOC,KAAK,CAACC,IAAI,CAACF,IAAI,EAAEG,GAAG,IAAIJ,MAAM,CAACI,GAAG,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}