{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, Progress, Table, Tag, Select, DatePicker, Space, Alert } from 'antd';\nimport { monitoringApi } from '../../services/api';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';\nimport { WarningOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst MonitoringDashboard = () => {\n  _s();\n  const [selectedServer, setSelectedServer] = useState(1);\n  const [metricsData, setMetricsData] = useState([]);\n  const [serverStatuses, setServerStatuses] = useState([]);\n  const [alerts, setAlerts] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // 加载监控数据\n  const loadMonitoringData = async () => {\n    try {\n      setLoading(true);\n\n      // 并行加载服务器状态和告警\n      const [serverData, alertData] = await Promise.all([monitoringApi.getServerMonitoring(), monitoringApi.getAlerts()]);\n      setServerStatuses(serverData);\n      setAlerts(alertData);\n\n      // 如果有选中的服务器，加载详细指标\n      if (selectedServer) {\n        const metricsData = await monitoringApi.getServerStats(selectedServer);\n        setMetricsData(metricsData);\n      }\n    } catch (error) {\n      console.error('加载监控数据失败:', error);\n      // 使用模拟数据作为后备\n      loadMockData();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 模拟数据作为后备\n  const loadMockData = () => {\n    // 生成模拟的时间序列数据\n    const generateMetrics = () => {\n      const data = [];\n      const now = new Date();\n      for (let i = 23; i >= 0; i--) {\n        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);\n        data.push({\n          timestamp: timestamp.toISOString(),\n          cpu_usage: Math.random() * 100,\n          memory_usage: 60 + Math.random() * 30,\n          disk_usage: 45 + Math.random() * 20,\n          network_in: Math.random() * 1000,\n          network_out: Math.random() * 800\n        });\n      }\n      return data;\n    };\n    setMetricsData(generateMetrics());\n    setServerStatuses([{\n      id: 1,\n      name: '测试服务器1',\n      status: 'online',\n      cpu_usage: 25.5,\n      memory_usage: 68.2,\n      disk_usage: 45.8,\n      last_update: new Date().toISOString()\n    }, {\n      id: 2,\n      name: '生产服务器1',\n      status: 'warning',\n      cpu_usage: 85.2,\n      memory_usage: 92.1,\n      disk_usage: 78.5,\n      last_update: new Date().toISOString()\n    }, {\n      id: 3,\n      name: '备份服务器1',\n      status: 'offline',\n      cpu_usage: 0,\n      memory_usage: 0,\n      disk_usage: 0,\n      last_update: new Date(Date.now() - 10 * 60 * 1000).toISOString()\n    }]);\n    setAlerts([{\n      id: 1,\n      server_name: '生产服务器1',\n      alert_type: 'cpu_high',\n      severity: 'high',\n      message: 'CPU使用率过高: 85.2%',\n      created_at: new Date().toISOString(),\n      status: 'active'\n    }, {\n      id: 2,\n      server_name: '生产服务器1',\n      alert_type: 'memory_high',\n      severity: 'critical',\n      message: '内存使用率过高: 92.1%',\n      created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      status: 'active'\n    }, {\n      id: 3,\n      server_name: '备份服务器1',\n      alert_type: 'server_offline',\n      severity: 'critical',\n      message: '服务器离线',\n      created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n      status: 'active'\n    }]);\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    loadMonitoringData();\n  }, [selectedServer]);\n\n  // 模拟数据 - 保留作为后备\n  useEffect(() => {\n    // 生成模拟的时间序列数据\n    const generateMetrics = () => {\n      const data = [];\n      const now = new Date();\n      for (let i = 23; i >= 0; i--) {\n        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);\n        data.push({\n          timestamp: timestamp.toISOString(),\n          cpu_usage: Math.random() * 100,\n          memory_usage: 60 + Math.random() * 30,\n          disk_usage: 45 + Math.random() * 20,\n          network_in: Math.random() * 1000,\n          network_out: Math.random() * 800\n        });\n      }\n      return data;\n    };\n    setMetricsData(generateMetrics());\n    setServerStatuses([{\n      id: 1,\n      name: '测试服务器1',\n      status: 'online',\n      cpu_usage: 25.5,\n      memory_usage: 68.2,\n      disk_usage: 45.8,\n      last_update: new Date().toISOString()\n    }, {\n      id: 2,\n      name: '生产服务器1',\n      status: 'warning',\n      cpu_usage: 85.2,\n      memory_usage: 92.1,\n      disk_usage: 78.5,\n      last_update: new Date().toISOString()\n    }, {\n      id: 3,\n      name: '备份服务器1',\n      status: 'offline',\n      cpu_usage: 0,\n      memory_usage: 0,\n      disk_usage: 0,\n      last_update: new Date(Date.now() - 10 * 60 * 1000).toISOString()\n    }]);\n    setAlerts([{\n      id: 1,\n      server_name: '生产服务器1',\n      alert_type: 'cpu_high',\n      severity: 'high',\n      message: 'CPU使用率过高: 85.2%',\n      created_at: new Date().toISOString(),\n      status: 'active'\n    }, {\n      id: 2,\n      server_name: '生产服务器1',\n      alert_type: 'memory_high',\n      severity: 'critical',\n      message: '内存使用率过高: 92.1%',\n      created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n      status: 'active'\n    }, {\n      id: 3,\n      server_name: '备份服务器1',\n      alert_type: 'server_offline',\n      severity: 'critical',\n      message: '服务器离线',\n      created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n      status: 'active'\n    }]);\n  }, []);\n\n  // 告警操作函数\n  const handleAcknowledgeAlert = async alertId => {\n    try {\n      await monitoringApi.acknowledgeAlert(alertId);\n      loadMonitoringData(); // 重新加载数据\n    } catch (error) {\n      console.error('确认告警失败:', error);\n    }\n  };\n  const handleResolveAlert = async alertId => {\n    try {\n      await monitoringApi.resolveAlert(alertId);\n      loadMonitoringData(); // 重新加载数据\n    } catch (error) {\n      console.error('解决告警失败:', error);\n    }\n  };\n  const serverColumns = [{\n    title: '服务器名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this), record.status === 'online' && /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n        style: {\n          color: '#52c41a'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 42\n      }, this), record.status === 'warning' && /*#__PURE__*/_jsxDEV(WarningOutlined, {\n        style: {\n          color: '#faad14'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 43\n      }, this), record.status === 'offline' && /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n        style: {\n          color: '#ff4d4f'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 43\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => {\n      const colorMap = {\n        online: 'green',\n        warning: 'orange',\n        offline: 'red'\n      };\n      const textMap = {\n        online: '在线',\n        warning: '警告',\n        offline: '离线'\n      };\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: colorMap[status],\n        children: textMap[status]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: 'CPU使用率',\n    dataIndex: 'cpu_usage',\n    key: 'cpu_usage',\n    render: value => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: Math.round(value),\n      size: \"small\",\n      status: value > 80 ? 'exception' : 'normal'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '内存使用率',\n    dataIndex: 'memory_usage',\n    key: 'memory_usage',\n    render: value => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: Math.round(value),\n      size: \"small\",\n      status: value > 85 ? 'exception' : 'normal'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '磁盘使用率',\n    dataIndex: 'disk_usage',\n    key: 'disk_usage',\n    render: value => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: Math.round(value),\n      size: \"small\",\n      status: value > 90 ? 'exception' : 'normal'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '最后更新',\n    dataIndex: 'last_update',\n    key: 'last_update',\n    render: text => new Date(text).toLocaleString()\n  }];\n  const alertColumns = [{\n    title: '服务器',\n    dataIndex: 'server_name',\n    key: 'server_name'\n  }, {\n    title: '告警类型',\n    dataIndex: 'alert_type',\n    key: 'alert_type',\n    render: text => {\n      const typeMap = {\n        cpu_high: 'CPU过高',\n        memory_high: '内存过高',\n        disk_high: '磁盘过高',\n        server_offline: '服务器离线'\n      };\n      return typeMap[text] || text;\n    }\n  }, {\n    title: '严重程度',\n    dataIndex: 'severity',\n    key: 'severity',\n    render: severity => {\n      const colorMap = {\n        low: 'blue',\n        medium: 'orange',\n        high: 'red',\n        critical: 'purple'\n      };\n      const textMap = {\n        low: '低',\n        medium: '中',\n        high: '高',\n        critical: '严重'\n      };\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: colorMap[severity],\n        children: textMap[severity]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: '告警消息',\n    dataIndex: 'message',\n    key: 'message'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 'active' ? 'red' : 'green',\n      children: status === 'active' ? '活跃' : '已解决'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: text => new Date(text).toLocaleString()\n  }];\n  const formatChartData = data => {\n    return data.map(item => ({\n      ...item,\n      time: new Date(item.timestamp).toLocaleTimeString()\n    }));\n  };\n  const onlineServers = serverStatuses.filter(s => s.status === 'online').length;\n  const warningServers = serverStatuses.filter(s => s.status === 'warning').length;\n  const offlineServers = serverStatuses.filter(s => s.status === 'offline').length;\n  const activeAlerts = alerts.filter(a => a.status === 'active').length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [activeAlerts > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      message: `当前有 ${activeAlerts} 个活跃告警需要处理`,\n      type: \"warning\",\n      showIcon: true,\n      closable: true,\n      style: {\n        marginBottom: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u670D\\u52A1\\u5668\",\n            value: onlineServers,\n            valueStyle: {\n              color: '#52c41a'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8B66\\u544A\\u670D\\u52A1\\u5668\",\n            value: warningServers,\n            valueStyle: {\n              color: '#faad14'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(WarningOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u79BB\\u7EBF\\u670D\\u52A1\\u5668\",\n            value: offlineServers,\n            valueStyle: {\n              color: '#ff4d4f'\n            },\n            prefix: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D3B\\u8DC3\\u544A\\u8B66\",\n            value: activeAlerts,\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7CFB\\u7EDF\\u8D44\\u6E90\\u76D1\\u63A7\",\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Select, {\n              value: selectedServer,\n              onChange: setSelectedServer,\n              style: {\n                width: 200\n              },\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: 1,\n                children: \"\\u6D4B\\u8BD5\\u670D\\u52A1\\u56681\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: 2,\n                children: \"\\u751F\\u4EA7\\u670D\\u52A1\\u56681\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              showTime: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: formatChartData(metricsData),\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"cpu_usage\",\n                stroke: \"#8884d8\",\n                name: \"CPU\\u4F7F\\u7528\\u7387(%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"memory_usage\",\n                stroke: \"#82ca9d\",\n                name: \"\\u5185\\u5B58\\u4F7F\\u7528\\u7387(%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"disk_usage\",\n                stroke: \"#ffc658\",\n                name: \"\\u78C1\\u76D8\\u4F7F\\u7528\\u7387(%)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7F51\\u7EDC\\u6D41\\u91CF\\u76D1\\u63A7\",\n          children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 250,\n            children: /*#__PURE__*/_jsxDEV(AreaChart, {\n              data: formatChartData(metricsData),\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"network_in\",\n                stackId: \"1\",\n                stroke: \"#8884d8\",\n                fill: \"#8884d8\",\n                name: \"\\u5165\\u7AD9\\u6D41\\u91CF(KB/s)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"network_out\",\n                stackId: \"1\",\n                stroke: \"#82ca9d\",\n                fill: \"#82ca9d\",\n                name: \"\\u51FA\\u7AD9\\u6D41\\u91CF(KB/s)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u670D\\u52A1\\u5668\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: serverColumns,\n            dataSource: serverStatuses,\n            rowKey: \"id\",\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        span: 24,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u544A\\u8B66\\u5217\\u8868\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: alertColumns,\n            dataSource: alerts,\n            rowKey: \"id\",\n            pagination: {\n              pageSize: 10\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 447,\n    columnNumber: 5\n  }, this);\n};\n_s(MonitoringDashboard, \"1NumdgdvkTqjvFzGUmZDG1JTfIk=\");\n_c = MonitoringDashboard;\nexport default MonitoringDashboard;\nvar _c;\n$RefreshReg$(_c, \"MonitoringDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "Progress", "Table", "Tag", "Select", "DatePicker", "Space", "<PERSON><PERSON>", "monitoringApi", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "AreaChart", "Area", "WarningOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "jsxDEV", "_jsxDEV", "Option", "RangePicker", "MonitoringDashboard", "_s", "selectedServer", "setSelectedServer", "metricsData", "setMetricsData", "serverStatuses", "setServerStatuses", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "loadMonitoringData", "serverData", "alertData", "Promise", "all", "getServerMonitoring", "get<PERSON><PERSON><PERSON>", "getServerStats", "error", "console", "loadMockData", "generateMetrics", "data", "now", "Date", "i", "timestamp", "getTime", "push", "toISOString", "cpu_usage", "Math", "random", "memory_usage", "disk_usage", "network_in", "network_out", "id", "name", "status", "last_update", "server_name", "alert_type", "severity", "message", "created_at", "handleAcknowledgeAlert", "alertId", "<PERSON><PERSON><PERSON><PERSON>", "handleResolveAlert", "<PERSON><PERSON><PERSON><PERSON>", "serverColumns", "title", "dataIndex", "key", "render", "text", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "color", "colorMap", "online", "warning", "offline", "textMap", "value", "percent", "round", "size", "toLocaleString", "alertColumns", "typeMap", "cpu_high", "memory_high", "disk_high", "server_offline", "low", "medium", "high", "critical", "formatChartData", "map", "item", "time", "toLocaleTimeString", "onlineServers", "filter", "s", "length", "warningServers", "offlineServers", "active<PERSON>lerts", "a", "type", "showIcon", "closable", "marginBottom", "gutter", "span", "valueStyle", "prefix", "extra", "onChange", "width", "showTime", "height", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stroke", "stackId", "fill", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/itai/frontend/src/components/Monitoring/MonitoringDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Table,\n  Tag,\n  Select,\n  DatePicker,\n  Space,\n  Alert,\n  Button,\n} from 'antd';\nimport { monitoringApi } from '../../services/api';\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  AreaChart,\n  Area,\n} from 'recharts';\nimport {\n  WarningOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\ninterface SystemMetrics {\n  timestamp: string;\n  cpu_usage: number;\n  memory_usage: number;\n  disk_usage: number;\n  network_in: number;\n  network_out: number;\n}\n\ninterface ServerStatus {\n  id: number;\n  name: string;\n  status: 'online' | 'offline' | 'warning';\n  cpu_usage: number;\n  memory_usage: number;\n  disk_usage: number;\n  last_update: string;\n}\n\ninterface AlertRecord {\n  id: number;\n  server_name: string;\n  alert_type: string;\n  severity: 'low' | 'medium' | 'high' | 'critical';\n  message: string;\n  created_at: string;\n  status: 'active' | 'resolved';\n}\n\nconst MonitoringDashboard: React.FC = () => {\n  const [selectedServer, setSelectedServer] = useState<number>(1);\n  const [metricsData, setMetricsData] = useState<SystemMetrics[]>([]);\n  const [serverStatuses, setServerStatuses] = useState<ServerStatus[]>([]);\n  const [alerts, setAlerts] = useState<AlertRecord[]>([]);\n  const [loading, setLoading] = useState(false);\n\n  // 加载监控数据\n  const loadMonitoringData = async () => {\n    try {\n      setLoading(true);\n\n      // 并行加载服务器状态和告警\n      const [serverData, alertData] = await Promise.all([\n        monitoringApi.getServerMonitoring(),\n        monitoringApi.getAlerts()\n      ]);\n\n      setServerStatuses(serverData);\n      setAlerts(alertData);\n\n      // 如果有选中的服务器，加载详细指标\n      if (selectedServer) {\n        const metricsData = await monitoringApi.getServerStats(selectedServer);\n        setMetricsData(metricsData);\n      }\n    } catch (error) {\n      console.error('加载监控数据失败:', error);\n      // 使用模拟数据作为后备\n      loadMockData();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 模拟数据作为后备\n  const loadMockData = () => {\n    // 生成模拟的时间序列数据\n    const generateMetrics = () => {\n      const data: SystemMetrics[] = [];\n      const now = new Date();\n\n      for (let i = 23; i >= 0; i--) {\n        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);\n        data.push({\n          timestamp: timestamp.toISOString(),\n          cpu_usage: Math.random() * 100,\n          memory_usage: 60 + Math.random() * 30,\n          disk_usage: 45 + Math.random() * 20,\n          network_in: Math.random() * 1000,\n          network_out: Math.random() * 800,\n        });\n      }\n      return data;\n    };\n\n    setMetricsData(generateMetrics());\n\n    setServerStatuses([\n      {\n        id: 1,\n        name: '测试服务器1',\n        status: 'online',\n        cpu_usage: 25.5,\n        memory_usage: 68.2,\n        disk_usage: 45.8,\n        last_update: new Date().toISOString(),\n      },\n      {\n        id: 2,\n        name: '生产服务器1',\n        status: 'warning',\n        cpu_usage: 85.2,\n        memory_usage: 92.1,\n        disk_usage: 78.5,\n        last_update: new Date().toISOString(),\n      },\n      {\n        id: 3,\n        name: '备份服务器1',\n        status: 'offline',\n        cpu_usage: 0,\n        memory_usage: 0,\n        disk_usage: 0,\n        last_update: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n      },\n    ]);\n\n    setAlerts([\n      {\n        id: 1,\n        server_name: '生产服务器1',\n        alert_type: 'cpu_high',\n        severity: 'high',\n        message: 'CPU使用率过高: 85.2%',\n        created_at: new Date().toISOString(),\n        status: 'active',\n      },\n      {\n        id: 2,\n        server_name: '生产服务器1',\n        alert_type: 'memory_high',\n        severity: 'critical',\n        message: '内存使用率过高: 92.1%',\n        created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n        status: 'active',\n      },\n      {\n        id: 3,\n        server_name: '备份服务器1',\n        alert_type: 'server_offline',\n        severity: 'critical',\n        message: '服务器离线',\n        created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n        status: 'active',\n      },\n    ]);\n  };\n\n  // 初始化数据\n  useEffect(() => {\n    loadMonitoringData();\n  }, [selectedServer]);\n\n  // 模拟数据 - 保留作为后备\n  useEffect(() => {\n    // 生成模拟的时间序列数据\n    const generateMetrics = () => {\n      const data: SystemMetrics[] = [];\n      const now = new Date();\n      \n      for (let i = 23; i >= 0; i--) {\n        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);\n        data.push({\n          timestamp: timestamp.toISOString(),\n          cpu_usage: Math.random() * 100,\n          memory_usage: 60 + Math.random() * 30,\n          disk_usage: 45 + Math.random() * 20,\n          network_in: Math.random() * 1000,\n          network_out: Math.random() * 800,\n        });\n      }\n      return data;\n    };\n\n    setMetricsData(generateMetrics());\n\n    setServerStatuses([\n      {\n        id: 1,\n        name: '测试服务器1',\n        status: 'online',\n        cpu_usage: 25.5,\n        memory_usage: 68.2,\n        disk_usage: 45.8,\n        last_update: new Date().toISOString(),\n      },\n      {\n        id: 2,\n        name: '生产服务器1',\n        status: 'warning',\n        cpu_usage: 85.2,\n        memory_usage: 92.1,\n        disk_usage: 78.5,\n        last_update: new Date().toISOString(),\n      },\n      {\n        id: 3,\n        name: '备份服务器1',\n        status: 'offline',\n        cpu_usage: 0,\n        memory_usage: 0,\n        disk_usage: 0,\n        last_update: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n      },\n    ]);\n\n    setAlerts([\n      {\n        id: 1,\n        server_name: '生产服务器1',\n        alert_type: 'cpu_high',\n        severity: 'high',\n        message: 'CPU使用率过高: 85.2%',\n        created_at: new Date().toISOString(),\n        status: 'active',\n      },\n      {\n        id: 2,\n        server_name: '生产服务器1',\n        alert_type: 'memory_high',\n        severity: 'critical',\n        message: '内存使用率过高: 92.1%',\n        created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n        status: 'active',\n      },\n      {\n        id: 3,\n        server_name: '备份服务器1',\n        alert_type: 'server_offline',\n        severity: 'critical',\n        message: '服务器离线',\n        created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),\n        status: 'active',\n      },\n    ]);\n  }, []);\n\n  // 告警操作函数\n  const handleAcknowledgeAlert = async (alertId: number) => {\n    try {\n      await monitoringApi.acknowledgeAlert(alertId);\n      loadMonitoringData(); // 重新加载数据\n    } catch (error) {\n      console.error('确认告警失败:', error);\n    }\n  };\n\n  const handleResolveAlert = async (alertId: number) => {\n    try {\n      await monitoringApi.resolveAlert(alertId);\n      loadMonitoringData(); // 重新加载数据\n    } catch (error) {\n      console.error('解决告警失败:', error);\n    }\n  };\n\n  const serverColumns: ColumnsType<ServerStatus> = [\n    {\n      title: '服务器名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <span>{text}</span>\n          {record.status === 'online' && <CheckCircleOutlined style={{ color: '#52c41a' }} />}\n          {record.status === 'warning' && <WarningOutlined style={{ color: '#faad14' }} />}\n          {record.status === 'offline' && <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => {\n        const colorMap: { [key: string]: string } = {\n          online: 'green',\n          warning: 'orange',\n          offline: 'red',\n        };\n        const textMap: { [key: string]: string } = {\n          online: '在线',\n          warning: '警告',\n          offline: '离线',\n        };\n        return <Tag color={colorMap[status]}>{textMap[status]}</Tag>;\n      },\n    },\n    {\n      title: 'CPU使用率',\n      dataIndex: 'cpu_usage',\n      key: 'cpu_usage',\n      render: (value) => (\n        <Progress \n          percent={Math.round(value)} \n          size=\"small\" \n          status={value > 80 ? 'exception' : 'normal'}\n        />\n      ),\n    },\n    {\n      title: '内存使用率',\n      dataIndex: 'memory_usage',\n      key: 'memory_usage',\n      render: (value) => (\n        <Progress \n          percent={Math.round(value)} \n          size=\"small\" \n          status={value > 85 ? 'exception' : 'normal'}\n        />\n      ),\n    },\n    {\n      title: '磁盘使用率',\n      dataIndex: 'disk_usage',\n      key: 'disk_usage',\n      render: (value) => (\n        <Progress \n          percent={Math.round(value)} \n          size=\"small\" \n          status={value > 90 ? 'exception' : 'normal'}\n        />\n      ),\n    },\n    {\n      title: '最后更新',\n      dataIndex: 'last_update',\n      key: 'last_update',\n      render: (text) => new Date(text).toLocaleString(),\n    },\n  ];\n\n  const alertColumns: ColumnsType<AlertRecord> = [\n    {\n      title: '服务器',\n      dataIndex: 'server_name',\n      key: 'server_name',\n    },\n    {\n      title: '告警类型',\n      dataIndex: 'alert_type',\n      key: 'alert_type',\n      render: (text) => {\n        const typeMap: { [key: string]: string } = {\n          cpu_high: 'CPU过高',\n          memory_high: '内存过高',\n          disk_high: '磁盘过高',\n          server_offline: '服务器离线',\n        };\n        return typeMap[text] || text;\n      },\n    },\n    {\n      title: '严重程度',\n      dataIndex: 'severity',\n      key: 'severity',\n      render: (severity: string) => {\n        const colorMap: { [key: string]: string } = {\n          low: 'blue',\n          medium: 'orange',\n          high: 'red',\n          critical: 'purple',\n        };\n        const textMap: { [key: string]: string } = {\n          low: '低',\n          medium: '中',\n          high: '高',\n          critical: '严重',\n        };\n        return <Tag color={colorMap[severity]}>{textMap[severity]}</Tag>;\n      },\n    },\n    {\n      title: '告警消息',\n      dataIndex: 'message',\n      key: 'message',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={status === 'active' ? 'red' : 'green'}>\n          {status === 'active' ? '活跃' : '已解决'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (text) => new Date(text).toLocaleString(),\n    },\n  ];\n\n  const formatChartData = (data: SystemMetrics[]) => {\n    return data.map(item => ({\n      ...item,\n      time: new Date(item.timestamp).toLocaleTimeString(),\n    }));\n  };\n\n  const onlineServers = serverStatuses.filter(s => s.status === 'online').length;\n  const warningServers = serverStatuses.filter(s => s.status === 'warning').length;\n  const offlineServers = serverStatuses.filter(s => s.status === 'offline').length;\n  const activeAlerts = alerts.filter(a => a.status === 'active').length;\n\n  return (\n    <div>\n      {/* 告警横幅 */}\n      {activeAlerts > 0 && (\n        <Alert\n          message={`当前有 ${activeAlerts} 个活跃告警需要处理`}\n          type=\"warning\"\n          showIcon\n          closable\n          style={{ marginBottom: 16 }}\n        />\n      )}\n\n      {/* 概览统计 */}\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"在线服务器\"\n              value={onlineServers}\n              valueStyle={{ color: '#52c41a' }}\n              prefix={<CheckCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"警告服务器\"\n              value={warningServers}\n              valueStyle={{ color: '#faad14' }}\n              prefix={<WarningOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"离线服务器\"\n              value={offlineServers}\n              valueStyle={{ color: '#ff4d4f' }}\n              prefix={<ExclamationCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃告警\"\n              value={activeAlerts}\n              valueStyle={{ color: '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 图表区域 */}\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={24}>\n          <Card\n            title=\"系统资源监控\"\n            extra={\n              <Space>\n                <Select\n                  value={selectedServer}\n                  onChange={setSelectedServer}\n                  style={{ width: 200 }}\n                >\n                  <Option value={1}>测试服务器1</Option>\n                  <Option value={2}>生产服务器1</Option>\n                </Select>\n                <RangePicker showTime />\n              </Space>\n            }\n          >\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={formatChartData(metricsData)}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"cpu_usage\" \n                  stroke=\"#8884d8\" \n                  name=\"CPU使用率(%)\"\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"memory_usage\" \n                  stroke=\"#82ca9d\" \n                  name=\"内存使用率(%)\"\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"disk_usage\" \n                  stroke=\"#ffc658\" \n                  name=\"磁盘使用率(%)\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 网络流量图表 */}\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={24}>\n          <Card title=\"网络流量监控\">\n            <ResponsiveContainer width=\"100%\" height={250}>\n              <AreaChart data={formatChartData(metricsData)}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"network_in\" \n                  stackId=\"1\"\n                  stroke=\"#8884d8\" \n                  fill=\"#8884d8\"\n                  name=\"入站流量(KB/s)\"\n                />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"network_out\" \n                  stackId=\"1\"\n                  stroke=\"#82ca9d\" \n                  fill=\"#82ca9d\"\n                  name=\"出站流量(KB/s)\"\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 服务器状态表格 */}\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={24}>\n          <Card title=\"服务器状态\">\n            <Table\n              columns={serverColumns}\n              dataSource={serverStatuses}\n              rowKey=\"id\"\n              pagination={false}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 告警列表 */}\n      <Row gutter={16}>\n        <Col span={24}>\n          <Card title=\"告警列表\">\n            <Table\n              columns={alertColumns}\n              dataSource={alerts}\n              rowKey=\"id\"\n              pagination={{ pageSize: 10 }}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default MonitoringDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,KAAK,QAEA,MAAM;AACb,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,SAAS,EACTC,IAAI,QACC,UAAU;AACjB,SACEC,eAAe,EACfC,mBAAmB,EACnBC,yBAAyB,QACpB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC;AAAO,CAAC,GAAGpB,MAAM;AACzB,MAAM;EAAEqB;AAAY,CAAC,GAAGpB,UAAU;AA+BlC,MAAMqB,mBAA6B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1C,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAS,CAAC,CAAC;EAC/D,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAkB,EAAE,CAAC;EACnE,MAAM,CAACqC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAiB,EAAE,CAAC;EACxE,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAgB,EAAE,CAAC;EACvD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM2C,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CAACE,UAAU,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAChDlC,aAAa,CAACmC,mBAAmB,CAAC,CAAC,EACnCnC,aAAa,CAACoC,SAAS,CAAC,CAAC,CAC1B,CAAC;MAEFX,iBAAiB,CAACM,UAAU,CAAC;MAC7BJ,SAAS,CAACK,SAAS,CAAC;;MAEpB;MACA,IAAIZ,cAAc,EAAE;QAClB,MAAME,WAAW,GAAG,MAAMtB,aAAa,CAACqC,cAAc,CAACjB,cAAc,CAAC;QACtEG,cAAc,CAACD,WAAW,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACAE,YAAY,CAAC,CAAC;IAChB,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMW,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,IAAqB,GAAG,EAAE;MAChC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MAEtB,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAACD,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGF,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC9DH,IAAI,CAACM,IAAI,CAAC;UACRF,SAAS,EAAEA,SAAS,CAACG,WAAW,CAAC,CAAC;UAClCC,SAAS,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC9BC,YAAY,EAAE,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UACrCE,UAAU,EAAE,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UACnCG,UAAU,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;UAChCI,WAAW,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC/B,CAAC,CAAC;MACJ;MACA,OAAOV,IAAI;IACb,CAAC;IAEDnB,cAAc,CAACkB,eAAe,CAAC,CAAC,CAAC;IAEjChB,iBAAiB,CAAC,CAChB;MACEgC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,QAAQ;MAChBT,SAAS,EAAE,IAAI;MACfG,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBM,WAAW,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACtC,CAAC,EACD;MACEQ,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,SAAS;MACjBT,SAAS,EAAE,IAAI;MACfG,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBM,WAAW,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACtC,CAAC,EACD;MACEQ,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,SAAS;MACjBT,SAAS,EAAE,CAAC;MACZG,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,CAAC;MACbM,WAAW,EAAE,IAAIhB,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACM,WAAW,CAAC;IACjE,CAAC,CACF,CAAC;IAEFtB,SAAS,CAAC,CACR;MACE8B,EAAE,EAAE,CAAC;MACLI,WAAW,EAAE,QAAQ;MACrBC,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,iBAAiB;MAC1BC,UAAU,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACpCU,MAAM,EAAE;IACV,CAAC,EACD;MACEF,EAAE,EAAE,CAAC;MACLI,WAAW,EAAE,QAAQ;MACrBC,UAAU,EAAE,aAAa;MACzBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,gBAAgB;MACzBC,UAAU,EAAE,IAAIrB,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACM,WAAW,CAAC,CAAC;MAC9DU,MAAM,EAAE;IACV,CAAC,EACD;MACEF,EAAE,EAAE,CAAC;MACLI,WAAW,EAAE,QAAQ;MACrBC,UAAU,EAAE,gBAAgB;MAC5BC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,OAAO;MAChBC,UAAU,EAAE,IAAIrB,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACM,WAAW,CAAC,CAAC;MAC/DU,MAAM,EAAE;IACV,CAAC,CACF,CAAC;EACJ,CAAC;;EAED;EACAvE,SAAS,CAAC,MAAM;IACd0C,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACV,cAAc,CAAC,CAAC;;EAEpB;EACAhC,SAAS,CAAC,MAAM;IACd;IACA,MAAMqD,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,IAAqB,GAAG,EAAE;MAChC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;MAEtB,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAACD,GAAG,CAACI,OAAO,CAAC,CAAC,GAAGF,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC9DH,IAAI,CAACM,IAAI,CAAC;UACRF,SAAS,EAAEA,SAAS,CAACG,WAAW,CAAC,CAAC;UAClCC,SAAS,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC9BC,YAAY,EAAE,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UACrCE,UAAU,EAAE,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UACnCG,UAAU,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI;UAChCI,WAAW,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC/B,CAAC,CAAC;MACJ;MACA,OAAOV,IAAI;IACb,CAAC;IAEDnB,cAAc,CAACkB,eAAe,CAAC,CAAC,CAAC;IAEjChB,iBAAiB,CAAC,CAChB;MACEgC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,QAAQ;MAChBT,SAAS,EAAE,IAAI;MACfG,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBM,WAAW,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACtC,CAAC,EACD;MACEQ,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,SAAS;MACjBT,SAAS,EAAE,IAAI;MACfG,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBM,WAAW,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACtC,CAAC,EACD;MACEQ,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,SAAS;MACjBT,SAAS,EAAE,CAAC;MACZG,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,CAAC;MACbM,WAAW,EAAE,IAAIhB,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACM,WAAW,CAAC;IACjE,CAAC,CACF,CAAC;IAEFtB,SAAS,CAAC,CACR;MACE8B,EAAE,EAAE,CAAC;MACLI,WAAW,EAAE,QAAQ;MACrBC,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,iBAAiB;MAC1BC,UAAU,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACpCU,MAAM,EAAE;IACV,CAAC,EACD;MACEF,EAAE,EAAE,CAAC;MACLI,WAAW,EAAE,QAAQ;MACrBC,UAAU,EAAE,aAAa;MACzBC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,gBAAgB;MACzBC,UAAU,EAAE,IAAIrB,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAACM,WAAW,CAAC,CAAC;MAC9DU,MAAM,EAAE;IACV,CAAC,EACD;MACEF,EAAE,EAAE,CAAC;MACLI,WAAW,EAAE,QAAQ;MACrBC,UAAU,EAAE,gBAAgB;MAC5BC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,OAAO;MAChBC,UAAU,EAAE,IAAIrB,IAAI,CAACA,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACM,WAAW,CAAC,CAAC;MAC/DU,MAAM,EAAE;IACV,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,sBAAsB,GAAG,MAAOC,OAAe,IAAK;IACxD,IAAI;MACF,MAAMnE,aAAa,CAACoE,gBAAgB,CAACD,OAAO,CAAC;MAC7CrC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAOF,OAAe,IAAK;IACpD,IAAI;MACF,MAAMnE,aAAa,CAACsE,YAAY,CAACH,OAAO,CAAC;MACzCrC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;EAED,MAAMiC,aAAwC,GAAG,CAC/C;IACEC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB9D,OAAA,CAACjB,KAAK;MAAAgF,QAAA,gBACJ/D,OAAA;QAAA+D,QAAA,EAAOF;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAClBL,MAAM,CAAClB,MAAM,KAAK,QAAQ,iBAAI5C,OAAA,CAACH,mBAAmB;QAACuE,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAClFL,MAAM,CAAClB,MAAM,KAAK,SAAS,iBAAI5C,OAAA,CAACJ,eAAe;QAACwE,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC/EL,MAAM,CAAClB,MAAM,KAAK,SAAS,iBAAI5C,OAAA,CAACF,yBAAyB;QAACsE,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGhB,MAAc,IAAK;MAC1B,MAAM0B,QAAmC,GAAG;QAC1CC,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE,QAAQ;QACjBC,OAAO,EAAE;MACX,CAAC;MACD,MAAMC,OAAkC,GAAG;QACzCH,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;MACX,CAAC;MACD,oBAAOzE,OAAA,CAACpB,GAAG;QAACyF,KAAK,EAAEC,QAAQ,CAAC1B,MAAM,CAAE;QAAAmB,QAAA,EAAEW,OAAO,CAAC9B,MAAM;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC9D;EACF,CAAC,EACD;IACEV,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGe,KAAK,iBACZ3E,OAAA,CAACtB,QAAQ;MACPkG,OAAO,EAAExC,IAAI,CAACyC,KAAK,CAACF,KAAK,CAAE;MAC3BG,IAAI,EAAC,OAAO;MACZlC,MAAM,EAAE+B,KAAK,GAAG,EAAE,GAAG,WAAW,GAAG;IAAS;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C;EAEL,CAAC,EACD;IACEV,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAGe,KAAK,iBACZ3E,OAAA,CAACtB,QAAQ;MACPkG,OAAO,EAAExC,IAAI,CAACyC,KAAK,CAACF,KAAK,CAAE;MAC3BG,IAAI,EAAC,OAAO;MACZlC,MAAM,EAAE+B,KAAK,GAAG,EAAE,GAAG,WAAW,GAAG;IAAS;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C;EAEL,CAAC,EACD;IACEV,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGe,KAAK,iBACZ3E,OAAA,CAACtB,QAAQ;MACPkG,OAAO,EAAExC,IAAI,CAACyC,KAAK,CAACF,KAAK,CAAE;MAC3BG,IAAI,EAAC,OAAO;MACZlC,MAAM,EAAE+B,KAAK,GAAG,EAAE,GAAG,WAAW,GAAG;IAAS;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C;EAEL,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGC,IAAI,IAAK,IAAIhC,IAAI,CAACgC,IAAI,CAAC,CAACkB,cAAc,CAAC;EAClD,CAAC,CACF;EAED,MAAMC,YAAsC,GAAG,CAC7C;IACEvB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGC,IAAI,IAAK;MAChB,MAAMoB,OAAkC,GAAG;QACzCC,QAAQ,EAAE,OAAO;QACjBC,WAAW,EAAE,MAAM;QACnBC,SAAS,EAAE,MAAM;QACjBC,cAAc,EAAE;MAClB,CAAC;MACD,OAAOJ,OAAO,CAACpB,IAAI,CAAC,IAAIA,IAAI;IAC9B;EACF,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGZ,QAAgB,IAAK;MAC5B,MAAMsB,QAAmC,GAAG;QAC1CgB,GAAG,EAAE,MAAM;QACXC,MAAM,EAAE,QAAQ;QAChBC,IAAI,EAAE,KAAK;QACXC,QAAQ,EAAE;MACZ,CAAC;MACD,MAAMf,OAAkC,GAAG;QACzCY,GAAG,EAAE,GAAG;QACRC,MAAM,EAAE,GAAG;QACXC,IAAI,EAAE,GAAG;QACTC,QAAQ,EAAE;MACZ,CAAC;MACD,oBAAOzF,OAAA,CAACpB,GAAG;QAACyF,KAAK,EAAEC,QAAQ,CAACtB,QAAQ,CAAE;QAAAe,QAAA,EAAEW,OAAO,CAAC1B,QAAQ;MAAC;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAClE;EACF,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGhB,MAAM,iBACb5C,OAAA,CAACpB,GAAG;MAACyF,KAAK,EAAEzB,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,OAAQ;MAAAmB,QAAA,EAC/CnB,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC;EAET,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGC,IAAI,IAAK,IAAIhC,IAAI,CAACgC,IAAI,CAAC,CAACkB,cAAc,CAAC;EAClD,CAAC,CACF;EAED,MAAMW,eAAe,GAAI/D,IAAqB,IAAK;IACjD,OAAOA,IAAI,CAACgE,GAAG,CAACC,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPC,IAAI,EAAE,IAAIhE,IAAI,CAAC+D,IAAI,CAAC7D,SAAS,CAAC,CAAC+D,kBAAkB,CAAC;IACpD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,aAAa,GAAGtF,cAAc,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrD,MAAM,KAAK,QAAQ,CAAC,CAACsD,MAAM;EAC9E,MAAMC,cAAc,GAAG1F,cAAc,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrD,MAAM,KAAK,SAAS,CAAC,CAACsD,MAAM;EAChF,MAAME,cAAc,GAAG3F,cAAc,CAACuF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrD,MAAM,KAAK,SAAS,CAAC,CAACsD,MAAM;EAChF,MAAMG,YAAY,GAAG1F,MAAM,CAACqF,MAAM,CAACM,CAAC,IAAIA,CAAC,CAAC1D,MAAM,KAAK,QAAQ,CAAC,CAACsD,MAAM;EAErE,oBACElG,OAAA;IAAA+D,QAAA,GAEGsC,YAAY,GAAG,CAAC,iBACfrG,OAAA,CAAChB,KAAK;MACJiE,OAAO,EAAE,OAAOoD,YAAY,YAAa;MACzCE,IAAI,EAAC,SAAS;MACdC,QAAQ;MACRC,QAAQ;MACRrC,KAAK,EAAE;QAAEsC,YAAY,EAAE;MAAG;IAAE;MAAA1C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACF,eAGDnE,OAAA,CAACzB,GAAG;MAACoI,MAAM,EAAE,EAAG;MAACvC,KAAK,EAAE;QAAEsC,YAAY,EAAE;MAAG,CAAE;MAAA3C,QAAA,gBAC3C/D,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAA7C,QAAA,eACX/D,OAAA,CAAC1B,IAAI;UAAAyF,QAAA,eACH/D,OAAA,CAACvB,SAAS;YACRgF,KAAK,EAAC,gCAAO;YACbkB,KAAK,EAAEoB,aAAc;YACrBc,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU,CAAE;YACjCyC,MAAM,eAAE9G,OAAA,CAACH,mBAAmB;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAA7C,QAAA,eACX/D,OAAA,CAAC1B,IAAI;UAAAyF,QAAA,eACH/D,OAAA,CAACvB,SAAS;YACRgF,KAAK,EAAC,gCAAO;YACbkB,KAAK,EAAEwB,cAAe;YACtBU,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU,CAAE;YACjCyC,MAAM,eAAE9G,OAAA,CAACJ,eAAe;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAA7C,QAAA,eACX/D,OAAA,CAAC1B,IAAI;UAAAyF,QAAA,eACH/D,OAAA,CAACvB,SAAS;YACRgF,KAAK,EAAC,gCAAO;YACbkB,KAAK,EAAEyB,cAAe;YACtBS,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU,CAAE;YACjCyC,MAAM,eAAE9G,OAAA,CAACF,yBAAyB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnE,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAA7C,QAAA,eACX/D,OAAA,CAAC1B,IAAI;UAAAyF,QAAA,eACH/D,OAAA,CAACvB,SAAS;YACRgF,KAAK,EAAC,0BAAM;YACZkB,KAAK,EAAE0B,YAAa;YACpBQ,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA,CAACzB,GAAG;MAACoI,MAAM,EAAE,EAAG;MAACvC,KAAK,EAAE;QAAEsC,YAAY,EAAE;MAAG,CAAE;MAAA3C,QAAA,eAC3C/D,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,EAAG;QAAA7C,QAAA,eACZ/D,OAAA,CAAC1B,IAAI;UACHmF,KAAK,EAAC,sCAAQ;UACdsD,KAAK,eACH/G,OAAA,CAACjB,KAAK;YAAAgF,QAAA,gBACJ/D,OAAA,CAACnB,MAAM;cACL8F,KAAK,EAAEtE,cAAe;cACtB2G,QAAQ,EAAE1G,iBAAkB;cAC5B8D,KAAK,EAAE;gBAAE6C,KAAK,EAAE;cAAI,CAAE;cAAAlD,QAAA,gBAEtB/D,OAAA,CAACC,MAAM;gBAAC0E,KAAK,EAAE,CAAE;gBAAAZ,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCnE,OAAA,CAACC,MAAM;gBAAC0E,KAAK,EAAE,CAAE;gBAAAZ,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACTnE,OAAA,CAACE,WAAW;cAACgH,QAAQ;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CACR;UAAAJ,QAAA,eAED/D,OAAA,CAACP,mBAAmB;YAACwH,KAAK,EAAC,MAAM;YAACE,MAAM,EAAE,GAAI;YAAApD,QAAA,eAC5C/D,OAAA,CAACd,SAAS;cAACyC,IAAI,EAAE+D,eAAe,CAACnF,WAAW,CAAE;cAAAwD,QAAA,gBAC5C/D,OAAA,CAACV,aAAa;gBAAC8H,eAAe,EAAC;cAAK;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnE,OAAA,CAACZ,KAAK;gBAACiI,OAAO,EAAC;cAAM;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxBnE,OAAA,CAACX,KAAK;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnE,OAAA,CAACT,OAAO;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXnE,OAAA,CAACR,MAAM;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnE,OAAA,CAACb,IAAI;gBACHoH,IAAI,EAAC,UAAU;gBACfc,OAAO,EAAC,WAAW;gBACnBC,MAAM,EAAC,SAAS;gBAChB3E,IAAI,EAAC;cAAW;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACFnE,OAAA,CAACb,IAAI;gBACHoH,IAAI,EAAC,UAAU;gBACfc,OAAO,EAAC,cAAc;gBACtBC,MAAM,EAAC,SAAS;gBAChB3E,IAAI,EAAC;cAAU;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACFnE,OAAA,CAACb,IAAI;gBACHoH,IAAI,EAAC,UAAU;gBACfc,OAAO,EAAC,YAAY;gBACpBC,MAAM,EAAC,SAAS;gBAChB3E,IAAI,EAAC;cAAU;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA,CAACzB,GAAG;MAACoI,MAAM,EAAE,EAAG;MAACvC,KAAK,EAAE;QAAEsC,YAAY,EAAE;MAAG,CAAE;MAAA3C,QAAA,eAC3C/D,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,EAAG;QAAA7C,QAAA,eACZ/D,OAAA,CAAC1B,IAAI;UAACmF,KAAK,EAAC,sCAAQ;UAAAM,QAAA,eAClB/D,OAAA,CAACP,mBAAmB;YAACwH,KAAK,EAAC,MAAM;YAACE,MAAM,EAAE,GAAI;YAAApD,QAAA,eAC5C/D,OAAA,CAACN,SAAS;cAACiC,IAAI,EAAE+D,eAAe,CAACnF,WAAW,CAAE;cAAAwD,QAAA,gBAC5C/D,OAAA,CAACV,aAAa;gBAAC8H,eAAe,EAAC;cAAK;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnE,OAAA,CAACZ,KAAK;gBAACiI,OAAO,EAAC;cAAM;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxBnE,OAAA,CAACX,KAAK;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnE,OAAA,CAACT,OAAO;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXnE,OAAA,CAACR,MAAM;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnE,OAAA,CAACL,IAAI;gBACH4G,IAAI,EAAC,UAAU;gBACfc,OAAO,EAAC,YAAY;gBACpBE,OAAO,EAAC,GAAG;gBACXD,MAAM,EAAC,SAAS;gBAChBE,IAAI,EAAC,SAAS;gBACd7E,IAAI,EAAC;cAAY;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACFnE,OAAA,CAACL,IAAI;gBACH4G,IAAI,EAAC,UAAU;gBACfc,OAAO,EAAC,aAAa;gBACrBE,OAAO,EAAC,GAAG;gBACXD,MAAM,EAAC,SAAS;gBAChBE,IAAI,EAAC,SAAS;gBACd7E,IAAI,EAAC;cAAY;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA,CAACzB,GAAG;MAACoI,MAAM,EAAE,EAAG;MAACvC,KAAK,EAAE;QAAEsC,YAAY,EAAE;MAAG,CAAE;MAAA3C,QAAA,eAC3C/D,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,EAAG;QAAA7C,QAAA,eACZ/D,OAAA,CAAC1B,IAAI;UAACmF,KAAK,EAAC,gCAAO;UAAAM,QAAA,eACjB/D,OAAA,CAACrB,KAAK;YACJ8I,OAAO,EAAEjE,aAAc;YACvBkE,UAAU,EAAEjH,cAAe;YAC3BkH,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE;UAAM;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnE,OAAA,CAACzB,GAAG;MAACoI,MAAM,EAAE,EAAG;MAAA5C,QAAA,eACd/D,OAAA,CAACxB,GAAG;QAACoI,IAAI,EAAE,EAAG;QAAA7C,QAAA,eACZ/D,OAAA,CAAC1B,IAAI;UAACmF,KAAK,EAAC,0BAAM;UAAAM,QAAA,eAChB/D,OAAA,CAACrB,KAAK;YACJ8I,OAAO,EAAEzC,YAAa;YACtB0C,UAAU,EAAE/G,MAAO;YACnBgH,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAG;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/D,EAAA,CAliBID,mBAA6B;AAAA2H,EAAA,GAA7B3H,mBAA6B;AAoiBnC,eAAeA,mBAAmB;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}