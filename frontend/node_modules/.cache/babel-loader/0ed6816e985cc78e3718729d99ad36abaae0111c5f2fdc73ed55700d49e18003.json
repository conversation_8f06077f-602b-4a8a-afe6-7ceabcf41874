{"ast": null, "code": "var INTERNAL_KEY_PREFIX = 'RC_TABLE_KEY';\nfunction toArray(arr) {\n  if (arr === undefined || arr === null) {\n    return [];\n  }\n  return Array.isArray(arr) ? arr : [arr];\n}\nexport function getColumnsKey(columns) {\n  var columnKeys = [];\n  var keys = {};\n  columns.forEach(function (column) {\n    var _ref = column || {},\n      key = _ref.key,\n      dataIndex = _ref.dataIndex;\n    var mergedKey = key || toArray(dataIndex).join('-') || INTERNAL_KEY_PREFIX;\n    while (keys[mergedKey]) {\n      mergedKey = \"\".concat(mergedKey, \"_next\");\n    }\n    keys[mergedKey] = true;\n    columnKeys.push(mergedKey);\n  });\n  return columnKeys;\n}\nexport function validateValue(val) {\n  return val !== null && val !== undefined;\n}\nexport function validNumberValue(value) {\n  return typeof value === 'number' && !Number.isNaN(value);\n}", "map": {"version": 3, "names": ["INTERNAL_KEY_PREFIX", "toArray", "arr", "undefined", "Array", "isArray", "getColumnsKey", "columns", "columnKeys", "keys", "for<PERSON>ach", "column", "_ref", "key", "dataIndex", "mergedKey", "join", "concat", "push", "validate<PERSON><PERSON>ue", "val", "validNumberValue", "value", "Number", "isNaN"], "sources": ["/home/<USER>/itai/frontend/node_modules/rc-table/es/utils/valueUtil.js"], "sourcesContent": ["var INTERNAL_KEY_PREFIX = 'RC_TABLE_KEY';\nfunction toArray(arr) {\n  if (arr === undefined || arr === null) {\n    return [];\n  }\n  return Array.isArray(arr) ? arr : [arr];\n}\nexport function getColumnsKey(columns) {\n  var columnKeys = [];\n  var keys = {};\n  columns.forEach(function (column) {\n    var _ref = column || {},\n      key = _ref.key,\n      dataIndex = _ref.dataIndex;\n    var mergedKey = key || toArray(dataIndex).join('-') || INTERNAL_KEY_PREFIX;\n    while (keys[mergedKey]) {\n      mergedKey = \"\".concat(mergedKey, \"_next\");\n    }\n    keys[mergedKey] = true;\n    columnKeys.push(mergedKey);\n  });\n  return columnKeys;\n}\nexport function validateValue(val) {\n  return val !== null && val !== undefined;\n}\nexport function validNumberValue(value) {\n  return typeof value === 'number' && !Number.isNaN(value);\n}"], "mappings": "AAAA,IAAIA,mBAAmB,GAAG,cAAc;AACxC,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,IAAI,EAAE;IACrC,OAAO,EAAE;EACX;EACA,OAAOE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;AACzC;AACA,OAAO,SAASI,aAAaA,CAACC,OAAO,EAAE;EACrC,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,IAAI,GAAG,CAAC,CAAC;EACbF,OAAO,CAACG,OAAO,CAAC,UAAUC,MAAM,EAAE;IAChC,IAAIC,IAAI,GAAGD,MAAM,IAAI,CAAC,CAAC;MACrBE,GAAG,GAAGD,IAAI,CAACC,GAAG;MACdC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC5B,IAAIC,SAAS,GAAGF,GAAG,IAAIZ,OAAO,CAACa,SAAS,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,IAAIhB,mBAAmB;IAC1E,OAAOS,IAAI,CAACM,SAAS,CAAC,EAAE;MACtBA,SAAS,GAAG,EAAE,CAACE,MAAM,CAACF,SAAS,EAAE,OAAO,CAAC;IAC3C;IACAN,IAAI,CAACM,SAAS,CAAC,GAAG,IAAI;IACtBP,UAAU,CAACU,IAAI,CAACH,SAAS,CAAC;EAC5B,CAAC,CAAC;EACF,OAAOP,UAAU;AACnB;AACA,OAAO,SAASW,aAAaA,CAACC,GAAG,EAAE;EACjC,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKjB,SAAS;AAC1C;AACA,OAAO,SAASkB,gBAAgBA,CAACC,KAAK,EAAE;EACtC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}