{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Tabs, Table, Button, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Card, Row, Col, Statistic, Progress } from 'antd';\nimport { PlusOutlined, PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined, DeleteOutlined, EyeOutlined, DownloadOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Option\n} = Select;\nconst DockerManagement = () => {\n  _s();\n  const [images, setImages] = useState([]);\n  const [containers, setContainers] = useState([]);\n  const [gpuStats, setGpuStats] = useState([]);\n  const [servers, setServers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedServer, setSelectedServer] = useState(1);\n\n  // 模拟数据\n  useEffect(() => {\n    setImages([{\n      repository: 'nginx',\n      tag: 'latest',\n      image_id: 'sha256:abcd1234',\n      created: '2024-01-01 00:00:00',\n      size: '133MB'\n    }, {\n      repository: 'mysql',\n      tag: '8.0',\n      image_id: 'sha256:efgh5678',\n      created: '2024-01-01 00:00:00',\n      size: '521MB'\n    }]);\n    setContainers([{\n      container_id: 'abcd1234efgh',\n      name: 'web-server',\n      image: 'nginx:latest',\n      status: 'Up 2 hours',\n      ports: '0.0.0.0:80->80/tcp',\n      created: '2024-01-01 00:00:00'\n    }, {\n      container_id: 'ijkl5678mnop',\n      name: 'database',\n      image: 'mysql:8.0',\n      status: 'Up 1 day',\n      ports: '0.0.0.0:3306->3306/tcp',\n      created: '2024-01-01 00:00:00'\n    }]);\n    setGpuStats([{\n      gpu_id: 0,\n      name: 'NVIDIA GeForce RTX 4090',\n      utilization: 75,\n      memory_used: 12000,\n      memory_total: 24000\n    }, {\n      gpu_id: 1,\n      name: 'NVIDIA GeForce RTX 4090',\n      utilization: 45,\n      memory_used: 8000,\n      memory_total: 24000\n    }]);\n  }, []);\n  const imageColumns = [{\n    title: '镜像仓库',\n    dataIndex: 'repository',\n    key: 'repository'\n  }, {\n    title: '标签',\n    dataIndex: 'tag',\n    key: 'tag',\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 25\n    }, this)\n  }, {\n    title: '镜像ID',\n    dataIndex: 'image_id',\n    key: 'image_id',\n    render: text => /*#__PURE__*/_jsxDEV(\"code\", {\n      children: text.substring(0, 12)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 25\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'created',\n    key: 'created'\n  }, {\n    title: '大小',\n    dataIndex: 'size',\n    key: 'size'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u955C\\u50CF\\u5417\\uFF1F\",\n        onConfirm: () => handleDeleteImage(record.image_id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 9\n    }, this)\n  }];\n  const containerColumns = [{\n    title: '容器名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '镜像',\n    dataIndex: 'image',\n    key: 'image'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: text => {\n      const isRunning = text.includes('Up');\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: isRunning ? 'green' : 'red',\n        children: isRunning ? '运行中' : '已停止'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '端口映射',\n    dataIndex: 'ports',\n    key: 'ports'\n  }, {\n    title: '创建时间',\n    dataIndex: 'created',\n    key: 'created'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => {\n      const isRunning = record.status.includes('Up');\n      return /*#__PURE__*/_jsxDEV(Space, {\n        size: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewLogs(record.container_id),\n          children: \"\\u65E5\\u5FD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: isRunning ? /*#__PURE__*/_jsxDEV(PauseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 33\n          }, this) : /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 59\n          }, this),\n          onClick: () => isRunning ? handleStopContainer(record.container_id) : handleStartContainer(record.container_id),\n          children: isRunning ? '停止' : '启动'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleRestartContainer(record.container_id),\n          children: \"\\u91CD\\u542F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n          title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u5BB9\\u5668\\u5417\\uFF1F\",\n          onConfirm: () => handleDeleteContainer(record.container_id),\n          okText: \"\\u786E\\u5B9A\",\n          cancelText: \"\\u53D6\\u6D88\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 48\n            }, this),\n            children: \"\\u5220\\u9664\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n  const handlePullImage = () => {\n    Modal.confirm({\n      title: '拉取镜像',\n      content: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"vertical\",\n        children: /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u955C\\u50CF\\u540D\\u79F0\",\n          name: \"imageName\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4F8B\\u5982: nginx:latest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this),\n      onOk: () => {\n        message.success('镜像拉取成功');\n      }\n    });\n  };\n  const handleDeleteImage = imageId => {\n    setImages(images.filter(img => img.image_id !== imageId));\n    message.success('镜像删除成功');\n  };\n  const handleStartContainer = containerId => {\n    const updatedContainers = containers.map(c => c.container_id === containerId ? {\n      ...c,\n      status: 'Up 1 second'\n    } : c);\n    setContainers(updatedContainers);\n    message.success('容器启动成功');\n  };\n  const handleStopContainer = containerId => {\n    const updatedContainers = containers.map(c => c.container_id === containerId ? {\n      ...c,\n      status: 'Exited (0) 1 second ago'\n    } : c);\n    setContainers(updatedContainers);\n    message.success('容器停止成功');\n  };\n  const handleRestartContainer = containerId => {\n    message.success('容器重启成功');\n  };\n  const handleDeleteContainer = containerId => {\n    setContainers(containers.filter(c => c.container_id !== containerId));\n    message.success('容器删除成功');\n  };\n  const handleViewLogs = containerId => {\n    Modal.info({\n      title: `容器日志 - ${containerId.substring(0, 12)}`,\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxHeight: 400,\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            fontSize: 12\n          },\n          children: [\"2024-01-01 00:00:00 [INFO] Container started\", '\\n', \"2024-01-01 00:00:01 [INFO] Application ready\", '\\n', \"2024-01-01 00:00:02 [INFO] Listening on port 80\", '\\n']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this),\n      width: 800\n    });\n  };\n  const runningContainers = containers.filter(c => c.status.includes('Up')).length;\n  const totalContainers = containers.length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u955C\\u50CF\\u6570\",\n            value: images.length,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5BB9\\u5668\\u6570\",\n            value: totalContainers,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FD0\\u884C\\u4E2D\\u5BB9\\u5668\",\n            value: runningContainers,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5BB9\\u5668\\u8FD0\\u884C\\u7387\",\n            value: totalContainers > 0 ? Math.round(runningContainers / totalContainers * 100) : 0,\n            suffix: \"%\",\n            valueStyle: {\n              color: runningContainers / totalContainers > 0.8 ? '#52c41a' : '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n      defaultActiveKey: \"containers\",\n      children: [/*#__PURE__*/_jsxDEV(TabPane, {\n        tab: \"\\u5BB9\\u5668\\u7BA1\\u7406\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5BB9\\u5668\\u5217\\u8868\",\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 31\n              }, this),\n              onClick: () => setLoading(true),\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 46\n              }, this),\n              children: \"\\u521B\\u5EFA\\u5BB9\\u5668\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: containerColumns,\n            dataSource: containers,\n            rowKey: \"container_id\",\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)\n      }, \"containers\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: \"\\u955C\\u50CF\\u7BA1\\u7406\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u955C\\u50CF\\u5217\\u8868\",\n          extra: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 31\n              }, this),\n              onClick: () => setLoading(true),\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 46\n              }, this),\n              onClick: handlePullImage,\n              children: \"\\u62C9\\u53D6\\u955C\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: imageColumns,\n            dataSource: images,\n            rowKey: \"image_id\",\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, \"images\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n        tab: \"GPU\\u76D1\\u63A7\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: gpuStats.map(gpu => /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            style: {\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: `GPU ${gpu.gpu_id} - ${gpu.name}`,\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                gutter: 16,\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: \"GPU\\u4F7F\\u7528\\u7387\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                      percent: gpu.utilization,\n                      status: gpu.utilization > 80 ? 'exception' : 'normal'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: 16\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: \"\\u663E\\u5B58\\u4F7F\\u7528\\u7387\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                      percent: Math.round(gpu.memory_used / gpu.memory_total * 100),\n                      status: gpu.memory_used / gpu.memory_total > 0.8 ? 'exception' : 'normal'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                gutter: 16,\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Statistic, {\n                    title: \"\\u5DF2\\u7528\\u663E\\u5B58\",\n                    value: gpu.memory_used,\n                    suffix: \"MB\",\n                    valueStyle: {\n                      fontSize: 14\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: /*#__PURE__*/_jsxDEV(Statistic, {\n                    title: \"\\u603B\\u663E\\u5B58\",\n                    value: gpu.memory_total,\n                    suffix: \"MB\",\n                    valueStyle: {\n                      fontSize: 14\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this)\n          }, gpu.gpu_id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this)\n      }, \"gpu\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 330,\n    columnNumber: 5\n  }, this);\n};\n_s(DockerManagement, \"mo4WAe14Q/OIDn38Xt5zGwkUjV4=\");\n_c = DockerManagement;\nexport default DockerManagement;\nvar _c;\n$RefreshReg$(_c, \"DockerManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Tabs", "Table", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Card", "Row", "Col", "Statistic", "Progress", "PlusOutlined", "PlayCircleOutlined", "PauseCircleOutlined", "ReloadOutlined", "DeleteOutlined", "EyeOutlined", "DownloadOutlined", "jsxDEV", "_jsxDEV", "TabPane", "Option", "DockerManagement", "_s", "images", "setImages", "containers", "setContainers", "gpuStats", "setGpuStats", "servers", "setServers", "loading", "setLoading", "selectedServer", "setSelectedServer", "repository", "tag", "image_id", "created", "size", "container_id", "name", "image", "status", "ports", "gpu_id", "utilization", "memory_used", "memory_total", "imageColumns", "title", "dataIndex", "key", "render", "text", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "substring", "_", "record", "onConfirm", "handleDeleteImage", "okText", "cancelText", "type", "danger", "icon", "containerColumns", "isRunning", "includes", "onClick", "handleViewLogs", "handleStopContainer", "handleStartContainer", "handleRestartContainer", "handleDeleteContainer", "handlePullImage", "confirm", "content", "layout", "<PERSON><PERSON>", "label", "placeholder", "onOk", "success", "imageId", "filter", "img", "containerId", "updatedContainers", "map", "c", "info", "style", "maxHeight", "overflow", "fontSize", "width", "runningContainers", "length", "totalContainers", "gutter", "marginBottom", "span", "value", "valueStyle", "Math", "round", "suffix", "defaultActiveKey", "tab", "extra", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "gpu", "percent", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/itai/frontend/src/components/Docker/DockerManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Tabs,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n} from 'antd';\nimport { dockerApi, serverApi } from '../../services/api';\nimport {\n  PlusOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  ReloadOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  DownloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\nconst { TabPane } = Tabs;\nconst { Option } = Select;\n\ninterface DockerImage {\n  repository: string;\n  tag: string;\n  image_id: string;\n  created: string;\n  size: string;\n}\n\ninterface DockerContainer {\n  container_id: string;\n  name: string;\n  image: string;\n  status: string;\n  ports: string;\n  created: string;\n}\n\ninterface ContainerStats {\n  container_id: string;\n  cpu_usage_percent: number;\n  memory_usage: string;\n  memory_limit: string;\n  memory_usage_percent: number;\n  network_io: string;\n  block_io: string;\n}\n\ninterface GPUStats {\n  gpu_id: number;\n  name: string;\n  utilization: number;\n  memory_used: number;\n  memory_total: number;\n}\n\nconst DockerManagement: React.FC = () => {\n  const [images, setImages] = useState<DockerImage[]>([]);\n  const [containers, setContainers] = useState<DockerContainer[]>([]);\n  const [gpuStats, setGpuStats] = useState<GPUStats[]>([]);\n  const [servers, setServers] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedServer, setSelectedServer] = useState<number>(1);\n\n  // 模拟数据\n  useEffect(() => {\n    setImages([\n      {\n        repository: 'nginx',\n        tag: 'latest',\n        image_id: 'sha256:abcd1234',\n        created: '2024-01-01 00:00:00',\n        size: '133MB',\n      },\n      {\n        repository: 'mysql',\n        tag: '8.0',\n        image_id: 'sha256:efgh5678',\n        created: '2024-01-01 00:00:00',\n        size: '521MB',\n      },\n    ]);\n\n    setContainers([\n      {\n        container_id: 'abcd1234efgh',\n        name: 'web-server',\n        image: 'nginx:latest',\n        status: 'Up 2 hours',\n        ports: '0.0.0.0:80->80/tcp',\n        created: '2024-01-01 00:00:00',\n      },\n      {\n        container_id: 'ijkl5678mnop',\n        name: 'database',\n        image: 'mysql:8.0',\n        status: 'Up 1 day',\n        ports: '0.0.0.0:3306->3306/tcp',\n        created: '2024-01-01 00:00:00',\n      },\n    ]);\n\n    setGpuStats([\n      {\n        gpu_id: 0,\n        name: 'NVIDIA GeForce RTX 4090',\n        utilization: 75,\n        memory_used: 12000,\n        memory_total: 24000,\n      },\n      {\n        gpu_id: 1,\n        name: 'NVIDIA GeForce RTX 4090',\n        utilization: 45,\n        memory_used: 8000,\n        memory_total: 24000,\n      },\n    ]);\n  }, []);\n\n  const imageColumns: ColumnsType<DockerImage> = [\n    {\n      title: '镜像仓库',\n      dataIndex: 'repository',\n      key: 'repository',\n    },\n    {\n      title: '标签',\n      dataIndex: 'tag',\n      key: 'tag',\n      render: (text) => <Tag color=\"blue\">{text}</Tag>,\n    },\n    {\n      title: '镜像ID',\n      dataIndex: 'image_id',\n      key: 'image_id',\n      render: (text) => <code>{text.substring(0, 12)}</code>,\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created',\n      key: 'created',\n    },\n    {\n      title: '大小',\n      dataIndex: 'size',\n      key: 'size',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Popconfirm\n            title=\"确定要删除这个镜像吗？\"\n            onConfirm={() => handleDeleteImage(record.image_id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const containerColumns: ColumnsType<DockerContainer> = [\n    {\n      title: '容器名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '镜像',\n      dataIndex: 'image',\n      key: 'image',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (text) => {\n        const isRunning = text.includes('Up');\n        return (\n          <Tag color={isRunning ? 'green' : 'red'}>\n            {isRunning ? '运行中' : '已停止'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '端口映射',\n      dataIndex: 'ports',\n      key: 'ports',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created',\n      key: 'created',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => {\n        const isRunning = record.status.includes('Up');\n        return (\n          <Space size=\"middle\">\n            <Button\n              type=\"link\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewLogs(record.container_id)}\n            >\n              日志\n            </Button>\n            <Button\n              type=\"link\"\n              icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}\n              onClick={() => isRunning ? handleStopContainer(record.container_id) : handleStartContainer(record.container_id)}\n            >\n              {isRunning ? '停止' : '启动'}\n            </Button>\n            <Button\n              type=\"link\"\n              icon={<ReloadOutlined />}\n              onClick={() => handleRestartContainer(record.container_id)}\n            >\n              重启\n            </Button>\n            <Popconfirm\n              title=\"确定要删除这个容器吗？\"\n              onConfirm={() => handleDeleteContainer(record.container_id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button type=\"link\" danger icon={<DeleteOutlined />}>\n                删除\n              </Button>\n            </Popconfirm>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  const handlePullImage = () => {\n    Modal.confirm({\n      title: '拉取镜像',\n      content: (\n        <Form layout=\"vertical\">\n          <Form.Item label=\"镜像名称\" name=\"imageName\">\n            <Input placeholder=\"例如: nginx:latest\" />\n          </Form.Item>\n        </Form>\n      ),\n      onOk: () => {\n        message.success('镜像拉取成功');\n      },\n    });\n  };\n\n  const handleDeleteImage = (imageId: string) => {\n    setImages(images.filter(img => img.image_id !== imageId));\n    message.success('镜像删除成功');\n  };\n\n  const handleStartContainer = (containerId: string) => {\n    const updatedContainers = containers.map(c =>\n      c.container_id === containerId\n        ? { ...c, status: 'Up 1 second' }\n        : c\n    );\n    setContainers(updatedContainers);\n    message.success('容器启动成功');\n  };\n\n  const handleStopContainer = (containerId: string) => {\n    const updatedContainers = containers.map(c =>\n      c.container_id === containerId\n        ? { ...c, status: 'Exited (0) 1 second ago' }\n        : c\n    );\n    setContainers(updatedContainers);\n    message.success('容器停止成功');\n  };\n\n  const handleRestartContainer = (containerId: string) => {\n    message.success('容器重启成功');\n  };\n\n  const handleDeleteContainer = (containerId: string) => {\n    setContainers(containers.filter(c => c.container_id !== containerId));\n    message.success('容器删除成功');\n  };\n\n  const handleViewLogs = (containerId: string) => {\n    Modal.info({\n      title: `容器日志 - ${containerId.substring(0, 12)}`,\n      content: (\n        <div style={{ maxHeight: 400, overflow: 'auto' }}>\n          <pre style={{ fontSize: 12 }}>\n            2024-01-01 00:00:00 [INFO] Container started{'\\n'}\n            2024-01-01 00:00:01 [INFO] Application ready{'\\n'}\n            2024-01-01 00:00:02 [INFO] Listening on port 80{'\\n'}\n          </pre>\n        </div>\n      ),\n      width: 800,\n    });\n  };\n\n  const runningContainers = containers.filter(c => c.status.includes('Up')).length;\n  const totalContainers = containers.length;\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总镜像数\"\n              value={images.length}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总容器数\"\n              value={totalContainers}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"运行中容器\"\n              value={runningContainers}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"容器运行率\"\n              value={totalContainers > 0 ? Math.round((runningContainers / totalContainers) * 100) : 0}\n              suffix=\"%\"\n              valueStyle={{ color: runningContainers / totalContainers > 0.8 ? '#52c41a' : '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Tabs defaultActiveKey=\"containers\">\n        <TabPane tab=\"容器管理\" key=\"containers\">\n          <Card\n            title=\"容器列表\"\n            extra={\n              <Space>\n                <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>\n                  刷新\n                </Button>\n                <Button type=\"primary\" icon={<PlusOutlined />}>\n                  创建容器\n                </Button>\n              </Space>\n            }\n          >\n            <Table\n              columns={containerColumns}\n              dataSource={containers}\n              rowKey=\"container_id\"\n              loading={loading}\n            />\n          </Card>\n        </TabPane>\n\n        <TabPane tab=\"镜像管理\" key=\"images\">\n          <Card\n            title=\"镜像列表\"\n            extra={\n              <Space>\n                <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>\n                  刷新\n                </Button>\n                <Button type=\"primary\" icon={<DownloadOutlined />} onClick={handlePullImage}>\n                  拉取镜像\n                </Button>\n              </Space>\n            }\n          >\n            <Table\n              columns={imageColumns}\n              dataSource={images}\n              rowKey=\"image_id\"\n              loading={loading}\n            />\n          </Card>\n        </TabPane>\n\n        <TabPane tab=\"GPU监控\" key=\"gpu\">\n          <Row gutter={16}>\n            {gpuStats.map((gpu) => (\n              <Col span={12} key={gpu.gpu_id} style={{ marginBottom: 16 }}>\n                <Card title={`GPU ${gpu.gpu_id} - ${gpu.name}`}>\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <div style={{ marginBottom: 16 }}>\n                        <div>GPU使用率</div>\n                        <Progress\n                          percent={gpu.utilization}\n                          status={gpu.utilization > 80 ? 'exception' : 'normal'}\n                        />\n                      </div>\n                    </Col>\n                    <Col span={12}>\n                      <div style={{ marginBottom: 16 }}>\n                        <div>显存使用率</div>\n                        <Progress\n                          percent={Math.round((gpu.memory_used / gpu.memory_total) * 100)}\n                          status={gpu.memory_used / gpu.memory_total > 0.8 ? 'exception' : 'normal'}\n                        />\n                      </div>\n                    </Col>\n                  </Row>\n                  <Row gutter={16}>\n                    <Col span={12}>\n                      <Statistic\n                        title=\"已用显存\"\n                        value={gpu.memory_used}\n                        suffix=\"MB\"\n                        valueStyle={{ fontSize: 14 }}\n                      />\n                    </Col>\n                    <Col span={12}>\n                      <Statistic\n                        title=\"总显存\"\n                        value={gpu.memory_total}\n                        suffix=\"MB\"\n                        valueStyle={{ fontSize: 14 }}\n                      />\n                    </Col>\n                  </Row>\n                </Card>\n              </Col>\n            ))}\n          </Row>\n        </TabPane>\n      </Tabs>\n    </div>\n  );\n};\n\nexport default DockerManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,QACH,MAAM;AAEb,SACEC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC;AAAQ,CAAC,GAAGzB,IAAI;AACxB,MAAM;EAAE0B;AAAO,CAAC,GAAGlB,MAAM;AAqCzB,MAAMmB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhC,QAAQ,CAAgB,EAAE,CAAC;EACvD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAoB,EAAE,CAAC;EACnE,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAa,EAAE,CAAC;EACxD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAQ,EAAE,CAAC;EACjD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAS,CAAC,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACd+B,SAAS,CAAC,CACR;MACEW,UAAU,EAAE,OAAO;MACnBC,GAAG,EAAE,QAAQ;MACbC,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,qBAAqB;MAC9BC,IAAI,EAAE;IACR,CAAC,EACD;MACEJ,UAAU,EAAE,OAAO;MACnBC,GAAG,EAAE,KAAK;MACVC,QAAQ,EAAE,iBAAiB;MAC3BC,OAAO,EAAE,qBAAqB;MAC9BC,IAAI,EAAE;IACR,CAAC,CACF,CAAC;IAEFb,aAAa,CAAC,CACZ;MACEc,YAAY,EAAE,cAAc;MAC5BC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,cAAc;MACrBC,MAAM,EAAE,YAAY;MACpBC,KAAK,EAAE,oBAAoB;MAC3BN,OAAO,EAAE;IACX,CAAC,EACD;MACEE,YAAY,EAAE,cAAc;MAC5BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE,UAAU;MAClBC,KAAK,EAAE,wBAAwB;MAC/BN,OAAO,EAAE;IACX,CAAC,CACF,CAAC;IAEFV,WAAW,CAAC,CACV;MACEiB,MAAM,EAAE,CAAC;MACTJ,IAAI,EAAE,yBAAyB;MAC/BK,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE;IAChB,CAAC,EACD;MACEH,MAAM,EAAE,CAAC;MACTJ,IAAI,EAAE,yBAAyB;MAC/BK,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE;IAChB,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAsC,GAAG,CAC7C;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,KAAK;IAChBC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAGC,IAAI,iBAAKpC,OAAA,CAACpB,GAAG;MAACyD,KAAK,EAAC,MAAM;MAAAC,QAAA,EAAEF;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACjD,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGC,IAAI,iBAAKpC,OAAA;MAAAsC,QAAA,EAAOF,IAAI,CAACO,SAAS,CAAC,CAAC,EAAE,EAAE;IAAC;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EACvD,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACS,CAAC,EAAEC,MAAM,kBAChB7C,OAAA,CAACrB,KAAK;MAAC0C,IAAI,EAAC,QAAQ;MAAAiB,QAAA,eAClBtC,OAAA,CAACd,UAAU;QACT8C,KAAK,EAAC,oEAAa;QACnBc,SAAS,EAAEA,CAAA,KAAMC,iBAAiB,CAACF,MAAM,CAAC1B,QAAQ,CAAE;QACpD6B,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAX,QAAA,eAEftC,OAAA,CAACtB,MAAM;UAACwE,IAAI,EAAC,MAAM;UAACC,MAAM;UAACC,IAAI,eAAEpD,OAAA,CAACJ,cAAc;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMW,gBAA8C,GAAG,CACrD;IACErB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGC,IAAI,IAAK;MAChB,MAAMkB,SAAS,GAAGlB,IAAI,CAACmB,QAAQ,CAAC,IAAI,CAAC;MACrC,oBACEvD,OAAA,CAACpB,GAAG;QAACyD,KAAK,EAAEiB,SAAS,GAAG,OAAO,GAAG,KAAM;QAAAhB,QAAA,EACrCgB,SAAS,GAAG,KAAK,GAAG;MAAK;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAEV;EACF,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACS,CAAC,EAAEC,MAAM,KAAK;MACrB,MAAMS,SAAS,GAAGT,MAAM,CAACpB,MAAM,CAAC8B,QAAQ,CAAC,IAAI,CAAC;MAC9C,oBACEvD,OAAA,CAACrB,KAAK;QAAC0C,IAAI,EAAC,QAAQ;QAAAiB,QAAA,gBAClBtC,OAAA,CAACtB,MAAM;UACLwE,IAAI,EAAC,MAAM;UACXE,IAAI,eAAEpD,OAAA,CAACH,WAAW;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBc,OAAO,EAAEA,CAAA,KAAMC,cAAc,CAACZ,MAAM,CAACvB,YAAY,CAAE;UAAAgB,QAAA,EACpD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA,CAACtB,MAAM;UACLwE,IAAI,EAAC,MAAM;UACXE,IAAI,EAAEE,SAAS,gBAAGtD,OAAA,CAACN,mBAAmB;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG1C,OAAA,CAACP,kBAAkB;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnEc,OAAO,EAAEA,CAAA,KAAMF,SAAS,GAAGI,mBAAmB,CAACb,MAAM,CAACvB,YAAY,CAAC,GAAGqC,oBAAoB,CAACd,MAAM,CAACvB,YAAY,CAAE;UAAAgB,QAAA,EAE/GgB,SAAS,GAAG,IAAI,GAAG;QAAI;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACT1C,OAAA,CAACtB,MAAM;UACLwE,IAAI,EAAC,MAAM;UACXE,IAAI,eAAEpD,OAAA,CAACL,cAAc;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBc,OAAO,EAAEA,CAAA,KAAMI,sBAAsB,CAACf,MAAM,CAACvB,YAAY,CAAE;UAAAgB,QAAA,EAC5D;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA,CAACd,UAAU;UACT8C,KAAK,EAAC,oEAAa;UACnBc,SAAS,EAAEA,CAAA,KAAMe,qBAAqB,CAAChB,MAAM,CAACvB,YAAY,CAAE;UAC5D0B,MAAM,EAAC,cAAI;UACXC,UAAU,EAAC,cAAI;UAAAX,QAAA,eAEftC,OAAA,CAACtB,MAAM;YAACwE,IAAI,EAAC,MAAM;YAACC,MAAM;YAACC,IAAI,eAAEpD,OAAA,CAACJ,cAAc;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAAC;UAErD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAEZ;EACF,CAAC,CACF;EAED,MAAMoB,eAAe,GAAGA,CAAA,KAAM;IAC5BjF,KAAK,CAACkF,OAAO,CAAC;MACZ/B,KAAK,EAAE,MAAM;MACbgC,OAAO,eACLhE,OAAA,CAAClB,IAAI;QAACmF,MAAM,EAAC,UAAU;QAAA3B,QAAA,eACrBtC,OAAA,CAAClB,IAAI,CAACoF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAC5C,IAAI,EAAC,WAAW;UAAAe,QAAA,eACtCtC,OAAA,CAACjB,KAAK;YAACqF,WAAW,EAAC;UAAkB;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACP;MACD2B,IAAI,EAAEA,CAAA,KAAM;QACVpF,OAAO,CAACqF,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMvB,iBAAiB,GAAIwB,OAAe,IAAK;IAC7CjE,SAAS,CAACD,MAAM,CAACmE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACtD,QAAQ,KAAKoD,OAAO,CAAC,CAAC;IACzDtF,OAAO,CAACqF,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMX,oBAAoB,GAAIe,WAAmB,IAAK;IACpD,MAAMC,iBAAiB,GAAGpE,UAAU,CAACqE,GAAG,CAACC,CAAC,IACxCA,CAAC,CAACvD,YAAY,KAAKoD,WAAW,GAC1B;MAAE,GAAGG,CAAC;MAAEpD,MAAM,EAAE;IAAc,CAAC,GAC/BoD,CACN,CAAC;IACDrE,aAAa,CAACmE,iBAAiB,CAAC;IAChC1F,OAAO,CAACqF,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMZ,mBAAmB,GAAIgB,WAAmB,IAAK;IACnD,MAAMC,iBAAiB,GAAGpE,UAAU,CAACqE,GAAG,CAACC,CAAC,IACxCA,CAAC,CAACvD,YAAY,KAAKoD,WAAW,GAC1B;MAAE,GAAGG,CAAC;MAAEpD,MAAM,EAAE;IAA0B,CAAC,GAC3CoD,CACN,CAAC;IACDrE,aAAa,CAACmE,iBAAiB,CAAC;IAChC1F,OAAO,CAACqF,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMV,sBAAsB,GAAIc,WAAmB,IAAK;IACtDzF,OAAO,CAACqF,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMT,qBAAqB,GAAIa,WAAmB,IAAK;IACrDlE,aAAa,CAACD,UAAU,CAACiE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACvD,YAAY,KAAKoD,WAAW,CAAC,CAAC;IACrEzF,OAAO,CAACqF,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMb,cAAc,GAAIiB,WAAmB,IAAK;IAC9C7F,KAAK,CAACiG,IAAI,CAAC;MACT9C,KAAK,EAAE,UAAU0C,WAAW,CAAC/B,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;MAC/CqB,OAAO,eACLhE,OAAA;QAAK+E,KAAK,EAAE;UAAEC,SAAS,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAA3C,QAAA,eAC/CtC,OAAA;UAAK+E,KAAK,EAAE;YAAEG,QAAQ,EAAE;UAAG,CAAE;UAAA5C,QAAA,GAAC,8CACgB,EAAC,IAAI,EAAC,8CACN,EAAC,IAAI,EAAC,iDACH,EAAC,IAAI;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MACDyC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAG7E,UAAU,CAACiE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACpD,MAAM,CAAC8B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC8B,MAAM;EAChF,MAAMC,eAAe,GAAG/E,UAAU,CAAC8E,MAAM;EAEzC,oBACErF,OAAA;IAAAsC,QAAA,gBACEtC,OAAA,CAACZ,GAAG;MAACmG,MAAM,EAAE,EAAG;MAACR,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAlD,QAAA,gBAC3CtC,OAAA,CAACX,GAAG;QAACoG,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,0BAAM;YACZ0D,KAAK,EAAErF,MAAM,CAACgF,MAAO;YACrBM,UAAU,EAAE;cAAEtD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAACoG,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,0BAAM;YACZ0D,KAAK,EAAEJ,eAAgB;YACvBK,UAAU,EAAE;cAAEtD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAACoG,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,gCAAO;YACb0D,KAAK,EAAEN,iBAAkB;YACzBO,UAAU,EAAE;cAAEtD,KAAK,EAAE;YAAU;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAACoG,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXtC,OAAA,CAACb,IAAI;UAAAmD,QAAA,eACHtC,OAAA,CAACV,SAAS;YACR0C,KAAK,EAAC,gCAAO;YACb0D,KAAK,EAAEJ,eAAe,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAET,iBAAiB,GAAGE,eAAe,GAAI,GAAG,CAAC,GAAG,CAAE;YACzFQ,MAAM,EAAC,GAAG;YACVH,UAAU,EAAE;cAAEtD,KAAK,EAAE+C,iBAAiB,GAAGE,eAAe,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN1C,OAAA,CAACxB,IAAI;MAACuH,gBAAgB,EAAC,YAAY;MAAAzD,QAAA,gBACjCtC,OAAA,CAACC,OAAO;QAAC+F,GAAG,EAAC,0BAAM;QAAA1D,QAAA,eACjBtC,OAAA,CAACb,IAAI;UACH6C,KAAK,EAAC,0BAAM;UACZiE,KAAK,eACHjG,OAAA,CAACrB,KAAK;YAAA2D,QAAA,gBACJtC,OAAA,CAACtB,MAAM;cAAC0E,IAAI,eAAEpD,OAAA,CAACL,cAAc;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACc,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAAC,IAAI,CAAE;cAAAwB,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1C,OAAA,CAACtB,MAAM;cAACwE,IAAI,EAAC,SAAS;cAACE,IAAI,eAAEpD,OAAA,CAACR,YAAY;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;UAAAJ,QAAA,eAEDtC,OAAA,CAACvB,KAAK;YACJyH,OAAO,EAAE7C,gBAAiB;YAC1B8C,UAAU,EAAE5F,UAAW;YACvB6F,MAAM,EAAC,cAAc;YACrBvF,OAAO,EAAEA;UAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GApBe,YAAY;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqB3B,CAAC,eAEV1C,OAAA,CAACC,OAAO;QAAC+F,GAAG,EAAC,0BAAM;QAAA1D,QAAA,eACjBtC,OAAA,CAACb,IAAI;UACH6C,KAAK,EAAC,0BAAM;UACZiE,KAAK,eACHjG,OAAA,CAACrB,KAAK;YAAA2D,QAAA,gBACJtC,OAAA,CAACtB,MAAM;cAAC0E,IAAI,eAAEpD,OAAA,CAACL,cAAc;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACc,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAAC,IAAI,CAAE;cAAAwB,QAAA,EAAC;YAEnE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1C,OAAA,CAACtB,MAAM;cAACwE,IAAI,EAAC,SAAS;cAACE,IAAI,eAAEpD,OAAA,CAACF,gBAAgB;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACc,OAAO,EAAEM,eAAgB;cAAAxB,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR;UAAAJ,QAAA,eAEDtC,OAAA,CAACvB,KAAK;YACJyH,OAAO,EAAEnE,YAAa;YACtBoE,UAAU,EAAE9F,MAAO;YACnB+F,MAAM,EAAC,UAAU;YACjBvF,OAAO,EAAEA;UAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GApBe,QAAQ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBvB,CAAC,eAEV1C,OAAA,CAACC,OAAO;QAAC+F,GAAG,EAAC,iBAAO;QAAA1D,QAAA,eAClBtC,OAAA,CAACZ,GAAG;UAACmG,MAAM,EAAE,EAAG;UAAAjD,QAAA,EACb7B,QAAQ,CAACmE,GAAG,CAAEyB,GAAG,iBAChBrG,OAAA,CAACX,GAAG;YAACoG,IAAI,EAAE,EAAG;YAAkBV,KAAK,EAAE;cAAES,YAAY,EAAE;YAAG,CAAE;YAAAlD,QAAA,eAC1DtC,OAAA,CAACb,IAAI;cAAC6C,KAAK,EAAE,OAAOqE,GAAG,CAAC1E,MAAM,MAAM0E,GAAG,CAAC9E,IAAI,EAAG;cAAAe,QAAA,gBAC7CtC,OAAA,CAACZ,GAAG;gBAACmG,MAAM,EAAE,EAAG;gBAAAjD,QAAA,gBACdtC,OAAA,CAACX,GAAG;kBAACoG,IAAI,EAAE,EAAG;kBAAAnD,QAAA,eACZtC,OAAA;oBAAK+E,KAAK,EAAE;sBAAES,YAAY,EAAE;oBAAG,CAAE;oBAAAlD,QAAA,gBAC/BtC,OAAA;sBAAAsC,QAAA,EAAK;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACjB1C,OAAA,CAACT,QAAQ;sBACP+G,OAAO,EAAED,GAAG,CAACzE,WAAY;sBACzBH,MAAM,EAAE4E,GAAG,CAACzE,WAAW,GAAG,EAAE,GAAG,WAAW,GAAG;oBAAS;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN1C,OAAA,CAACX,GAAG;kBAACoG,IAAI,EAAE,EAAG;kBAAAnD,QAAA,eACZtC,OAAA;oBAAK+E,KAAK,EAAE;sBAAES,YAAY,EAAE;oBAAG,CAAE;oBAAAlD,QAAA,gBAC/BtC,OAAA;sBAAAsC,QAAA,EAAK;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChB1C,OAAA,CAACT,QAAQ;sBACP+G,OAAO,EAAEV,IAAI,CAACC,KAAK,CAAEQ,GAAG,CAACxE,WAAW,GAAGwE,GAAG,CAACvE,YAAY,GAAI,GAAG,CAAE;sBAChEL,MAAM,EAAE4E,GAAG,CAACxE,WAAW,GAAGwE,GAAG,CAACvE,YAAY,GAAG,GAAG,GAAG,WAAW,GAAG;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1C,OAAA,CAACZ,GAAG;gBAACmG,MAAM,EAAE,EAAG;gBAAAjD,QAAA,gBACdtC,OAAA,CAACX,GAAG;kBAACoG,IAAI,EAAE,EAAG;kBAAAnD,QAAA,eACZtC,OAAA,CAACV,SAAS;oBACR0C,KAAK,EAAC,0BAAM;oBACZ0D,KAAK,EAAEW,GAAG,CAACxE,WAAY;oBACvBiE,MAAM,EAAC,IAAI;oBACXH,UAAU,EAAE;sBAAET,QAAQ,EAAE;oBAAG;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1C,OAAA,CAACX,GAAG;kBAACoG,IAAI,EAAE,EAAG;kBAAAnD,QAAA,eACZtC,OAAA,CAACV,SAAS;oBACR0C,KAAK,EAAC,oBAAK;oBACX0D,KAAK,EAAEW,GAAG,CAACvE,YAAa;oBACxBgE,MAAM,EAAC,IAAI;oBACXH,UAAU,EAAE;sBAAET,QAAQ,EAAE;oBAAG;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAxCW2D,GAAG,CAAC1E,MAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyCzB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC,GA9CiB,KAAK;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+CrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtC,EAAA,CA/YID,gBAA0B;AAAAoG,EAAA,GAA1BpG,gBAA0B;AAiZhC,eAAeA,gBAAgB;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}