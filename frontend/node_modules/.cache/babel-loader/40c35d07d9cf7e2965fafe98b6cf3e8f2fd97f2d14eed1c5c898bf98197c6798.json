{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Space, Tag, Modal, Form, Input, Select, Switch, message, Popconfirm, Card, Row, Col, Statistic } from 'antd';\nimport { serverApi } from '../../services/api';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, StopOutlined, ReloadOutlined, EyeOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst ServerManagement = () => {\n  _s();\n  const [servers, setServers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingServer, setEditingServer] = useState(null);\n  const [commandModalVisible, setCommandModalVisible] = useState(false);\n  const [selectedServer, setSelectedServer] = useState(null);\n  const [form] = Form.useForm();\n  const [commandForm] = Form.useForm();\n\n  // 加载服务器数据\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      console.log('开始加载服务器列表...');\n      const data = await serverApi.getServers();\n      console.log('API响应数据:', data);\n      if (data === undefined || data === null) {\n        console.warn('API返回空数据，使用模拟数据');\n        // 使用模拟数据作为后备\n        const mockData = [{\n          id: 1,\n          name: \"示例服务器\",\n          host: \"*************\",\n          port: 22,\n          username: \"root\",\n          auth_type: \"password\",\n          description: \"示例服务器（模拟数据）\",\n          tags: [\"示例\"],\n          monitoring_enabled: true,\n          alert_enabled: true,\n          is_active: true,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }];\n        setServers(mockData.map(server => ({\n          ...server,\n          status: 'offline' // 模拟状态\n        })));\n        message.warning('无法连接到后端服务，显示模拟数据');\n      } else if (Array.isArray(data)) {\n        console.log('成功加载服务器列表，数量:', data.length);\n        setServers(data.map(server => ({\n          ...server,\n          status: Math.random() > 0.5 ? 'online' : 'offline' // 模拟状态\n        })));\n      } else {\n        console.error('服务器数据格式错误:', data);\n        setServers([]);\n        message.error('服务器数据格式错误');\n      }\n    } catch (error) {\n      console.error('加载服务器列表失败:', error);\n      message.error(`加载服务器列表失败: ${error instanceof Error ? error.message : '未知错误'}`);\n      setServers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadServers();\n  }, []);\n  const columns = [{\n    title: '服务器名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: record.status === 'online' ? 'green' : record.status === 'offline' ? 'red' : 'orange',\n        children: record.status === 'online' ? '在线' : record.status === 'offline' ? '离线' : '连接中'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '地址',\n    dataIndex: 'host',\n    key: 'host',\n    render: (text, record) => `${text}:${record.port}`\n  }, {\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '认证方式',\n    dataIndex: 'auth_type',\n    key: 'auth_type',\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: text === 'password' ? 'blue' : 'green',\n      children: text === 'password' ? '密码' : '密钥'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '标签',\n    dataIndex: 'tags',\n    key: 'tags',\n    render: tags => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: tags === null || tags === void 0 ? void 0 : tags.map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: tag\n      }, tag, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 13\n      }, this))\n    }, void 0, false)\n  }, {\n    title: '监控状态',\n    key: 'monitoring',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        color: record.monitoring_enabled ? 'green' : 'default',\n        children: record.monitoring_enabled ? '已启用' : '已禁用'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: record.alert_enabled ? 'orange' : 'default',\n        children: record.alert_enabled ? '告警开启' : '告警关闭'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleViewServer(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEditServer(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: record.monitoring_enabled ? /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 47\n        }, this) : /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 66\n        }, this),\n        onClick: () => handleToggleMonitoring(record),\n        children: record.monitoring_enabled ? '停止监控' : '开始监控'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleExecuteCommand(record),\n        children: \"\\u6267\\u884C\\u547D\\u4EE4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u53F0\\u670D\\u52A1\\u5668\\u5417\\uFF1F\",\n        onConfirm: () => handleDeleteServer(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this)\n  }];\n  const handleAddServer = () => {\n    setEditingServer(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditServer = server => {\n    setEditingServer(server);\n    form.setFieldsValue(server);\n    setModalVisible(true);\n  };\n  const handleViewServer = async server => {\n    try {\n      var _server$tags;\n      // 测试连接状态\n      const connectionResult = await serverApi.testConnection(server.id);\n      Modal.info({\n        title: `服务器详情 - ${server.name}`,\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 16\n            }, this), \" \", server.host, \":\", server.port]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u7528\\u6237\\u540D:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 16\n            }, this), \" \", server.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BA4\\u8BC1\\u65B9\\u5F0F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 16\n            }, this), \" \", server.auth_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u63CF\\u8FF0:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 16\n            }, this), \" \", server.description]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6807\\u7B7E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 16\n            }, this), \" \", (_server$tags = server.tags) === null || _server$tags === void 0 ? void 0 : _server$tags.join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8FDE\\u63A5\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 16\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: connectionResult.success ? 'green' : 'red',\n              children: connectionResult.success ? '连接正常' : '连接失败'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), connectionResult.success && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5EF6\\u8FDF:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 18\n            }, this), \" \", connectionResult.latency, \"ms\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u521B\\u5EFA\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 16\n            }, this), \" \", new Date(server.created_at).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this),\n        width: 600\n      });\n    } catch (error) {\n      message.error('获取服务器详情失败');\n    }\n  };\n  const handleDeleteServer = async id => {\n    try {\n      await serverApi.deleteServer(id);\n      setServers(servers.filter(s => s.id !== id));\n      message.success('服务器删除成功');\n    } catch (error) {\n      message.error('删除服务器失败');\n    }\n  };\n  const handleToggleMonitoring = async server => {\n    try {\n      if (server.monitoring_enabled) {\n        await serverApi.stopMonitoring(server.id);\n      } else {\n        await serverApi.startMonitoring(server.id);\n      }\n      const updatedServers = (servers || []).map(s => s.id === server.id ? {\n        ...s,\n        monitoring_enabled: !s.monitoring_enabled\n      } : s);\n      setServers(updatedServers);\n      message.success(`${server.name} 监控已${server.monitoring_enabled ? '停止' : '启动'}`);\n    } catch (error) {\n      message.error('切换监控状态失败');\n    }\n  };\n  const handleExecuteCommand = server => {\n    setSelectedServer(server);\n    commandForm.resetFields();\n    setCommandModalVisible(true);\n  };\n  const handleCommandOk = async () => {\n    try {\n      const values = await commandForm.validateFields();\n      const result = await serverApi.executeCommand(selectedServer.id, values);\n      Modal.info({\n        title: '命令执行结果',\n        content: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9000\\u51FA\\u7801:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 16\n            }, this), \" \", result.exit_code]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6267\\u884C\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 16\n            }, this), \" \", result.duration, \"\\u79D2\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8F93\\u51FA:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                background: '#f5f5f5',\n                padding: 8,\n                borderRadius: 4,\n                maxHeight: 200,\n                overflow: 'auto'\n              },\n              children: result.stdout\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), result.stderr && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u9519\\u8BEF:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                background: '#fff2f0',\n                padding: 8,\n                borderRadius: 4,\n                maxHeight: 200,\n                overflow: 'auto',\n                color: 'red'\n              },\n              children: result.stderr\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this),\n        width: 800\n      });\n      setCommandModalVisible(false);\n      commandForm.resetFields();\n    } catch (error) {\n      message.error('命令执行失败');\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingServer) {\n        // 更新服务器\n        await serverApi.updateServer(editingServer.id, values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器更新成功');\n      } else {\n        // 创建新服务器\n        await serverApi.createServer(values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器添加成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error(editingServer ? '更新服务器失败' : '添加服务器失败');\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n    form.resetFields();\n  };\n  const onlineCount = servers.filter(s => s.status === 'online').length;\n  const totalCount = servers.length;\n  const monitoringCount = servers.filter(s => s.monitoring_enabled).length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u670D\\u52A1\\u5668\\u6570\",\n            value: totalCount,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u670D\\u52A1\\u5668\",\n            value: onlineCount,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u76D1\\u63A7\\u4E2D\",\n            value: monitoringCount,\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u7387\",\n            value: totalCount > 0 ? Math.round(onlineCount / totalCount * 100) : 0,\n            suffix: \"%\",\n            valueStyle: {\n              color: onlineCount / totalCount > 0.8 ? '#52c41a' : '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u670D\\u52A1\\u5668\\u5217\\u8868\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 27\n          }, this),\n          onClick: () => setLoading(true),\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 42\n          }, this),\n          onClick: handleAddServer,\n          children: \"\\u6DFB\\u52A0\\u670D\\u52A1\\u5668\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: servers,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingServer ? '编辑服务器' : '添加服务器',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          port: 22,\n          auth_type: 'password',\n          monitoring_enabled: true,\n          alert_enabled: true\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u670D\\u52A1\\u5668\\u540D\\u79F0\",\n              name: \"name\",\n              rules: [{\n                required: true,\n                message: '请输入服务器名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u670D\\u52A1\\u5668\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u670D\\u52A1\\u5668\\u5730\\u5740\",\n              name: \"host\",\n              rules: [{\n                required: true,\n                message: '请输入服务器地址'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\\u6216\\u57DF\\u540D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u7AEF\\u53E3\",\n              name: \"port\",\n              rules: [{\n                required: true,\n                message: '请输入端口号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u7528\\u6237\\u540D\",\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"root\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u8BA4\\u8BC1\\u65B9\\u5F0F\",\n              name: \"auth_type\",\n              rules: [{\n                required: true,\n                message: '请选择认证方式'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"password\",\n                  children: \"\\u5BC6\\u7801\\u8BA4\\u8BC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"key\",\n                  children: \"\\u5BC6\\u94A5\\u8BA4\\u8BC1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u63CF\\u8FF0\",\n          name: \"description\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u670D\\u52A1\\u5668\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u542F\\u7528\\u76D1\\u63A7\",\n              name: \"monitoring_enabled\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u542F\\u7528\\u544A\\u8B66\",\n              name: \"alert_enabled\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `执行命令 - ${selectedServer === null || selectedServer === void 0 ? void 0 : selectedServer.name}`,\n      open: commandModalVisible,\n      onOk: handleCommandOk,\n      onCancel: () => setCommandModalVisible(false),\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: commandForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u547D\\u4EE4\",\n          name: \"command\",\n          rules: [{\n            required: true,\n            message: '请输入要执行的命令'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 3,\n            placeholder: \"\\u4F8B\\u5982: ls -la\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5DE5\\u4F5C\\u76EE\\u5F55\",\n          name: \"working_dir\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4F8B\\u5982: /home/<USER>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u73AF\\u5883\\u53D8\\u91CF\",\n          name: \"env_vars\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 2,\n            placeholder: \"\\u4F8B\\u5982: PATH=/usr/bin:$PATH\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u8D85\\u65F6\\u65F6\\u95F4(\\u79D2)\",\n          name: \"timeout\",\n          initialValue: 300,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 396,\n    columnNumber: 5\n  }, this);\n};\n_s(ServerManagement, \"ZFP59UYYaOx6AEcl/Jb6xcdNGrA=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = ServerManagement;\nexport default ServerManagement;\nvar _c;\n$RefreshReg$(_c, \"ServerManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "Switch", "message", "Popconfirm", "Card", "Row", "Col", "Statistic", "serverApi", "PlusOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "StopOutlined", "ReloadOutlined", "EyeOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "ServerManagement", "_s", "servers", "setServers", "loading", "setLoading", "modalVisible", "setModalVisible", "editingServer", "setEditingServer", "commandModalVisible", "setCommandModalVisible", "selectedServer", "setSelectedServer", "form", "useForm", "commandForm", "loadServers", "console", "log", "data", "getServers", "undefined", "warn", "mockData", "id", "name", "host", "port", "username", "auth_type", "description", "tags", "monitoring_enabled", "alert_enabled", "is_active", "created_at", "Date", "toISOString", "updated_at", "map", "server", "status", "warning", "Array", "isArray", "length", "Math", "random", "error", "Error", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "tag", "_", "size", "type", "icon", "onClick", "handleViewServer", "handleEditServer", "handleToggleMonitoring", "handleExecuteCommand", "onConfirm", "handleDeleteServer", "okText", "cancelText", "danger", "handleAddServer", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_server$tags", "connectionResult", "testConnection", "info", "content", "join", "success", "latency", "toLocaleString", "width", "deleteServer", "filter", "s", "stopMonitoring", "startMonitoring", "updatedServers", "handleCommandOk", "values", "validateFields", "result", "executeCommand", "exit_code", "duration", "style", "marginTop", "background", "padding", "borderRadius", "maxHeight", "overflow", "stdout", "stderr", "handleModalOk", "updateServer", "createServer", "handleModalCancel", "onlineCount", "totalCount", "monitoringCount", "gutter", "marginBottom", "span", "value", "valueStyle", "round", "suffix", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "open", "onOk", "onCancel", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "TextArea", "rows", "valuePropName", "initialValue", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/itai/frontend/src/components/Server/ServerManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Switch,\n  message,\n  Popconfirm,\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Spin,\n  Alert,\n} from 'antd';\nimport { serverApi } from '../../services/api';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  StopOutlined,\n  ReloadOutlined,\n  EyeOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\ninterface Server {\n  id: number;\n  name: string;\n  host: string;\n  port: number;\n  username: string;\n  auth_type: string;\n  description?: string;\n  tags?: string[];\n  monitoring_enabled: boolean;\n  alert_enabled: boolean;\n  is_active: boolean;\n  status?: 'online' | 'offline' | 'connecting';\n  created_at: string;\n  updated_at: string;\n}\n\nconst { Option } = Select;\n\nconst ServerManagement: React.FC = () => {\n  const [servers, setServers] = useState<Server[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingServer, setEditingServer] = useState<Server | null>(null);\n  const [commandModalVisible, setCommandModalVisible] = useState(false);\n  const [selectedServer, setSelectedServer] = useState<Server | null>(null);\n  const [form] = Form.useForm();\n  const [commandForm] = Form.useForm();\n\n  // 加载服务器数据\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      console.log('开始加载服务器列表...');\n\n      const data = await serverApi.getServers();\n      console.log('API响应数据:', data);\n\n      if (data === undefined || data === null) {\n        console.warn('API返回空数据，使用模拟数据');\n        // 使用模拟数据作为后备\n        const mockData = [\n          {\n            id: 1,\n            name: \"示例服务器\",\n            host: \"*************\",\n            port: 22,\n            username: \"root\",\n            auth_type: \"password\",\n            description: \"示例服务器（模拟数据）\",\n            tags: [\"示例\"],\n            monitoring_enabled: true,\n            alert_enabled: true,\n            is_active: true,\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n          }\n        ];\n        setServers(mockData.map((server: any) => ({\n          ...server,\n          status: 'offline' // 模拟状态\n        })));\n        message.warning('无法连接到后端服务，显示模拟数据');\n      } else if (Array.isArray(data)) {\n        console.log('成功加载服务器列表，数量:', data.length);\n        setServers(data.map((server: any) => ({\n          ...server,\n          status: Math.random() > 0.5 ? 'online' : 'offline' // 模拟状态\n        })));\n      } else {\n        console.error('服务器数据格式错误:', data);\n        setServers([]);\n        message.error('服务器数据格式错误');\n      }\n    } catch (error) {\n      console.error('加载服务器列表失败:', error);\n      message.error(`加载服务器列表失败: ${error instanceof Error ? error.message : '未知错误'}`);\n      setServers([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadServers();\n  }, []);\n\n  const columns: ColumnsType<Server> = [\n    {\n      title: '服务器名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <span>{text}</span>\n          <Tag color={record.status === 'online' ? 'green' : record.status === 'offline' ? 'red' : 'orange'}>\n            {record.status === 'online' ? '在线' : record.status === 'offline' ? '离线' : '连接中'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '地址',\n      dataIndex: 'host',\n      key: 'host',\n      render: (text, record) => `${text}:${record.port}`,\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '认证方式',\n      dataIndex: 'auth_type',\n      key: 'auth_type',\n      render: (text) => (\n        <Tag color={text === 'password' ? 'blue' : 'green'}>\n          {text === 'password' ? '密码' : '密钥'}\n        </Tag>\n      ),\n    },\n    {\n      title: '标签',\n      dataIndex: 'tags',\n      key: 'tags',\n      render: (tags: string[]) => (\n        <>\n          {tags?.map((tag) => (\n            <Tag key={tag} color=\"blue\">\n              {tag}\n            </Tag>\n          ))}\n        </>\n      ),\n    },\n    {\n      title: '监控状态',\n      key: 'monitoring',\n      render: (_, record) => (\n        <Space>\n          <Tag color={record.monitoring_enabled ? 'green' : 'default'}>\n            {record.monitoring_enabled ? '已启用' : '已禁用'}\n          </Tag>\n          <Tag color={record.alert_enabled ? 'orange' : 'default'}>\n            {record.alert_enabled ? '告警开启' : '告警关闭'}\n          </Tag>\n        </Space>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"link\"\n            icon={<EyeOutlined />}\n            onClick={() => handleViewServer(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEditServer(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            icon={record.monitoring_enabled ? <StopOutlined /> : <PlayCircleOutlined />}\n            onClick={() => handleToggleMonitoring(record)}\n          >\n            {record.monitoring_enabled ? '停止监控' : '开始监控'}\n          </Button>\n          <Button\n            type=\"link\"\n            onClick={() => handleExecuteCommand(record)}\n          >\n            执行命令\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这台服务器吗？\"\n            onConfirm={() => handleDeleteServer(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  const handleAddServer = () => {\n    setEditingServer(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditServer = (server: Server) => {\n    setEditingServer(server);\n    form.setFieldsValue(server);\n    setModalVisible(true);\n  };\n\n  const handleViewServer = async (server: Server) => {\n    try {\n      // 测试连接状态\n      const connectionResult = await serverApi.testConnection(server.id);\n\n      Modal.info({\n        title: `服务器详情 - ${server.name}`,\n        content: (\n          <div>\n            <p><strong>地址:</strong> {server.host}:{server.port}</p>\n            <p><strong>用户名:</strong> {server.username}</p>\n            <p><strong>认证方式:</strong> {server.auth_type}</p>\n            <p><strong>描述:</strong> {server.description}</p>\n            <p><strong>标签:</strong> {server.tags?.join(', ')}</p>\n            <p><strong>连接状态:</strong>\n              <Tag color={(connectionResult as any).success ? 'green' : 'red'}>\n                {(connectionResult as any).success ? '连接正常' : '连接失败'}\n              </Tag>\n            </p>\n            {(connectionResult as any).success && (\n              <p><strong>延迟:</strong> {(connectionResult as any).latency}ms</p>\n            )}\n            <p><strong>创建时间:</strong> {new Date(server.created_at).toLocaleString()}</p>\n          </div>\n        ),\n        width: 600,\n      });\n    } catch (error) {\n      message.error('获取服务器详情失败');\n    }\n  };\n\n  const handleDeleteServer = async (id: number) => {\n    try {\n      await serverApi.deleteServer(id);\n      setServers(servers.filter(s => s.id !== id));\n      message.success('服务器删除成功');\n    } catch (error) {\n      message.error('删除服务器失败');\n    }\n  };\n\n  const handleToggleMonitoring = async (server: Server) => {\n    try {\n      if (server.monitoring_enabled) {\n        await serverApi.stopMonitoring(server.id);\n      } else {\n        await serverApi.startMonitoring(server.id);\n      }\n\n      const updatedServers = (servers || []).map(s =>\n        s.id === server.id\n          ? { ...s, monitoring_enabled: !s.monitoring_enabled }\n          : s\n      );\n      setServers(updatedServers);\n      message.success(\n        `${server.name} 监控已${server.monitoring_enabled ? '停止' : '启动'}`\n      );\n    } catch (error) {\n      message.error('切换监控状态失败');\n    }\n  };\n\n  const handleExecuteCommand = (server: Server) => {\n    setSelectedServer(server);\n    commandForm.resetFields();\n    setCommandModalVisible(true);\n  };\n\n  const handleCommandOk = async () => {\n    try {\n      const values = await commandForm.validateFields();\n      const result = await serverApi.executeCommand(selectedServer!.id, values);\n\n      Modal.info({\n        title: '命令执行结果',\n        content: (\n          <div>\n            <p><strong>退出码:</strong> {(result as any).exit_code}</p>\n            <p><strong>执行时间:</strong> {(result as any).duration}秒</p>\n            <div style={{ marginTop: 16 }}>\n              <strong>输出:</strong>\n              <pre style={{\n                background: '#f5f5f5',\n                padding: 8,\n                borderRadius: 4,\n                maxHeight: 200,\n                overflow: 'auto'\n              }}>\n                {(result as any).stdout}\n              </pre>\n            </div>\n            {(result as any).stderr && (\n              <div style={{ marginTop: 16 }}>\n                <strong>错误:</strong>\n                <pre style={{\n                  background: '#fff2f0',\n                  padding: 8,\n                  borderRadius: 4,\n                  maxHeight: 200,\n                  overflow: 'auto',\n                  color: 'red'\n                }}>\n                  {(result as any).stderr}\n                </pre>\n              </div>\n            )}\n          </div>\n        ),\n        width: 800,\n      });\n\n      setCommandModalVisible(false);\n      commandForm.resetFields();\n    } catch (error) {\n      message.error('命令执行失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingServer) {\n        // 更新服务器\n        await serverApi.updateServer(editingServer.id, values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器更新成功');\n      } else {\n        // 创建新服务器\n        await serverApi.createServer(values);\n        await loadServers(); // 重新加载列表\n        message.success('服务器添加成功');\n      }\n      \n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error(editingServer ? '更新服务器失败' : '添加服务器失败');\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n    form.resetFields();\n  };\n\n  const onlineCount = servers.filter(s => s.status === 'online').length;\n  const totalCount = servers.length;\n  const monitoringCount = servers.filter(s => s.monitoring_enabled).length;\n\n  return (\n    <div>\n      <Row gutter={16} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总服务器数\"\n              value={totalCount}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"在线服务器\"\n              value={onlineCount}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"监控中\"\n              value={monitoringCount}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"在线率\"\n              value={totalCount > 0 ? Math.round((onlineCount / totalCount) * 100) : 0}\n              suffix=\"%\"\n              valueStyle={{ color: onlineCount / totalCount > 0.8 ? '#52c41a' : '#ff4d4f' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card\n        title=\"服务器列表\"\n        extra={\n          <Space>\n            <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>\n              刷新\n            </Button>\n            <Button type=\"primary\" icon={<PlusOutlined />} onClick={handleAddServer}>\n              添加服务器\n            </Button>\n          </Space>\n        }\n      >\n        <Table\n          columns={columns}\n          dataSource={servers}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingServer ? '编辑服务器' : '添加服务器'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            port: 22,\n            auth_type: 'password',\n            monitoring_enabled: true,\n            alert_enabled: true,\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                label=\"服务器名称\"\n                name=\"name\"\n                rules={[{ required: true, message: '请输入服务器名称' }]}\n              >\n                <Input placeholder=\"请输入服务器名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label=\"服务器地址\"\n                name=\"host\"\n                rules={[{ required: true, message: '请输入服务器地址' }]}\n              >\n                <Input placeholder=\"请输入IP地址或域名\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                label=\"端口\"\n                name=\"port\"\n                rules={[{ required: true, message: '请输入端口号' }]}\n              >\n                <Input type=\"number\" placeholder=\"22\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                label=\"用户名\"\n                name=\"username\"\n                rules={[{ required: true, message: '请输入用户名' }]}\n              >\n                <Input placeholder=\"root\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                label=\"认证方式\"\n                name=\"auth_type\"\n                rules={[{ required: true, message: '请选择认证方式' }]}\n              >\n                <Select>\n                  <Option value=\"password\">密码认证</Option>\n                  <Option value=\"key\">密钥认证</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            label=\"描述\"\n            name=\"description\"\n          >\n            <Input.TextArea rows={3} placeholder=\"请输入服务器描述\" />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                label=\"启用监控\"\n                name=\"monitoring_enabled\"\n                valuePropName=\"checked\"\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                label=\"启用告警\"\n                name=\"alert_enabled\"\n                valuePropName=\"checked\"\n              >\n                <Switch />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n\n      <Modal\n        title={`执行命令 - ${selectedServer?.name}`}\n        open={commandModalVisible}\n        onOk={handleCommandOk}\n        onCancel={() => setCommandModalVisible(false)}\n        width={800}\n      >\n        <Form\n          form={commandForm}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            label=\"命令\"\n            name=\"command\"\n            rules={[{ required: true, message: '请输入要执行的命令' }]}\n          >\n            <Input.TextArea\n              rows={3}\n              placeholder=\"例如: ls -la\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            label=\"工作目录\"\n            name=\"working_dir\"\n          >\n            <Input placeholder=\"例如: /home/<USER>\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"环境变量\"\n            name=\"env_vars\"\n          >\n            <Input.TextArea\n              rows={2}\n              placeholder=\"例如: PATH=/usr/bin:$PATH\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            label=\"超时时间(秒)\"\n            name=\"timeout\"\n            initialValue={300}\n          >\n            <Input type=\"number\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ServerManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,QAGJ,MAAM;AACb,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,WAAW,QACN,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAoB3B,MAAM;EAAEC;AAAO,CAAC,GAAGpB,MAAM;AAEzB,MAAMqB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAW,EAAE,CAAC;EACpD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM,CAAC4C,IAAI,CAAC,GAAGrC,IAAI,CAACsC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,CAAC,GAAGvC,IAAI,CAACsC,OAAO,CAAC,CAAC;;EAEpC;EACA,MAAME,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChBa,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAE3B,MAAMC,IAAI,GAAG,MAAMjC,SAAS,CAACkC,UAAU,CAAC,CAAC;MACzCH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,IAAI,CAAC;MAE7B,IAAIA,IAAI,KAAKE,SAAS,IAAIF,IAAI,KAAK,IAAI,EAAE;QACvCF,OAAO,CAACK,IAAI,CAAC,iBAAiB,CAAC;QAC/B;QACA,MAAMC,QAAQ,GAAG,CACf;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,eAAe;UACrBC,IAAI,EAAE,EAAE;UACRC,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE,UAAU;UACrBC,WAAW,EAAE,aAAa;UAC1BC,IAAI,EAAE,CAAC,IAAI,CAAC;UACZC,kBAAkB,EAAE,IAAI;UACxBC,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCC,UAAU,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACrC,CAAC,CACF;QACDnC,UAAU,CAACqB,QAAQ,CAACgB,GAAG,CAAEC,MAAW,KAAM;UACxC,GAAGA,MAAM;UACTC,MAAM,EAAE,SAAS,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC;QACJ7D,OAAO,CAAC8D,OAAO,CAAC,kBAAkB,CAAC;MACrC,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACzB,IAAI,CAAC,EAAE;QAC9BF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,IAAI,CAAC0B,MAAM,CAAC;QACzC3C,UAAU,CAACiB,IAAI,CAACoB,GAAG,CAAEC,MAAW,KAAM;UACpC,GAAGA,MAAM;UACTC,MAAM,EAAEK,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAG,SAAS,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,MAAM;QACL9B,OAAO,CAAC+B,KAAK,CAAC,YAAY,EAAE7B,IAAI,CAAC;QACjCjB,UAAU,CAAC,EAAE,CAAC;QACdtB,OAAO,CAACoE,KAAK,CAAC,WAAW,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClCpE,OAAO,CAACoE,KAAK,CAAC,cAAcA,KAAK,YAAYC,KAAK,GAAGD,KAAK,CAACpE,OAAO,GAAG,MAAM,EAAE,CAAC;MAC9EsB,UAAU,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlC,SAAS,CAAC,MAAM;IACd8C,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkC,OAA4B,GAAG,CACnC;IACEC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB7D,OAAA,CAACtB,KAAK;MAAAoF,QAAA,gBACJ9D,OAAA;QAAA8D,QAAA,EAAOF;MAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACnBlE,OAAA,CAACrB,GAAG;QAACwF,KAAK,EAAEN,MAAM,CAACf,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAGe,MAAM,CAACf,MAAM,KAAK,SAAS,GAAG,KAAK,GAAG,QAAS;QAAAgB,QAAA,EAC/FD,MAAM,CAACf,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAGe,MAAM,CAACf,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG;MAAK;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,KAAK,GAAGD,IAAI,IAAIC,MAAM,CAAC7B,IAAI;EAClD,CAAC,EACD;IACEwB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGC,IAAI,iBACX5D,OAAA,CAACrB,GAAG;MAACwF,KAAK,EAAEP,IAAI,KAAK,UAAU,GAAG,MAAM,GAAG,OAAQ;MAAAE,QAAA,EAChDF,IAAI,KAAK,UAAU,GAAG,IAAI,GAAG;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAET,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGvB,IAAc,iBACrBpC,OAAA,CAAAE,SAAA;MAAA4D,QAAA,EACG1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,GAAG,CAAEwB,GAAG,iBACbpE,OAAA,CAACrB,GAAG;QAAWwF,KAAK,EAAC,MAAM;QAAAL,QAAA,EACxBM;MAAG,GADIA,GAAG;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAER,CACN;IAAC,gBACF;EAEN,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACU,CAAC,EAAER,MAAM,kBAChB7D,OAAA,CAACtB,KAAK;MAAAoF,QAAA,gBACJ9D,OAAA,CAACrB,GAAG;QAACwF,KAAK,EAAEN,MAAM,CAACxB,kBAAkB,GAAG,OAAO,GAAG,SAAU;QAAAyB,QAAA,EACzDD,MAAM,CAACxB,kBAAkB,GAAG,KAAK,GAAG;MAAK;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACNlE,OAAA,CAACrB,GAAG;QAACwF,KAAK,EAAEN,MAAM,CAACvB,aAAa,GAAG,QAAQ,GAAG,SAAU;QAAAwB,QAAA,EACrDD,MAAM,CAACvB,aAAa,GAAG,MAAM,GAAG;MAAM;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACU,CAAC,EAAER,MAAM,kBAChB7D,OAAA,CAACtB,KAAK;MAAC4F,IAAI,EAAC,QAAQ;MAAAR,QAAA,gBAClB9D,OAAA,CAACvB,MAAM;QACL8F,IAAI,EAAC,MAAM;QACXC,IAAI,eAAExE,OAAA,CAACF,WAAW;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBO,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAACb,MAAM,CAAE;QAAAC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA,CAACvB,MAAM;QACL8F,IAAI,EAAC,MAAM;QACXC,IAAI,eAAExE,OAAA,CAACP,YAAY;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBO,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAACd,MAAM,CAAE;QAAAC,QAAA,EACzC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA,CAACvB,MAAM;QACL8F,IAAI,EAAC,MAAM;QACXC,IAAI,EAAEX,MAAM,CAACxB,kBAAkB,gBAAGrC,OAAA,CAACJ,YAAY;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlE,OAAA,CAACL,kBAAkB;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5EO,OAAO,EAAEA,CAAA,KAAMG,sBAAsB,CAACf,MAAM,CAAE;QAAAC,QAAA,EAE7CD,MAAM,CAACxB,kBAAkB,GAAG,MAAM,GAAG;MAAM;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACTlE,OAAA,CAACvB,MAAM;QACL8F,IAAI,EAAC,MAAM;QACXE,OAAO,EAAEA,CAAA,KAAMI,oBAAoB,CAAChB,MAAM,CAAE;QAAAC,QAAA,EAC7C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA,CAACd,UAAU;QACTsE,KAAK,EAAC,0EAAc;QACpBsB,SAAS,EAAEA,CAAA,KAAMC,kBAAkB,CAAClB,MAAM,CAAChC,EAAE,CAAE;QAC/CmD,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAnB,QAAA,eAEf9D,OAAA,CAACvB,MAAM;UAAC8F,IAAI,EAAC,MAAM;UAACW,MAAM;UAACV,IAAI,eAAExE,OAAA,CAACN,cAAc;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMiB,eAAe,GAAGA,CAAA,KAAM;IAC5BtE,gBAAgB,CAAC,IAAI,CAAC;IACtBK,IAAI,CAACkE,WAAW,CAAC,CAAC;IAClBzE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgE,gBAAgB,GAAI9B,MAAc,IAAK;IAC3ChC,gBAAgB,CAACgC,MAAM,CAAC;IACxB3B,IAAI,CAACmE,cAAc,CAACxC,MAAM,CAAC;IAC3BlC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+D,gBAAgB,GAAG,MAAO7B,MAAc,IAAK;IACjD,IAAI;MAAA,IAAAyC,YAAA;MACF;MACA,MAAMC,gBAAgB,GAAG,MAAMhG,SAAS,CAACiG,cAAc,CAAC3C,MAAM,CAAChB,EAAE,CAAC;MAElEjD,KAAK,CAAC6G,IAAI,CAAC;QACTjC,KAAK,EAAE,WAAWX,MAAM,CAACf,IAAI,EAAE;QAC/B4D,OAAO,eACL1F,OAAA;UAAA8D,QAAA,gBACE9D,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrB,MAAM,CAACd,IAAI,EAAC,GAAC,EAACc,MAAM,CAACb,IAAI;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDlE,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrB,MAAM,CAACZ,QAAQ;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ClE,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrB,MAAM,CAACX,SAAS;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDlE,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACrB,MAAM,CAACV,WAAW;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDlE,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,GAAAoB,YAAA,GAACzC,MAAM,CAACT,IAAI,cAAAkD,YAAA,uBAAXA,YAAA,CAAaK,IAAI,CAAC,IAAI,CAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDlE,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvBlE,OAAA,CAACrB,GAAG;cAACwF,KAAK,EAAGoB,gBAAgB,CAASK,OAAO,GAAG,OAAO,GAAG,KAAM;cAAA9B,QAAA,EAC5DyB,gBAAgB,CAASK,OAAO,GAAG,MAAM,GAAG;YAAM;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EACFqB,gBAAgB,CAASK,OAAO,iBAChC5F,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAEqB,gBAAgB,CAASM,OAAO,EAAC,IAAE;UAAA;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACjE,eACDlE,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIzB,IAAI,CAACI,MAAM,CAACL,UAAU,CAAC,CAACsD,cAAc,CAAC,CAAC;UAAA;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CACN;QACD6B,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,WAAW,CAAC;IAC5B;EACF,CAAC;EAED,MAAM0B,kBAAkB,GAAG,MAAOlD,EAAU,IAAK;IAC/C,IAAI;MACF,MAAMtC,SAAS,CAACyG,YAAY,CAACnE,EAAE,CAAC;MAChCtB,UAAU,CAACD,OAAO,CAAC2F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrE,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC5C5C,OAAO,CAAC2G,OAAO,CAAC,SAAS,CAAC;IAC5B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,SAAS,CAAC;IAC1B;EACF,CAAC;EAED,MAAMuB,sBAAsB,GAAG,MAAO/B,MAAc,IAAK;IACvD,IAAI;MACF,IAAIA,MAAM,CAACR,kBAAkB,EAAE;QAC7B,MAAM9C,SAAS,CAAC4G,cAAc,CAACtD,MAAM,CAAChB,EAAE,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMtC,SAAS,CAAC6G,eAAe,CAACvD,MAAM,CAAChB,EAAE,CAAC;MAC5C;MAEA,MAAMwE,cAAc,GAAG,CAAC/F,OAAO,IAAI,EAAE,EAAEsC,GAAG,CAACsD,CAAC,IAC1CA,CAAC,CAACrE,EAAE,KAAKgB,MAAM,CAAChB,EAAE,GACd;QAAE,GAAGqE,CAAC;QAAE7D,kBAAkB,EAAE,CAAC6D,CAAC,CAAC7D;MAAmB,CAAC,GACnD6D,CACN,CAAC;MACD3F,UAAU,CAAC8F,cAAc,CAAC;MAC1BpH,OAAO,CAAC2G,OAAO,CACb,GAAG/C,MAAM,CAACf,IAAI,OAAOe,MAAM,CAACR,kBAAkB,GAAG,IAAI,GAAG,IAAI,EAC9D,CAAC;IACH,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMwB,oBAAoB,GAAIhC,MAAc,IAAK;IAC/C5B,iBAAiB,CAAC4B,MAAM,CAAC;IACzBzB,WAAW,CAACgE,WAAW,CAAC,CAAC;IACzBrE,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMuF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMnF,WAAW,CAACoF,cAAc,CAAC,CAAC;MACjD,MAAMC,MAAM,GAAG,MAAMlH,SAAS,CAACmH,cAAc,CAAC1F,cAAc,CAAEa,EAAE,EAAE0E,MAAM,CAAC;MAEzE3H,KAAK,CAAC6G,IAAI,CAAC;QACTjC,KAAK,EAAE,QAAQ;QACfkC,OAAO,eACL1F,OAAA;UAAA8D,QAAA,gBACE9D,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAEuC,MAAM,CAASE,SAAS;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDlE,OAAA;YAAA8D,QAAA,gBAAG9D,OAAA;cAAA8D,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAEuC,MAAM,CAASG,QAAQ,EAAC,QAAC;UAAA;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzDlE,OAAA;YAAK6G,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAG,CAAE;YAAAhD,QAAA,gBAC5B9D,OAAA;cAAA8D,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpBlE,OAAA;cAAK6G,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,CAAC;gBACVC,YAAY,EAAE,CAAC;gBACfC,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE;cACZ,CAAE;cAAArD,QAAA,EACE2C,MAAM,CAASW;YAAM;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACJuC,MAAM,CAASY,MAAM,iBACrBrH,OAAA;YAAK6G,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAG,CAAE;YAAAhD,QAAA,gBAC5B9D,OAAA;cAAA8D,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpBlE,OAAA;cAAK6G,KAAK,EAAE;gBACVE,UAAU,EAAE,SAAS;gBACrBC,OAAO,EAAE,CAAC;gBACVC,YAAY,EAAE,CAAC;gBACfC,SAAS,EAAE,GAAG;gBACdC,QAAQ,EAAE,MAAM;gBAChBhD,KAAK,EAAE;cACT,CAAE;cAAAL,QAAA,EACE2C,MAAM,CAASY;YAAM;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;QACD6B,KAAK,EAAE;MACT,CAAC,CAAC;MAEFhF,sBAAsB,CAAC,KAAK,CAAC;MAC7BK,WAAW,CAACgE,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAMiE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMf,MAAM,GAAG,MAAMrF,IAAI,CAACsF,cAAc,CAAC,CAAC;MAE1C,IAAI5F,aAAa,EAAE;QACjB;QACA,MAAMrB,SAAS,CAACgI,YAAY,CAAC3G,aAAa,CAACiB,EAAE,EAAE0E,MAAM,CAAC;QACtD,MAAMlF,WAAW,CAAC,CAAC,CAAC,CAAC;QACrBpC,OAAO,CAAC2G,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAC,MAAM;QACL;QACA,MAAMrG,SAAS,CAACiI,YAAY,CAACjB,MAAM,CAAC;QACpC,MAAMlF,WAAW,CAAC,CAAC,CAAC,CAAC;QACrBpC,OAAO,CAAC2G,OAAO,CAAC,SAAS,CAAC;MAC5B;MAEAjF,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAACkE,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpE,OAAO,CAACoE,KAAK,CAACzC,aAAa,GAAG,SAAS,GAAG,SAAS,CAAC;IACtD;EACF,CAAC;EAED,MAAM6G,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9G,eAAe,CAAC,KAAK,CAAC;IACtBO,IAAI,CAACkE,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,MAAMsC,WAAW,GAAGpH,OAAO,CAAC2F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpD,MAAM,KAAK,QAAQ,CAAC,CAACI,MAAM;EACrE,MAAMyE,UAAU,GAAGrH,OAAO,CAAC4C,MAAM;EACjC,MAAM0E,eAAe,GAAGtH,OAAO,CAAC2F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7D,kBAAkB,CAAC,CAACa,MAAM;EAExE,oBACElD,OAAA;IAAA8D,QAAA,gBACE9D,OAAA,CAACZ,GAAG;MAACyI,MAAM,EAAE,EAAG;MAAChB,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAhE,QAAA,gBAC3C9D,OAAA,CAACX,GAAG;QAAC0I,IAAI,EAAE,CAAE;QAAAjE,QAAA,eACX9D,OAAA,CAACb,IAAI;UAAA2E,QAAA,eACH9D,OAAA,CAACV,SAAS;YACRkE,KAAK,EAAC,gCAAO;YACbwE,KAAK,EAAEL,UAAW;YAClBM,UAAU,EAAE;cAAE9D,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAACX,GAAG;QAAC0I,IAAI,EAAE,CAAE;QAAAjE,QAAA,eACX9D,OAAA,CAACb,IAAI;UAAA2E,QAAA,eACH9D,OAAA,CAACV,SAAS;YACRkE,KAAK,EAAC,gCAAO;YACbwE,KAAK,EAAEN,WAAY;YACnBO,UAAU,EAAE;cAAE9D,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAACX,GAAG;QAAC0I,IAAI,EAAE,CAAE;QAAAjE,QAAA,eACX9D,OAAA,CAACb,IAAI;UAAA2E,QAAA,eACH9D,OAAA,CAACV,SAAS;YACRkE,KAAK,EAAC,oBAAK;YACXwE,KAAK,EAAEJ,eAAgB;YACvBK,UAAU,EAAE;cAAE9D,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAACX,GAAG;QAAC0I,IAAI,EAAE,CAAE;QAAAjE,QAAA,eACX9D,OAAA,CAACb,IAAI;UAAA2E,QAAA,eACH9D,OAAA,CAACV,SAAS;YACRkE,KAAK,EAAC,oBAAK;YACXwE,KAAK,EAAEL,UAAU,GAAG,CAAC,GAAGxE,IAAI,CAAC+E,KAAK,CAAER,WAAW,GAAGC,UAAU,GAAI,GAAG,CAAC,GAAG,CAAE;YACzEQ,MAAM,EAAC,GAAG;YACVF,UAAU,EAAE;cAAE9D,KAAK,EAAEuD,WAAW,GAAGC,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG;YAAU;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlE,OAAA,CAACb,IAAI;MACHqE,KAAK,EAAC,gCAAO;MACb4E,KAAK,eACHpI,OAAA,CAACtB,KAAK;QAAAoF,QAAA,gBACJ9D,OAAA,CAACvB,MAAM;UAAC+F,IAAI,eAAExE,OAAA,CAACH,cAAc;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,OAAO,EAAEA,CAAA,KAAMhE,UAAU,CAAC,IAAI,CAAE;UAAAqD,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlE,OAAA,CAACvB,MAAM;UAAC8F,IAAI,EAAC,SAAS;UAACC,IAAI,eAAExE,OAAA,CAACR,YAAY;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,OAAO,EAAEU,eAAgB;UAAArB,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAJ,QAAA,eAED9D,OAAA,CAACxB,KAAK;QACJ+E,OAAO,EAAEA,OAAQ;QACjB8E,UAAU,EAAE/H,OAAQ;QACpBgI,MAAM,EAAC,IAAI;QACX9H,OAAO,EAAEA,OAAQ;QACjB+H,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C;MAAE;QAAA5E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPlE,OAAA,CAACpB,KAAK;MACJ4E,KAAK,EAAE5C,aAAa,GAAG,OAAO,GAAG,OAAQ;MACzCiI,IAAI,EAAEnI,YAAa;MACnBoI,IAAI,EAAExB,aAAc;MACpByB,QAAQ,EAAEtB,iBAAkB;MAC5B1B,KAAK,EAAE,GAAI;MAAAjC,QAAA,eAEX9D,OAAA,CAACnB,IAAI;QACHqC,IAAI,EAAEA,IAAK;QACX8H,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbjH,IAAI,EAAE,EAAE;UACRE,SAAS,EAAE,UAAU;UACrBG,kBAAkB,EAAE,IAAI;UACxBC,aAAa,EAAE;QACjB,CAAE;QAAAwB,QAAA,gBAEF9D,OAAA,CAACZ,GAAG;UAACyI,MAAM,EAAE,EAAG;UAAA/D,QAAA,gBACd9D,OAAA,CAACX,GAAG;YAAC0I,IAAI,EAAE,EAAG;YAAAjE,QAAA,eACZ9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbrH,IAAI,EAAC,MAAM;cACXsH,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpK,OAAO,EAAE;cAAW,CAAC,CAAE;cAAA6E,QAAA,eAEjD9D,OAAA,CAAClB,KAAK;gBAACwK,WAAW,EAAC;cAAU;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACX,GAAG;YAAC0I,IAAI,EAAE,EAAG;YAAAjE,QAAA,eACZ9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;cACRC,KAAK,EAAC,gCAAO;cACbrH,IAAI,EAAC,MAAM;cACXsH,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpK,OAAO,EAAE;cAAW,CAAC,CAAE;cAAA6E,QAAA,eAEjD9D,OAAA,CAAClB,KAAK;gBAACwK,WAAW,EAAC;cAAY;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlE,OAAA,CAACZ,GAAG;UAACyI,MAAM,EAAE,EAAG;UAAA/D,QAAA,gBACd9D,OAAA,CAACX,GAAG;YAAC0I,IAAI,EAAE,CAAE;YAAAjE,QAAA,eACX9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;cACRC,KAAK,EAAC,cAAI;cACVrH,IAAI,EAAC,MAAM;cACXsH,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpK,OAAO,EAAE;cAAS,CAAC,CAAE;cAAA6E,QAAA,eAE/C9D,OAAA,CAAClB,KAAK;gBAACyF,IAAI,EAAC,QAAQ;gBAAC+E,WAAW,EAAC;cAAI;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACX,GAAG;YAAC0I,IAAI,EAAE,CAAE;YAAAjE,QAAA,eACX9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXrH,IAAI,EAAC,UAAU;cACfsH,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpK,OAAO,EAAE;cAAS,CAAC,CAAE;cAAA6E,QAAA,eAE/C9D,OAAA,CAAClB,KAAK;gBAACwK,WAAW,EAAC;cAAM;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACX,GAAG;YAAC0I,IAAI,EAAE,CAAE;YAAAjE,QAAA,eACX9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZrH,IAAI,EAAC,WAAW;cAChBsH,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6E,QAAA,eAEhD9D,OAAA,CAACjB,MAAM;gBAAA+E,QAAA,gBACL9D,OAAA,CAACG,MAAM;kBAAC6H,KAAK,EAAC,UAAU;kBAAAlE,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtClE,OAAA,CAACG,MAAM;kBAAC6H,KAAK,EAAC,KAAK;kBAAAlE,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlE,OAAA,CAACnB,IAAI,CAACqK,IAAI;UACRC,KAAK,EAAC,cAAI;UACVrH,IAAI,EAAC,aAAa;UAAAgC,QAAA,eAElB9D,OAAA,CAAClB,KAAK,CAACyK,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACF,WAAW,EAAC;UAAU;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAEZlE,OAAA,CAACZ,GAAG;UAACyI,MAAM,EAAE,EAAG;UAAA/D,QAAA,gBACd9D,OAAA,CAACX,GAAG;YAAC0I,IAAI,EAAE,EAAG;YAAAjE,QAAA,eACZ9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZrH,IAAI,EAAC,oBAAoB;cACzB2H,aAAa,EAAC,SAAS;cAAA3F,QAAA,eAEvB9D,OAAA,CAAChB,MAAM;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlE,OAAA,CAACX,GAAG;YAAC0I,IAAI,EAAE,EAAG;YAAAjE,QAAA,eACZ9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZrH,IAAI,EAAC,eAAe;cACpB2H,aAAa,EAAC,SAAS;cAAA3F,QAAA,eAEvB9D,OAAA,CAAChB,MAAM;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAERlE,OAAA,CAACpB,KAAK;MACJ4E,KAAK,EAAE,UAAUxC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEc,IAAI,EAAG;MACxC+G,IAAI,EAAE/H,mBAAoB;MAC1BgI,IAAI,EAAExC,eAAgB;MACtByC,QAAQ,EAAEA,CAAA,KAAMhI,sBAAsB,CAAC,KAAK,CAAE;MAC9CgF,KAAK,EAAE,GAAI;MAAAjC,QAAA,eAEX9D,OAAA,CAACnB,IAAI;QACHqC,IAAI,EAAEE,WAAY;QAClB4H,MAAM,EAAC,UAAU;QAAAlF,QAAA,gBAEjB9D,OAAA,CAACnB,IAAI,CAACqK,IAAI;UACRC,KAAK,EAAC,cAAI;UACVrH,IAAI,EAAC,SAAS;UACdsH,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEpK,OAAO,EAAE;UAAY,CAAC,CAAE;UAAA6E,QAAA,eAElD9D,OAAA,CAAClB,KAAK,CAACyK,QAAQ;YACbC,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAY;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlE,OAAA,CAACnB,IAAI,CAACqK,IAAI;UACRC,KAAK,EAAC,0BAAM;UACZrH,IAAI,EAAC,aAAa;UAAAgC,QAAA,eAElB9D,OAAA,CAAClB,KAAK;YAACwK,WAAW,EAAC;UAAgB;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEZlE,OAAA,CAACnB,IAAI,CAACqK,IAAI;UACRC,KAAK,EAAC,0BAAM;UACZrH,IAAI,EAAC,UAAU;UAAAgC,QAAA,eAEf9D,OAAA,CAAClB,KAAK,CAACyK,QAAQ;YACbC,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAyB;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlE,OAAA,CAACnB,IAAI,CAACqK,IAAI;UACRC,KAAK,EAAC,kCAAS;UACfrH,IAAI,EAAC,SAAS;UACd4H,YAAY,EAAE,GAAI;UAAA5F,QAAA,eAElB9D,OAAA,CAAClB,KAAK;YAACyF,IAAI,EAAC;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAnjBID,gBAA0B;EAAA,QAOfvB,IAAI,CAACsC,OAAO,EACLtC,IAAI,CAACsC,OAAO;AAAA;AAAAwI,EAAA,GAR9BvJ,gBAA0B;AAqjBhC,eAAeA,gBAAgB;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}