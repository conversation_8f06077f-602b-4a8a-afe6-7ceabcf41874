{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TwitterSquareFilledSvg from \"@ant-design/icons-svg/es/asn/TwitterSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TwitterSquareFilled = function TwitterSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TwitterSquareFilledSvg\n  }));\n};\n\n/**![twitter-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzI3LjMgNDAxLjdjLjMgNC43LjMgOS42LjMgMTQuNCAwIDE0Ni44LTExMS44IDMxNS45LTMxNi4xIDMxNS45LTYzIDAtMTIxLjQtMTguMy0xNzAuNi00OS44IDkgMSAxNy42IDEuNCAyNi44IDEuNCA1MiAwIDk5LjgtMTcuNiAxMzcuOS00Ny40LTQ4LjgtMS04OS44LTMzLTEwMy44LTc3IDE3LjEgMi41IDMyLjUgMi41IDUwLjEtMmExMTEgMTExIDAgMDEtODguOS0xMDl2LTEuNGMxNC43IDguMyAzMiAxMy40IDUwLjEgMTQuMWExMTEuMTMgMTExLjEzIDAgMDEtNDkuNS05Mi40YzAtMjAuNyA1LjQtMzkuNiAxNS4xLTU2YTMxNS4yOCAzMTUuMjggMCAwMDIyOSAxMTYuMUM0OTIgMzUzLjEgNTQ4LjQgMjkyIDYxNi4yIDI5MmMzMiAwIDYwLjggMTMuNCA4MS4xIDM1IDI1LjEtNC43IDQ5LjEtMTQuMSA3MC41LTI2LjctOC4zIDI1LjctMjUuNyA0Ny40LTQ4LjggNjEuMSAyMi40LTIuNCA0NC04LjYgNjQtMTcuMy0xNS4xIDIyLjItMzQgNDEuOS01NS43IDU3LjZ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TwitterSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TwitterSquareFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TwitterSquareFilledSvg", "AntdIcon", "TwitterSquareFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["/home/<USER>/itai/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/TwitterSquareFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TwitterSquareFilledSvg from \"@ant-design/icons-svg/es/asn/TwitterSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TwitterSquareFilled = function TwitterSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TwitterSquareFilledSvg\n  }));\n};\n\n/**![twitter-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNzI3LjMgNDAxLjdjLjMgNC43LjMgOS42LjMgMTQuNCAwIDE0Ni44LTExMS44IDMxNS45LTMxNi4xIDMxNS45LTYzIDAtMTIxLjQtMTguMy0xNzAuNi00OS44IDkgMSAxNy42IDEuNCAyNi44IDEuNCA1MiAwIDk5LjgtMTcuNiAxMzcuOS00Ny40LTQ4LjgtMS04OS44LTMzLTEwMy44LTc3IDE3LjEgMi41IDMyLjUgMi41IDUwLjEtMmExMTEgMTExIDAgMDEtODguOS0xMDl2LTEuNGMxNC43IDguMyAzMiAxMy40IDUwLjEgMTQuMWExMTEuMTMgMTExLjEzIDAgMDEtNDkuNS05Mi40YzAtMjAuNyA1LjQtMzkuNiAxNS4xLTU2YTMxNS4yOCAzMTUuMjggMCAwMDIyOSAxMTYuMUM0OTIgMzUzLjEgNTQ4LjQgMjkyIDYxNi4yIDI5MmMzMiAwIDYwLjggMTMuNCA4MS4xIDM1IDI1LjEtNC43IDQ5LjEtMTQuMSA3MC41LTI2LjctOC4zIDI1LjctMjUuNyA0Ny40LTQ4LjggNjEuMSAyMi40LTIuNCA0NC04LjYgNjQtMTcuMy0xNS4xIDIyLjItMzQgNDEuOS01NS43IDU3LjZ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TwitterSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TwitterSquareFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,mBAAmB,CAAC;AAChE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,qBAAqB;AAC7C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}