{"ast": null, "code": "export default function (constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}", "map": {"version": 3, "names": ["constructor", "factory", "prototype", "extend", "parent", "definition", "Object", "create", "key"], "sources": ["/home/<USER>/itai/frontend/node_modules/d3-color/src/define.js"], "sourcesContent": ["export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n"], "mappings": "AAAA,eAAe,UAASA,WAAW,EAAEC,OAAO,EAAEC,SAAS,EAAE;EACvDF,WAAW,CAACE,SAAS,GAAGD,OAAO,CAACC,SAAS,GAAGA,SAAS;EACrDA,SAAS,CAACF,WAAW,GAAGA,WAAW;AACrC;AAEA,OAAO,SAASG,MAAMA,CAACC,MAAM,EAAEC,UAAU,EAAE;EACzC,IAAIH,SAAS,GAAGI,MAAM,CAACC,MAAM,CAACH,MAAM,CAACF,SAAS,CAAC;EAC/C,KAAK,IAAIM,GAAG,IAAIH,UAAU,EAAEH,SAAS,CAACM,GAAG,CAAC,GAAGH,UAAU,CAACG,GAAG,CAAC;EAC5D,OAAON,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}