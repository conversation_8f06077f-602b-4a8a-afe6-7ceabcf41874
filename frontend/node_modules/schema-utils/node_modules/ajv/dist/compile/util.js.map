{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../lib/compile/util.ts"], "names": [], "mappings": ";;;AAEA,uCAA6D;AAC7D,yCAAoC;AAGpC,2BAA2B;AAC3B,SAAgB,MAAM,CAA4B,GAAQ;IACxD,MAAM,IAAI,GAAsB,EAAE,CAAA;IAClC,KAAK,MAAM,IAAI,IAAI,GAAG;QAAE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;IACzC,OAAO,IAAI,CAAA;AACb,CAAC;AAJD,wBAIC;AAED,SAAgB,iBAAiB,CAAC,EAAa,EAAE,MAAiB;IAChE,IAAI,OAAO,MAAM,IAAI,SAAS;QAAE,OAAO,MAAM,CAAA;IAC7C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,IAAI,CAAA;IACjD,iBAAiB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;IAC7B,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AACnD,CAAC;AALD,8CAKC;AAED,SAAgB,iBAAiB,CAAC,EAAa,EAAE,SAAoB,EAAE,CAAC,MAAM;IAC5E,MAAM,EAAC,IAAI,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;IACvB,IAAI,CAAC,IAAI,CAAC,YAAY;QAAE,OAAM;IAC9B,IAAI,OAAO,MAAM,KAAK,SAAS;QAAE,OAAM;IACvC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAA;IACjC,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAAE,eAAe,CAAC,EAAE,EAAE,qBAAqB,GAAG,GAAG,CAAC,CAAA;IACnE,CAAC;AACH,CAAC;AARD,8CAQC;AAED,SAAgB,cAAc,CAC5B,MAAiB,EACjB,KAAyC;IAEzC,IAAI,OAAO,MAAM,IAAI,SAAS;QAAE,OAAO,CAAC,MAAM,CAAA;IAC9C,KAAK,MAAM,GAAG,IAAI,MAAM;QAAE,IAAI,KAAK,CAAC,GAAG,CAAC;YAAE,OAAO,IAAI,CAAA;IACrD,OAAO,KAAK,CAAA;AACd,CAAC;AAPD,wCAOC;AAED,SAAgB,oBAAoB,CAAC,MAAiB,EAAE,KAAsB;IAC5E,IAAI,OAAO,MAAM,IAAI,SAAS;QAAE,OAAO,CAAC,MAAM,CAAA;IAC9C,KAAK,MAAM,GAAG,IAAI,MAAM;QAAE,IAAI,GAAG,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;YAAE,OAAO,IAAI,CAAA;IAC3E,OAAO,KAAK,CAAA;AACd,CAAC;AAJD,oDAIC;AAED,SAAgB,cAAc,CAC5B,EAAC,YAAY,EAAE,UAAU,EAAe,EACxC,MAAe,EACf,OAAe,EACf,KAAsB;IAEtB,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,IAAI,OAAO,MAAM,IAAI,QAAQ,IAAI,OAAO,MAAM,IAAI,SAAS;YAAE,OAAO,MAAM,CAAA;QAC1E,IAAI,OAAO,MAAM,IAAI,QAAQ;YAAE,OAAO,IAAA,WAAC,EAAA,GAAG,MAAM,EAAE,CAAA;IACpD,CAAC;IACD,OAAO,IAAA,WAAC,EAAA,GAAG,YAAY,GAAG,UAAU,GAAG,IAAA,qBAAW,EAAC,OAAO,CAAC,EAAE,CAAA;AAC/D,CAAC;AAXD,wCAWC;AAED,SAAgB,gBAAgB,CAAC,GAAW;IAC1C,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAA;AACrD,CAAC;AAFD,4CAEC;AAED,SAAgB,cAAc,CAAC,GAAoB;IACjD,OAAO,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAA;AACnD,CAAC;AAFD,wCAEC;AAED,SAAgB,iBAAiB,CAAC,GAAoB;IACpD,IAAI,OAAO,GAAG,IAAI,QAAQ;QAAE,OAAO,GAAG,GAAG,EAAE,CAAA;IAC3C,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACrD,CAAC;AAHD,8CAGC;AAED,SAAgB,mBAAmB,CAAC,GAAW;IAC7C,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AACpD,CAAC;AAFD,kDAEC;AAED,SAAgB,QAAQ,CAAI,EAAW,EAAE,CAAiB;IACxD,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;QACtB,KAAK,MAAM,CAAC,IAAI,EAAE;YAAE,CAAC,CAAC,CAAC,CAAC,CAAA;IAC1B,CAAC;SAAM,CAAC;QACN,CAAC,CAAC,EAAE,CAAC,CAAA;IACP,CAAC;AACH,CAAC;AAND,4BAMC;AAkBD,SAAS,kBAAkB,CAA0B,EACnD,UAAU,EACV,WAAW,EACX,WAAW,EACX,YAAY,GACS;IACrB,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;QAC/B,MAAM,GAAG,GACP,EAAE,KAAK,SAAS;YACd,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,EAAE,YAAY,cAAI;gBACpB,CAAC,CAAC,CAAC,IAAI,YAAY,cAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrF,CAAC,CAAC,IAAI,YAAY,cAAI;oBACtB,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;oBACpC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QAC3B,OAAO,MAAM,KAAK,cAAI,IAAI,CAAC,CAAC,GAAG,YAAY,cAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;IACjF,CAAC,CAAA;AACH,CAAC;AAOY,QAAA,cAAc,GAAmB;IAC5C,KAAK,EAAE,kBAAkB,CAAC;QACxB,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,CAC5B,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,EAAE,gBAAgB,IAAI,gBAAgB,EAAE,GAAG,EAAE;YACtD,GAAG,CAAC,EAAE,CACJ,IAAA,WAAC,EAAA,GAAG,IAAI,WAAW,EACnB,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,EAC1B,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAA,WAAC,EAAA,GAAG,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAA,WAAC,EAAA,iBAAiB,EAAE,KAAK,IAAI,GAAG,CAAC,CAC5E,CAAA;QACH,CAAC,CAAC;QACJ,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,CAC7B,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE;YAC7B,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;YACtB,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAA,WAAC,EAAA,GAAG,EAAE,QAAQ,CAAC,CAAA;gBAC9B,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,CAAA;YAC7B,CAAC;QACH,CAAC,CAAC;QACJ,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,GAAG,IAAI,EAAE,GAAG,EAAE,EAAC,CAAC;QACpE,YAAY,EAAE,oBAAoB;KACnC,CAAC;IACF,KAAK,EAAE,kBAAkB,CAAC;QACxB,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,CAC5B,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,EAAE,gBAAgB,IAAI,gBAAgB,EAAE,GAAG,EAAE,CACtD,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,sBAAsB,EAAE,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,EAAE,CAAC,CAC/E;QACH,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,CAC7B,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,CAC7B,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,EAAE,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,EAAE,CAAC,CAC5E;QACH,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACtE,YAAY,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;KACtD,CAAC;CACH,CAAA;AAED,SAAgB,oBAAoB,CAAC,GAAY,EAAE,EAAwB;IACzE,IAAI,EAAE,KAAK,IAAI;QAAE,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,IAAI,CAAC,CAAA;IACrC,IAAI,EAAE,KAAK,SAAS;QAAE,YAAY,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IAClD,OAAO,KAAK,CAAA;AACd,CAAC;AALD,oDAKC;AAED,SAAgB,YAAY,CAAC,GAAY,EAAE,KAAW,EAAE,EAA0B;IAChF,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAA,WAAC,EAAA,GAAG,KAAK,GAAG,IAAA,qBAAW,EAAC,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAA;AAChF,CAAC;AAFD,oCAEC;AAED,MAAM,QAAQ,GAA4B,EAAE,CAAA;AAE5C,SAAgB,OAAO,CAAC,GAAY,EAAE,CAAiB;IACrD,OAAO,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE;QAC5B,GAAG,EAAE,CAAC;QACN,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,YAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;KACjE,CAAC,CAAA;AACJ,CAAC;AALD,0BAKC;AAED,IAAY,IAGX;AAHD,WAAY,IAAI;IACd,6BAAG,CAAA;IACH,6BAAG,CAAA;AACL,CAAC,EAHW,IAAI,oBAAJ,IAAI,QAGf;AAED,SAAgB,YAAY,CAC1B,QAAgC,EAChC,YAAmB,EACnB,gBAA0B;IAE1B,WAAW;IACX,IAAI,QAAQ,YAAY,cAAI,EAAE,CAAC;QAC7B,MAAM,QAAQ,GAAG,YAAY,KAAK,IAAI,CAAC,GAAG,CAAA;QAC1C,OAAO,gBAAgB;YACrB,CAAC,CAAC,QAAQ;gBACR,CAAC,CAAC,IAAA,WAAC,EAAA,SAAS,QAAQ,QAAQ;gBAC5B,CAAC,CAAC,IAAA,WAAC,EAAA,UAAU,QAAQ,SAAS;YAChC,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,IAAA,WAAC,EAAA,SAAS,QAAQ,EAAE;gBACtB,CAAC,CAAC,IAAA,WAAC,EAAA,SAAS,QAAQ,4CAA4C,CAAA,CAAC,sCAAsC;IAC3G,CAAC;IACD,OAAO,gBAAgB,CAAC,CAAC,CAAC,IAAA,qBAAW,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAA;AAChG,CAAC;AAjBD,oCAiBC;AAED,SAAgB,eAAe,CAC7B,EAAa,EACb,GAAW,EACX,OAAwB,EAAE,CAAC,IAAI,CAAC,YAAY;IAE5C,IAAI,CAAC,IAAI;QAAE,OAAM;IACjB,GAAG,GAAG,gBAAgB,GAAG,EAAE,CAAA;IAC3B,IAAI,IAAI,KAAK,IAAI;QAAE,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACvC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAC1B,CAAC;AATD,0CASC"}