const set = require('regenerate')(0xAA, 0xB7, 0xBA, 0x2BC, 0x2C7, 0x2CD, 0x2D7, 0x2D9, 0x313, 0x320, 0x358, 0x35E, 0x10FB, 0x1DF8, 0x202F, 0x2071, 0x207F, 0x20F0, 0x2132, 0x214E, 0x2E17, 0xA7D3, 0xA92E);
set.addRange(0x41, 0x5A).addRange(0x61, 0x7A).addRange(0xC0, 0xD6).addRange(0xD8, 0xF6).addRange(0xF8, 0x2B8).addRange(0x2C9, 0x2CB).addRange(0x2E0, 0x2E4).addRange(0x300, 0x30E).addRange(0x310, 0x311).addRange(0x323, 0x325).addRange(0x32D, 0x32E).addRange(0x330, 0x331).addRange(0x363, 0x36F).addRange(0x485, 0x486).addRange(0x951, 0x952).addRange(0x1D00, 0x1D25).addRange(0x1D2C, 0x1D5C).addRange(0x1D62, 0x1D65).addRange(0x1D6B, 0x1D77).addRange(0x1D79, 0x1DBE).addRange(0x1E00, 0x1EFF).addRange(0x2090, 0x209C).addRange(0x212A, 0x212B).addRange(0x2160, 0x2188).addRange(0x2C60, 0x2C7F).addRange(0xA700, 0xA707).addRange(0xA722, 0xA787).addRange(0xA78B, 0xA7CD).addRange(0xA7D0, 0xA7D1).addRange(0xA7D5, 0xA7DC).addRange(0xA7F2, 0xA7FF).addRange(0xAB30, 0xAB5A).addRange(0xAB5C, 0xAB64).addRange(0xAB66, 0xAB69).addRange(0xFB00, 0xFB06).addRange(0xFF21, 0xFF3A).addRange(0xFF41, 0xFF5A).addRange(0x10780, 0x10785).addRange(0x10787, 0x107B0).addRange(0x107B2, 0x107BA).addRange(0x1DF00, 0x1DF1E).addRange(0x1DF25, 0x1DF2A);
exports.characters = set;
