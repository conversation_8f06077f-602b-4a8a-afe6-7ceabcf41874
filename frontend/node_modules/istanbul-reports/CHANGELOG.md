# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.1.7](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.1.6...istanbul-reports-v3.1.7) (2024-02-19)


### Bug Fixes

* **ux:** address bug with firefox event handling ([2207a87](https://github.com/istanbuljs/istanbuljs/commit/2207a87ec978b7637d8b55de2ff887e462bd48d3))

## [3.1.6](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.1.5...istanbul-reports-v3.1.6) (2023-07-25)


### Bug Fixes

* **clover:** always close last open tag ([07c6ea6](https://github.com/istanbuljs/istanbuljs/commit/07c6ea6537261a19b117702b7362dee0bdc001ac))

## [3.1.5](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.1.4...istanbul-reports-v3.1.5) (2022-07-13)


### Bug Fixes

* `new Date()` such that it works with MockDate library ([#688](https://github.com/istanbuljs/istanbuljs/issues/688)) ([85905f9](https://github.com/istanbuljs/istanbuljs/commit/85905f989c9480e63ad534c6ff8b1a12dae278eb))
* add placeholder to fix Implicit Else ([#679](https://github.com/istanbuljs/istanbuljs/issues/679)) ([0516f51](https://github.com/istanbuljs/istanbuljs/commit/0516f519575ee28f77ebf1e9556ac294d78904ea))

### [3.1.4](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.1.3...istanbul-reports-v3.1.4) (2022-01-17)


### Bug Fixes

* "E" is not showing in the HTML reporter for "implicit else" branches after pull 633 ([#663](https://github.com/istanbuljs/istanbuljs/issues/663)) ([7818922](https://github.com/istanbuljs/istanbuljs/commit/7818922fd7229c4eee12b1407b5a13020f5d34de))

### [3.1.3](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.1.2...istanbul-reports-v3.1.3) (2021-12-29)


### Bug Fixes

* reverse tabnabbing vulnerability in URLs ([#591](https://github.com/istanbuljs/istanbuljs/issues/591)) ([4eceb9e](https://github.com/istanbuljs/istanbuljs/commit/4eceb9eb8b3169b882d74ecc526fb5837ebc6205))

### [3.1.2](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.1.1...istanbul-reports-v3.1.2) (2021-12-23)


### Bug Fixes

* remove stray div tag from HTML report ([68d9c74](https://github.com/istanbuljs/istanbuljs/commit/68d9c7469927ddcf15346307eacea8fd7104086c))

### [3.1.1](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.1.0...istanbul-reports-v3.1.1) (2021-12-01)


### Bug Fixes

* rel="noopener" to the link in the generated html reports ([f234bb3](https://github.com/istanbuljs/istanbuljs/commit/f234bb321421e7312a83595934a1abf81c7af70c))

## [3.1.0](https://github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.0.5...istanbul-reports-v3.1.0) (2021-11-30)


### Features

* add filter to HTML report ([#650](https://github.com/istanbuljs/istanbuljs/issues/650)) ([eab47f7](https://github.com/istanbuljs/istanbuljs/commit/eab47f76be90343f679ef0e5567a21447a4995dc))

### [3.0.5](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.0.4...istanbul-reports-v3.0.5) (2021-10-13)


### Bug Fixes

* cobertura reports in root folder ([#571](https://www.github.com/istanbuljs/istanbuljs/issues/571)) ([596f6ff](https://www.github.com/istanbuljs/istanbuljs/commit/596f6ff1342ae4baa6688bf3ee7786c75d4df947))

### [3.0.4](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.0.3...istanbul-reports-v3.0.4) (2021-10-12)


### Bug Fixes

* handle reports with "loc" but no "decl" ([#637](https://www.github.com/istanbuljs/istanbuljs/issues/637)) ([cdc28f3](https://www.github.com/istanbuljs/istanbuljs/commit/cdc28f3a1e80e786eaab3b7d3b8b9b558fc2d3c8)), closes [#322](https://www.github.com/istanbuljs/istanbuljs/issues/322)

### [3.0.3](https://www.github.com/istanbuljs/istanbuljs/compare/istanbul-reports-v3.0.2...istanbul-reports-v3.0.3) (2021-10-06)


### Bug Fixes

* lcov reporter crash when missing branches ([#613](https://www.github.com/istanbuljs/istanbuljs/issues/613)) ([d34981c](https://www.github.com/istanbuljs/istanbuljs/commit/d34981c8131e2ecbff6fc02ffd8702fd9808e241))

## [3.0.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.2) (2020-04-01)


### Bug Fixes

* Ignore insignificant lines when coalesce ([#525](https://github.com/istanbuljs/istanbuljs/issues/525)) ([d7d7cfa](https://github.com/istanbuljs/istanbuljs/commit/d7d7cfa1301f0dde2ff19078c31235ffd55c01ef))





## [3.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.1) (2020-03-26)


### Bug Fixes

* cobertura should escape invalid characters ([#534](https://github.com/istanbuljs/istanbuljs/issues/534)) ([4fd5114](https://github.com/istanbuljs/istanbuljs/commit/4fd5114a0926d20e4e1e3055323c44281f0af6cd))





# [3.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0) (2019-12-20)


### Features

* **text:** Coalesce ranges of missing lines ([#511](https://github.com/istanbuljs/istanbuljs/issues/511)) ([54636fc](https://github.com/istanbuljs/istanbuljs/commit/54636fc9acbb53e5724fe9018837d0d205413194))





# [3.0.0-alpha.6](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0-alpha.6) (2019-12-07)


### Bug Fixes

* Add favicon to html report ([#493](https://github.com/istanbuljs/istanbuljs/issues/493)) ([5afe203](https://github.com/istanbuljs/istanbuljs/commit/5afe20347dd3ae954b31707a67f381f87920797f))





# [3.0.0-alpha.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0-alpha.5) (2019-11-22)


### Features

* Add support for projectRoot option ([#492](https://github.com/istanbuljs/istanbuljs/issues/492)) ([177fd45](https://github.com/istanbuljs/istanbuljs/commit/177fd45ebd7e505e79120995d937d40f965bad79))





# [3.0.0-alpha.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0-alpha.4) (2019-11-18)


### Bug Fixes

* Remove handlebars ([#503](https://github.com/istanbuljs/istanbuljs/issues/503)) ([aa8ae7f](https://github.com/istanbuljs/istanbuljs/commit/aa8ae7fe42ef9c8aeaa193309bafb22ad725bc3d)), closes [#476](https://github.com/istanbuljs/istanbuljs/issues/476)





# [3.0.0-alpha.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0-alpha.3) (2019-10-19)


### Bug Fixes

* Add missing dependency on istanbul-lib-report ([#490](https://github.com/istanbuljs/istanbuljs/issues/490)) ([95a2b2f](https://github.com/istanbuljs/istanbuljs/commit/95a2b2f)), closes [istanbuljs/nyc#1204](https://github.com/istanbuljs/nyc/issues/1204)





# [3.0.0-alpha.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0-alpha.2) (2019-10-06)


### Bug Fixes

* Use path.posix.relative to generate URL's for html reports ([#472](https://github.com/istanbuljs/istanbuljs/issues/472)) ([05dc22c](https://github.com/istanbuljs/istanbuljs/commit/05dc22c))
* **html-spa:** Filter only exact paths ([#431](https://github.com/istanbuljs/istanbuljs/issues/431)) ([bbc85f6](https://github.com/istanbuljs/istanbuljs/commit/bbc85f6)), closes [#426](https://github.com/istanbuljs/istanbuljs/issues/426)





# [3.0.0-alpha.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0-alpha.1) (2019-06-20)


### Bug Fixes

* Set `opts.file = '-'` on text-lcov ([#424](https://github.com/istanbuljs/istanbuljs/issues/424)) ([4be56b2](https://github.com/istanbuljs/istanbuljs/commit/4be56b2))





# [3.0.0-alpha.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@3.0.0-alpha.0) (2019-06-19)


### Features

* Refactor istanbul-lib-report so report can choose summarizer ([#408](https://github.com/istanbuljs/istanbuljs/issues/408)) ([0f328fd](https://github.com/istanbuljs/istanbuljs/commit/0f328fd))
* **text report:** Optimize output to show more missing lines ([#341](https://github.com/istanbuljs/istanbuljs/issues/341)) ([c4e8b8e](https://github.com/istanbuljs/istanbuljs/commit/c4e8b8e))
* Modern html report ([#345](https://github.com/istanbuljs/istanbuljs/issues/345)) ([95ebaf1](https://github.com/istanbuljs/istanbuljs/commit/95ebaf1))
* Update dependencies, require Node.js 8 ([#401](https://github.com/istanbuljs/istanbuljs/issues/401)) ([bf3a539](https://github.com/istanbuljs/istanbuljs/commit/bf3a539))


### BREAKING CHANGES

* Existing istanbul-lib-report API's have been changed
* Node.js 8 is now required





## [2.2.5](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.2.5) (2019-05-02)


### Bug Fixes

* **istanbul-reports:** Remove isRoot check causing incorrect report formatting ([#66](https://github.com/istanbuljs/istanbuljs/issues/66)). ([#382](https://github.com/istanbuljs/istanbuljs/issues/382)) ([df6e994](https://github.com/istanbuljs/istanbuljs/commit/df6e994))





## [2.2.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.2.4) (2019-04-24)

**Note:** Version bump only for package istanbul-reports





## [2.2.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.2.3) (2019-04-17)


### Bug Fixes

* Initialize cols for HTML report sorting ([#369](https://github.com/istanbuljs/istanbuljs/issues/369)) ([28f61de](https://github.com/istanbuljs/istanbuljs/commit/28f61de))





## [2.2.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.2.2) (2019-04-09)

**Note:** Version bump only for package istanbul-reports





## [2.2.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.2.1) (2019-04-03)

**Note:** Version bump only for package istanbul-reports





# [2.2.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.2.0) (2019-03-12)


### Features

* set medium colour to yellow ([#306](https://github.com/istanbuljs/istanbuljs/issues/306)) ([ed40be7](https://github.com/istanbuljs/istanbuljs/commit/ed40be7))





## [2.1.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.1.1) (2019-02-14)


### Bug Fixes

* update dependencies due to vulnerabilities ([#294](https://github.com/istanbuljs/istanbuljs/issues/294)) ([4c14fed](https://github.com/istanbuljs/istanbuljs/commit/4c14fed))





# [2.1.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.1.0) (2019-01-26)


### Features

* **istanbul-reports:** Enable keyboard shortcuts on HTML report file listing view ([#265](https://github.com/istanbuljs/istanbuljs/issues/265)) ([f49b355](https://github.com/istanbuljs/istanbuljs/commit/f49b355))





<a name="2.0.3"></a>
## [2.0.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.0.3) (2018-12-25)


### Bug Fixes

* functionMap is sometimes missing a key from functions ([#253](https://github.com/istanbuljs/istanbuljs/issues/253)) ([399f215](https://github.com/istanbuljs/istanbuljs/commit/399f215))




<a name="2.0.2"></a>
## [2.0.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.0.2) (2018-12-19)


### Bug Fixes

* clover report metrics must be an inline xml element ([#226](https://github.com/istanbuljs/istanbuljs/issues/226)) ([e290c95](https://github.com/istanbuljs/istanbuljs/commit/e290c95)), closes [#13](https://github.com/istanbuljs/istanbuljs/issues/13)




<a name="2.0.1"></a>
## [2.0.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.0.1) (2018-09-06)




**Note:** Version bump only for package istanbul-reports

<a name="2.0.0"></a>
# [2.0.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@2.0.0) (2018-07-07)


### Chores

* Specify node >= 6 in istanbul-reports. ([#197](https://github.com/istanbuljs/istanbuljs/issues/197)) ([5810c38](https://github.com/istanbuljs/istanbuljs/commit/5810c38))


### BREAKING CHANGES

* Requires node >= 6.




<a name="1.5.0"></a>
# [1.5.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.5.0) (2018-06-06)


### Features

* ability to skip rows with full coverage ([#170](https://github.com/istanbuljs/istanbuljs/issues/170)) ([bbcdc07](https://github.com/istanbuljs/istanbuljs/commit/bbcdc07))




<a name="1.4.1"></a>
## [1.4.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.4.1) (2018-05-31)


### Bug Fixes

* ensure using correct context ([#168](https://github.com/istanbuljs/istanbuljs/issues/168)) ([df102fd](https://github.com/istanbuljs/istanbuljs/commit/df102fd))




<a name="1.4.0"></a>
# [1.4.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.4.0) (2018-04-17)


### Features

* allow custom reporters to be loaded ([#155](https://github.com/istanbuljs/istanbuljs/issues/155)) ([6d89cca](https://github.com/istanbuljs/istanbuljs/commit/6d89cca))




<a name="1.3.0"></a>
# [1.3.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.3.0) (2018-03-09)


### Features

* added named anchors to code coverage line numbers. ([#149](https://github.com/istanbuljs/istanbuljs/issues/149)) ([98e1c50](https://github.com/istanbuljs/istanbuljs/commit/98e1c50))




<a name="1.2.0"></a>
# [1.2.0](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.2.0) (2018-03-04)


### Bug Fixes

* update fixtures to reflect new heading ([36801d3](https://github.com/istanbuljs/istanbuljs/commit/36801d3))


### Features

* add skip-empty option for html & text reports ([#140](https://github.com/istanbuljs/istanbuljs/issues/140)) ([d2a4262](https://github.com/istanbuljs/istanbuljs/commit/d2a4262))
* add uncovered block navigation ([#136](https://github.com/istanbuljs/istanbuljs/issues/136)) ([c798930](https://github.com/istanbuljs/istanbuljs/commit/c798930))




<a name="1.1.4"></a>
## [1.1.4](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.1.4) (2018-02-13)


### Bug Fixes

* changed column header from "Uncovered Lines" to "Uncovered Line #s" ([#138](https://github.com/istanbuljs/istanbuljs/issues/138)) ([7ba7760](https://github.com/istanbuljs/istanbuljs/commit/7ba7760))




<a name="1.1.3"></a>
## [1.1.3](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.1.3) (2017-10-21)




**Note:** Version bump only for package istanbul-reports

<a name="1.1.2"></a>
## [1.1.2](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.1.2) (2017-08-26)


### Bug Fixes

* prevent branch highlighting from extending pass the end of a line ([#80](https://github.com/istanbuljs/istanbuljs/issues/80)) ([f490377](https://github.com/istanbuljs/istanbuljs/commit/f490377))




<a name="1.1.1"></a>
## [1.1.1](https://github.com/istanbuljs/istanbuljs/compare/<EMAIL>-reports@1.1.1) (2017-05-27)




<a name="1.1.0"></a>
# [1.1.0](https://github.com/istanbuljs/istanbul-reports/compare/<EMAIL>-reports@1.1.0) (2017-04-29)


### Features

* once 100% line coverage is achieved, missing branch coverage is now shown in text report ([#45](https://github.com/istanbuljs/istanbuljs/issues/45)) ([8a809f8](https://github.com/istanbuljs/istanbul-reports/commit/8a809f8))




<a name="1.0.2"></a>
## [1.0.2](https://github.com/istanbuljs/istanbul-reports/compare/<EMAIL>-reports@1.0.2) (2017-03-27)


### Bug Fixes

* **windows:** preserve escape char of json-summary key path ([4d71d5e](https://github.com/istanbuljs/istanbul-reports/commit/4d71d5e))

<a name="1.0.1"></a>
## [1.0.1](https://github.com/istanbuljs/istanbul-reports/compare/v1.0.0...v1.0.1) (2017-01-29)


### Bug Fixes

* add files key to package.json ([#17](https://github.com/istanbuljs/istanbul-reports/issues/17)) ([141f801](https://github.com/istanbuljs/istanbul-reports/commit/141f801))



<a name="1.0.0"></a>
# [1.0.0](https://github.com/istanbuljs/istanbul-reports/compare/v1.0.0-alpha.8...v1.0.0) (2016-10-17)


### Bug Fixes

* fail gracefully if structuredText[startLine] is undefined ([#10](https://github.com/istanbuljs/istanbul-reports/issues/10)) ([bed1d13](https://github.com/istanbuljs/istanbul-reports/commit/bed1d13))
* preserve escape char of json key path on Windows ([#12](https://github.com/istanbuljs/istanbul-reports/issues/12)) ([4e5266e](https://github.com/istanbuljs/istanbul-reports/commit/4e5266e))
* skip branch if meta does not exist (fixes speedskater/babel-plugin-rewire[#165](https://github.com/istanbuljs/istanbul-reports/issues/165)) ([#11](https://github.com/istanbuljs/istanbul-reports/issues/11)) ([62bae2f](https://github.com/istanbuljs/istanbul-reports/commit/62bae2f))
* Teamcity reporter modified to send proper coverage values ([#8](https://github.com/istanbuljs/istanbul-reports/issues/8)) ([4147f50](https://github.com/istanbuljs/istanbul-reports/commit/4147f50))
