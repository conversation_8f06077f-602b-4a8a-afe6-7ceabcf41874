import { TypeScriptVueExtensionOptions } from './TypeScriptVueExtensionOptions';
interface TypeScriptVueExtensionConfiguration {
    enabled: boolean;
    compiler: string;
}
declare function createTypeScriptVueExtensionConfiguration(options: TypeScriptVueExtensionOptions | undefined): TypeScriptVueExtensionConfiguration;
export { TypeScriptVueExtensionConfiguration, createTypeScriptVueExtensionConfiguration };
