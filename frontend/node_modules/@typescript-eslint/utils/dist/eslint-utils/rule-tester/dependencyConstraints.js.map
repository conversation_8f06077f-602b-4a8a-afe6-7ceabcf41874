{"version": 3, "file": "dependencyConstraints.js", "sourceRoot": "", "sources": ["../../../src/eslint-utils/rule-tester/dependencyConstraints.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAmBjC,MAAM,sBAAsB,GAAwB;IAClD,iBAAiB,EAAE,IAAI;CACxB,CAAC;AAEF,SAAS,6BAA6B,CACpC,WAAmB,EACnB,YAA0C;IAE1C,MAAM,UAAU,GACd,OAAO,YAAY,KAAK,QAAQ;QAC9B,CAAC,CAAC;YACE,KAAK,EAAE,KAAK,YAAY,EAAE;SAC3B;QACH,CAAC,CAAC,YAAY,CAAC;IAEnB,OAAO,MAAM,CAAC,SAAS,CACpB,OAAO,CAAC,GAAG,WAAW,eAAe,CAAyB,CAAC,OAAO,EACvE,UAAU,CAAC,KAAK,EAChB,OAAO,UAAU,CAAC,OAAO,KAAK,QAAQ;QACpC,CAAC,iCAAM,sBAAsB,GAAK,UAAU,CAAC,OAAO,EACpD,CAAC,CAAC,UAAU,CAAC,OAAO,CACvB,CAAC;AACJ,CAAC;AAED,SAAS,iCAAiC,CACxC,qBAAuD;IAEvD,IAAI,qBAAqB,IAAI,IAAI,EAAE;QACjC,OAAO,IAAI,CAAC;KACb;IAED,KAAK,MAAM,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC,OAAO,CACpD,qBAAqB,CACtB,EAAE;QACD,IAAI,CAAC,6BAA6B,CAAC,WAAW,EAAE,UAAU,CAAC,EAAE;YAC3D,OAAO,KAAK,CAAC;SACd;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAEQ,8EAAiC"}