{"version": 3, "file": "createParseSettings.js", "sourceRoot": "", "sources": ["../../src/parseSettings/createParseSettings.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAE1B,qDAA8D;AAE9D,mDAGyB;AACzB,mEAAgE;AAEhE,qDAAkD;AAClD,6DAA0D;AAC1D,6DAA0D;AAE1D,MAAM,GAAG,GAAG,IAAA,eAAK,EACf,8EAA8E,CAC/E,CAAC;AAEF,IAAI,oBAA0D,CAAC;AAE/D,SAAgB,mBAAmB,CACjC,IAAY,EACZ,UAAoC,EAAE;;IAEtC,MAAM,SAAS,GAAG,IAAA,+BAAc,EAAC,OAAO,CAAC,CAAC;IAC1C,MAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,QAAQ;QACzC,CAAC,CAAC,OAAO,CAAC,eAAe;QACzB,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IACpB,MAAM,aAAa,GAAyB;QAC1C,IAAI,EAAE,aAAa,CAAC,IAAI,CAAC;QACzB,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,IAAI;QACjC,QAAQ,EAAE,EAAE;QACZ,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,KAAK,IAAI;QAC3D,UAAU,EACR,OAAO,CAAC,UAAU,KAAK,IAAI;YACzB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,mBAAmB,CAAC,CAAC;YAChC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;gBACnC,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC7B,CAAC,CAAC,IAAI,GAAG,EAAE;QACf,2CAA2C,EAAE,KAAK;QAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,KAAK,IAAI;QAC7D,gDAAgD,EAC9C,OAAO,CAAC,gDAAgD,KAAK,IAAI;QACnE,mBAAmB,EACjB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC;YAC1C,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC;YAC/D,CAAC,CAAC,OAAO,CAAC,mBAAmB;YAC7B,CAAC,CAAC,EAAE;QACR,QAAQ,EAAE,IAAA,2BAAkB,EAC1B,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS;YACpE,CAAC,CAAC,OAAO,CAAC,QAAQ;YAClB,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAC5B,eAAe,CAChB;QACD,GAAG,EAAE,OAAO,CAAC,GAAG,KAAK,IAAI;QACzB,GAAG,EAAE,OAAO,CAAC,GAAG,KAAK,IAAI;QACzB,GAAG,EACD,OAAO,OAAO,CAAC,QAAQ,KAAK,UAAU;YACpC,CAAC,CAAC,OAAO,CAAC,QAAQ;YAClB,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK;gBAC5B,CAAC,CAAC,GAAS,EAAE,GAAE,CAAC;gBAChB,CAAC,CAAC,OAAO,CAAC,GAAG;QACjB,cAAc,EAAE,MAAA,OAAO,CAAC,cAAc,mCAAI,EAAE;QAC5C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,KAAK,KAAK;QACpD,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI;QACnE,QAAQ,EAAE,EAAE;QACZ,KAAK,EAAE,OAAO,CAAC,KAAK,KAAK,IAAI;QAC7B,SAAS;QACT,MAAM,EAAE,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI;QAC3C,kBAAkB,EAAE,CAAC,oBAAoB,aAApB,oBAAoB,cAApB,oBAAoB,IAApB,oBAAoB,GAAK,IAAI,6BAAa,CAC7D,SAAS;YACP,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,MAAA,MAAA,OAAO,CAAC,aAAa,0CAAE,IAAI,mCAC3B,uDAAuC,CAC5C,EAAC;QACF,eAAe;KAChB,CAAC;IAEF,8EAA8E;IAC9E,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE;QACrC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE;YACrD,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACxC;QACD,IACE,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACtC,6EAA6E;YAC7E,eAAK,CAAC,OAAO,CAAC,4BAA4B,CAAC,EAC3C;YACA,mGAAmG;YACnG,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC/C;QACD,eAAK,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;KACpC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QACnC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE;YAC5B,MAAM,IAAI,KAAK,CACb,qPAAqP,CACtP,CAAC;SACH;QACD,GAAG,CACD,gFAAgF,CACjF,CAAC;KACH;IAED,mDAAmD;IACnD,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;QAC3B,aAAa,CAAC,QAAQ,GAAG,IAAA,uCAAkB,EAAC;YAC1C,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,OAAO,EAAE,IAAA,6CAAqB,EAAC,aAAa,EAAE,OAAO,CAAC,OAAO,CAAC;YAC9D,uBAAuB,EAAE,OAAO,CAAC,uBAAuB;YACxD,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,eAAe,EAAE,eAAe;SACjC,CAAC,CAAC;KACJ;IAED,IAAA,uCAAkB,EAAC,aAAa,CAAC,CAAC;IAElC,OAAO,aAAa,CAAC;AACvB,CAAC;AArGD,kDAqGC;AAED,SAAgB,uBAAuB;IACrC,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,EAAE,CAAC;AAChC,CAAC;AAFD,0DAEC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,IAAa;IAClC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;KACrB;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,WAAW,CAAC,GAAa;IAChC,OAAO,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC;AAC1C,CAAC"}