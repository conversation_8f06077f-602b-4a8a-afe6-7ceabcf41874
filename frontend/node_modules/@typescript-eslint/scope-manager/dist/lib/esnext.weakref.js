"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_weakref = void 0;
const base_config_1 = require("./base-config");
exports.esnext_weakref = {
    WeakRef: base_config_1.TYPE_VALUE,
    WeakRefConstructor: base_config_1.TYPE,
    FinalizationRegistry: base_config_1.TYPE_VALUE,
    FinalizationRegistryConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=esnext.weakref.js.map