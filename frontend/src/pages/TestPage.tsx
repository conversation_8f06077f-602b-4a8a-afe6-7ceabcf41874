import React, { useState, useEffect } from 'react';
import { Card, Button, Space, message, Spin, Alert } from 'antd';
import { serverApi, dockerApi, scriptApi, monitoringApi } from '../services/api';

const TestPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>({});

  const testAPI = async (apiName: string, apiCall: () => Promise<any>) => {
    try {
      setLoading(true);
      const result = await apiCall();
      setResults(prev => ({
        ...prev,
        [apiName]: { success: true, data: result }
      }));
      message.success(`${apiName} 测试成功`);
    } catch (error) {
      setResults(prev => ({
        ...prev,
        [apiName]: { success: false, error: error.message }
      }));
      message.error(`${apiName} 测试失败`);
    } finally {
      setLoading(false);
    }
  };

  const testAllAPIs = async () => {
    const tests = [
      { name: '服务器列表', call: () => serverApi.getServers() },
      { name: '脚本列表', call: () => scriptApi.getScripts() },
      { name: '执行记录', call: () => scriptApi.getExecutions() },
      { name: '监控数据', call: () => monitoringApi.getServerMonitoring() },
      { name: '告警列表', call: () => monitoringApi.getAlerts() },
    ];

    for (const test of tests) {
      await testAPI(test.name, test.call);
      await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="API 功能测试" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="API 测试页面"
            description="这个页面用于测试各个API接口的连通性和功能。由于后端服务可能未启动，系统会自动使用模拟数据。"
            type="info"
            showIcon
          />
          
          <Space wrap>
            <Button 
              type="primary" 
              onClick={testAllAPIs}
              loading={loading}
            >
              测试所有API
            </Button>
            
            <Button 
              onClick={() => testAPI('服务器列表', () => serverApi.getServers())}
              loading={loading}
            >
              测试服务器API
            </Button>
            
            <Button 
              onClick={() => testAPI('脚本列表', () => scriptApi.getScripts())}
              loading={loading}
            >
              测试脚本API
            </Button>
            
            <Button 
              onClick={() => testAPI('监控数据', () => monitoringApi.getServerMonitoring())}
              loading={loading}
            >
              测试监控API
            </Button>
          </Space>
        </Space>
      </Card>

      <Card title="测试结果">
        {Object.keys(results).length === 0 ? (
          <p>暂无测试结果，请点击上方按钮开始测试</p>
        ) : (
          <Space direction="vertical" style={{ width: '100%' }}>
            {Object.entries(results).map(([apiName, result]: [string, any]) => (
              <Card 
                key={apiName}
                size="small"
                title={apiName}
                extra={
                  <span style={{ color: result.success ? 'green' : 'red' }}>
                    {result.success ? '✅ 成功' : '❌ 失败'}
                  </span>
                }
              >
                {result.success ? (
                  <div>
                    <p><strong>数据类型:</strong> {Array.isArray(result.data) ? '数组' : typeof result.data}</p>
                    {Array.isArray(result.data) && (
                      <p><strong>数据长度:</strong> {result.data.length}</p>
                    )}
                    <details>
                      <summary>查看详细数据</summary>
                      <pre style={{ 
                        background: '#f5f5f5', 
                        padding: '8px', 
                        borderRadius: '4px',
                        fontSize: '12px',
                        maxHeight: '200px',
                        overflow: 'auto'
                      }}>
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  </div>
                ) : (
                  <div>
                    <p style={{ color: 'red' }}>
                      <strong>错误:</strong> {result.error}
                    </p>
                  </div>
                )}
              </Card>
            ))}
          </Space>
        )}
      </Card>

      <Card title="系统状态" style={{ marginTop: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <strong>前端服务:</strong> 
            <span style={{ color: 'green', marginLeft: '8px' }}>✅ 运行中</span>
            <span style={{ marginLeft: '8px', color: '#666' }}>http://localhost:3000</span>
          </div>
          
          <div>
            <strong>后端API:</strong> 
            <span style={{ color: 'orange', marginLeft: '8px' }}>⚠️ 使用模拟数据</span>
            <span style={{ marginLeft: '8px', color: '#666' }}>http://localhost:8000</span>
          </div>
          
          <div>
            <strong>API文档:</strong> 
            <a 
              href="http://localhost:8000/docs" 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ marginLeft: '8px' }}
            >
              查看Swagger文档
            </a>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default TestPage;
