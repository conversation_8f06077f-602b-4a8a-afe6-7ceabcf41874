/**
 * API服务层
 */
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    // 如果是网络错误或服务器错误，返回模拟数据
    if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
      console.warn('API服务不可用，使用模拟数据');
      return Promise.resolve(getMockData(error.config));
    }
    return Promise.reject(error);
  }
);

// 模拟数据函数
function getMockData(config: any) {
  const url = config.url;

  if (url.includes('/api/v1/servers') && config.method === 'get') {
    return [
      {
        id: 1,
        name: '测试服务器1',
        host: '*************',
        port: 22,
        username: 'root',
        auth_type: 'password',
        description: '测试用服务器',
        tags: ['test', 'development'],
        monitoring_enabled: true,
        alert_enabled: true,
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ];
  }

  if (url.includes('/api/v1/scripts') && config.method === 'get' && !url.includes('executions')) {
    return [
      {
        id: 1,
        name: '系统信息收集',
        description: '收集服务器基本信息',
        script_type: 'shell',
        category: '系统管理',
        tags: ['system', 'info'],
        content: '#!/bin/bash\nuname -a\ndf -h\nfree -m',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ];
  }

  if (url.includes('/api/v1/scripts/executions')) {
    return [
      {
        id: 1,
        script_name: '系统信息收集',
        server_name: '测试服务器1',
        status: 'success',
        exit_code: 0,
        duration: 5,
        started_at: '2024-01-01T10:00:00Z',
        finished_at: '2024-01-01T10:00:05Z'
      }
    ];
  }

  // Docker API 模拟数据
  if (url.includes('/api/v1/docker') && url.includes('/images')) {
    return [
      {
        repository: 'nginx',
        tag: 'latest',
        image_id: 'sha256:abcd1234',
        created: '2024-01-01 00:00:00',
        size: '133MB'
      },
      {
        repository: 'mysql',
        tag: '8.0',
        image_id: 'sha256:efgh5678',
        created: '2024-01-01 00:00:00',
        size: '521MB'
      }
    ];
  }

  if (url.includes('/api/v1/docker') && url.includes('/containers')) {
    return [
      {
        container_id: 'abcd1234efgh',
        name: 'web-server',
        image: 'nginx:latest',
        status: 'Up 2 hours',
        ports: '0.0.0.0:80->80/tcp',
        created: '2024-01-01 00:00:00'
      },
      {
        container_id: 'ijkl5678mnop',
        name: 'database',
        image: 'mysql:8.0',
        status: 'Up 1 day',
        ports: '0.0.0.0:3306->3306/tcp',
        created: '2024-01-01 00:00:00'
      }
    ];
  }

  if (url.includes('/api/v1/docker') && url.includes('/gpu-stats')) {
    return [
      {
        gpu_id: 0,
        name: 'NVIDIA GeForce RTX 4090',
        utilization: 75,
        memory_used: 12000,
        memory_total: 24000
      }
    ];
  }

  // 监控API模拟数据
  if (url.includes('/api/v1/monitoring/servers')) {
    return [
      {
        id: 1,
        name: '测试服务器1',
        status: 'online',
        cpu_usage: 25.5,
        memory_usage: 68.2,
        disk_usage: 45.8,
        last_update: new Date().toISOString()
      },
      {
        id: 2,
        name: '生产服务器1',
        status: 'warning',
        cpu_usage: 85.2,
        memory_usage: 92.1,
        disk_usage: 78.5,
        last_update: new Date().toISOString()
      }
    ];
  }

  if (url.includes('/api/v1/monitoring/alerts')) {
    return [
      {
        id: 1,
        server_name: '生产服务器1',
        alert_type: 'cpu_high',
        severity: 'high',
        message: 'CPU使用率过高: 85.2%',
        created_at: new Date().toISOString(),
        status: 'active'
      },
      {
        id: 2,
        server_name: '生产服务器1',
        alert_type: 'memory_high',
        severity: 'critical',
        message: '内存使用率过高: 92.1%',
        created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        status: 'active'
      }
    ];
  }

  if (url.includes('/test-connection')) {
    return { success: true, message: '连接成功', latency: 50 };
  }

  if (url.includes('/execute')) {
    return {
      success: true,
      exit_code: 0,
      stdout: 'Linux testserver 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux\nFilesystem      Size  Used Avail Use% Mounted on\n/dev/sda1        20G  8.5G   11G  45% /\ntmpfs           2.0G     0  2.0G   0% /dev/shm',
      stderr: '',
      duration: 2.5
    };
  }

  if (url.includes('/logs')) {
    return {
      logs: '2024-01-01 00:00:00 [INFO] Container started\n2024-01-01 00:00:01 [INFO] Application ready\n2024-01-01 00:00:02 [INFO] Listening on port 80'
    };
  }

  return { success: true, message: '操作成功' };
}

// 服务器管理API
export const serverApi = {
  // 获取服务器列表
  getServers: () => api.get('/api/v1/servers'),
  
  // 获取服务器详情
  getServer: (id: number) => api.get(`/api/v1/servers/${id}`),
  
  // 创建服务器
  createServer: (data: any) => api.post('/api/v1/servers', data),
  
  // 更新服务器
  updateServer: (id: number, data: any) => api.put(`/api/v1/servers/${id}`, data),
  
  // 删除服务器
  deleteServer: (id: number) => api.delete(`/api/v1/servers/${id}`),
  
  // 测试连接
  testConnection: (id: number) => api.post(`/api/v1/servers/${id}/test-connection`),
  
  // 执行命令
  executeCommand: (id: number, data: any) => api.post(`/api/v1/servers/${id}/execute`, data),
  
  // 开始监控
  startMonitoring: (id: number) => api.post(`/api/v1/servers/${id}/start-monitoring`),
  
  // 停止监控
  stopMonitoring: (id: number) => api.post(`/api/v1/servers/${id}/stop-monitoring`),
};

// Docker管理API
export const dockerApi = {
  // 获取镜像列表
  getImages: (serverId: number) => api.get(`/api/v1/docker/${serverId}/images`),
  
  // 拉取镜像
  pullImage: (serverId: number, imageName: string) => 
    api.post(`/api/v1/docker/${serverId}/images/pull?image_name=${imageName}`),
  
  // 删除镜像
  deleteImage: (serverId: number, imageId: string, force = false) => 
    api.delete(`/api/v1/docker/${serverId}/images/${imageId}?force=${force}`),
  
  // 获取容器列表
  getContainers: (serverId: number, all = true) => 
    api.get(`/api/v1/docker/${serverId}/containers?all_containers=${all}`),
  
  // 创建容器
  createContainer: (serverId: number, config: any) => 
    api.post(`/api/v1/docker/${serverId}/containers`, config),
  
  // 启动容器
  startContainer: (serverId: number, containerId: string) => 
    api.post(`/api/v1/docker/${serverId}/containers/${containerId}/start`),
  
  // 停止容器
  stopContainer: (serverId: number, containerId: string, timeout = 10) => 
    api.post(`/api/v1/docker/${serverId}/containers/${containerId}/stop?timeout=${timeout}`),
  
  // 重启容器
  restartContainer: (serverId: number, containerId: string) => 
    api.post(`/api/v1/docker/${serverId}/containers/${containerId}/restart`),
  
  // 删除容器
  deleteContainer: (serverId: number, containerId: string, force = false) => 
    api.delete(`/api/v1/docker/${serverId}/containers/${containerId}?force=${force}`),
  
  // 获取容器日志
  getContainerLogs: (serverId: number, containerId: string, lines = 100) => 
    api.get(`/api/v1/docker/${serverId}/containers/${containerId}/logs?lines=${lines}`),
  
  // 获取容器统计
  getContainerStats: (serverId: number, containerId: string) => 
    api.get(`/api/v1/docker/${serverId}/containers/${containerId}/stats`),
  
  // 获取GPU统计
  getGpuStats: (serverId: number) => 
    api.get(`/api/v1/docker/${serverId}/gpu-stats`),
};

// 脚本管理API
export const scriptApi = {
  // 获取脚本列表
  getScripts: () => api.get('/api/v1/scripts'),
  
  // 获取脚本详情
  getScript: (id: number) => api.get(`/api/v1/scripts/${id}`),
  
  // 创建脚本
  createScript: (data: any) => api.post('/api/v1/scripts', data),
  
  // 更新脚本
  updateScript: (id: number, data: any) => api.put(`/api/v1/scripts/${id}`, data),
  
  // 删除脚本
  deleteScript: (id: number) => api.delete(`/api/v1/scripts/${id}`),
  
  // 执行脚本
  executeScript: (id: number, data: any) => api.post(`/api/v1/scripts/${id}/execute`, data),
  
  // 获取执行记录
  getExecutions: () => api.get('/api/v1/scripts/executions'),
  
  // 获取执行详情
  getExecution: (id: number) => api.get(`/api/v1/scripts/executions/${id}`),
};

// 文件管理API
export const fileApi = {
  // 上传文件
  uploadFile: (serverId: number, formData: FormData) => 
    api.post(`/api/v1/files/${serverId}/upload`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),
  
  // 下载文件
  downloadFile: (serverId: number, remotePath: string) => 
    api.get(`/api/v1/files/${serverId}/download?remote_path=${remotePath}`, {
      responseType: 'blob'
    }),
  
  // 列出文件
  listFiles: (serverId: number, remotePath = '/') => 
    api.get(`/api/v1/files/${serverId}/list?remote_path=${remotePath}`),
  
  // 删除文件
  deleteFile: (serverId: number, remotePath: string) => 
    api.delete(`/api/v1/files/${serverId}/delete?remote_path=${remotePath}`),
  
  // 创建目录
  createDirectory: (serverId: number, remotePath: string) => 
    api.post(`/api/v1/files/${serverId}/mkdir?remote_path=${remotePath}`),
  
  // 获取文件内容
  getFileContent: (serverId: number, remotePath: string) => 
    api.get(`/api/v1/files/${serverId}/content?remote_path=${remotePath}`),
  
  // 保存文件内容
  saveFileContent: (serverId: number, remotePath: string, content: string) => 
    api.post(`/api/v1/files/${serverId}/content`, { remote_path: remotePath, content }),
};

// 监控API
export const monitoringApi = {
  // 获取服务器监控数据
  getServerMonitoring: () => api.get('/api/v1/monitoring/servers'),
  
  // 获取服务器详细监控
  getServerStats: (serverId: number) => api.get(`/api/v1/monitoring/servers/${serverId}/stats`),
  
  // 获取历史数据
  getHistoryData: (serverId: number, timeRange: string) => 
    api.get(`/api/v1/monitoring/servers/${serverId}/history?range=${timeRange}`),
  
  // 获取告警列表
  getAlerts: () => api.get('/api/v1/monitoring/alerts'),
  
  // 确认告警
  acknowledgeAlert: (alertId: number) => 
    api.post(`/api/v1/monitoring/alerts/${alertId}/acknowledge`),
  
  // 解决告警
  resolveAlert: (alertId: number) => 
    api.post(`/api/v1/monitoring/alerts/${alertId}/resolve`),
};

// 健康检查API
export const healthApi = {
  // 系统健康检查
  getHealth: () => api.get('/health'),
  
  // 获取系统信息
  getSystemInfo: () => api.get('/'),
};

export default api;
