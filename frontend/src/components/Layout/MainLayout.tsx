import React, { useState } from 'react';
import {
  Layout,
  Menu,
  theme,
  Avatar,
  Dropdown,
  Space,
  Badge,
  Button,
} from 'antd';
import {
  DatabaseOutlined,
  ContainerOutlined,
  CodeOutlined,
  MonitorOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import ServerManagement from '../Server/ServerManagement';
import DockerManagement from '../Docker/DockerManagement';
import ScriptManagement from '../Script/ScriptManagement';
import MonitoringDashboard from '../Monitoring/MonitoringDashboard';

const { Header, Sider, Content } = Layout;

type MenuItem = Required<MenuProps>['items'][number];

const items: MenuItem[] = [
  {
    key: 'servers',
    icon: <DatabaseOutlined />,
    label: '服务器管理',
  },
  {
    key: 'docker',
    icon: <ContainerOutlined />,
    label: 'Docker管理',
  },
  {
    key: 'scripts',
    icon: <CodeOutlined />,
    label: '脚本管理',
  },
  {
    key: 'monitoring',
    icon: <MonitorOutlined />,
    label: '监控面板',
  },
  {
    key: 'files',
    icon: <FileTextOutlined />,
    label: '文件管理',
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '系统设置',
  },
];

const userMenuItems: MenuProps['items'] = [
  {
    key: 'profile',
    label: '个人资料',
    icon: <UserOutlined />,
  },
  {
    key: 'settings',
    label: '设置',
    icon: <SettingOutlined />,
  },
  {
    type: 'divider',
  },
  {
    key: 'logout',
    label: '退出登录',
  },
];

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('servers');
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const renderContent = () => {
    switch (selectedKey) {
      case 'servers':
        return <ServerManagement />;
      case 'docker':
        return <DockerManagement />;
      case 'scripts':
        return <ScriptManagement />;
      case 'monitoring':
        return <MonitoringDashboard />;
      case 'files':
        return <div>文件管理功能开发中...</div>;
      case 'settings':
        return <div>系统设置功能开发中...</div>;
      default:
        return <ServerManagement />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div className="demo-logo-vertical" style={{ 
          height: 32, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 6,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          {collapsed ? 'DMS' : '设备管理系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedKey]}
          items={items}
          onClick={({ key }: { key: string }) => setSelectedKey(key)}
        />
      </Sider>
      <Layout>
        <Header style={{ 
          padding: '0 16px', 
          background: colorBgContainer,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space size="middle">
            <Badge count={5}>
              <Button type="text" icon={<BellOutlined />} />
            </Badge>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <span>管理员</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
          }}
        >
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
