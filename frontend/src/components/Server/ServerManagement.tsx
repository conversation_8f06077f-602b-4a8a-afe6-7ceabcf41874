import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Statistic,
  Spin,
  Alert,
} from 'antd';
import { serverApi } from '../../services/api';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

interface Server {
  id: number;
  name: string;
  host: string;
  port: number;
  username: string;
  auth_type: string;
  description?: string;
  tags?: string[];
  monitoring_enabled: boolean;
  alert_enabled: boolean;
  is_active: boolean;
  status?: 'online' | 'offline' | 'connecting';
  created_at: string;
  updated_at: string;
}

const { Option } = Select;

const ServerManagement: React.FC = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingServer, setEditingServer] = useState<Server | null>(null);
  const [commandModalVisible, setCommandModalVisible] = useState(false);
  const [selectedServer, setSelectedServer] = useState<Server | null>(null);
  const [form] = Form.useForm();
  const [commandForm] = Form.useForm();

  // 加载服务器数据
  const loadServers = async () => {
    try {
      setLoading(true);
      const data = await serverApi.getServers();
      setServers(data.map((server: any) => ({
        ...server,
        status: Math.random() > 0.5 ? 'online' : 'offline' // 模拟状态
      })));
    } catch (error) {
      console.error('加载服务器列表失败:', error);
      message.error('加载服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadServers();
  }, []);

  const columns: ColumnsType<Server> = [
    {
      title: '服务器名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <span>{text}</span>
          <Tag color={record.status === 'online' ? 'green' : record.status === 'offline' ? 'red' : 'orange'}>
            {record.status === 'online' ? '在线' : record.status === 'offline' ? '离线' : '连接中'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '地址',
      dataIndex: 'host',
      key: 'host',
      render: (text, record) => `${text}:${record.port}`,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '认证方式',
      dataIndex: 'auth_type',
      key: 'auth_type',
      render: (text) => (
        <Tag color={text === 'password' ? 'blue' : 'green'}>
          {text === 'password' ? '密码' : '密钥'}
        </Tag>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags: string[]) => (
        <>
          {tags?.map((tag) => (
            <Tag key={tag} color="blue">
              {tag}
            </Tag>
          ))}
        </>
      ),
    },
    {
      title: '监控状态',
      key: 'monitoring',
      render: (_, record) => (
        <Space>
          <Tag color={record.monitoring_enabled ? 'green' : 'default'}>
            {record.monitoring_enabled ? '已启用' : '已禁用'}
          </Tag>
          <Tag color={record.alert_enabled ? 'orange' : 'default'}>
            {record.alert_enabled ? '告警开启' : '告警关闭'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewServer(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditServer(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={record.monitoring_enabled ? <StopOutlined /> : <PlayCircleOutlined />}
            onClick={() => handleToggleMonitoring(record)}
          >
            {record.monitoring_enabled ? '停止监控' : '开始监控'}
          </Button>
          <Button
            type="link"
            onClick={() => handleExecuteCommand(record)}
          >
            执行命令
          </Button>
          <Popconfirm
            title="确定要删除这台服务器吗？"
            onConfirm={() => handleDeleteServer(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAddServer = () => {
    setEditingServer(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditServer = (server: Server) => {
    setEditingServer(server);
    form.setFieldsValue(server);
    setModalVisible(true);
  };

  const handleViewServer = async (server: Server) => {
    try {
      // 测试连接状态
      const connectionResult = await serverApi.testConnection(server.id);

      Modal.info({
        title: `服务器详情 - ${server.name}`,
        content: (
          <div>
            <p><strong>地址:</strong> {server.host}:{server.port}</p>
            <p><strong>用户名:</strong> {server.username}</p>
            <p><strong>认证方式:</strong> {server.auth_type}</p>
            <p><strong>描述:</strong> {server.description}</p>
            <p><strong>标签:</strong> {server.tags?.join(', ')}</p>
            <p><strong>连接状态:</strong>
              <Tag color={(connectionResult as any).success ? 'green' : 'red'}>
                {(connectionResult as any).success ? '连接正常' : '连接失败'}
              </Tag>
            </p>
            {(connectionResult as any).success && (
              <p><strong>延迟:</strong> {(connectionResult as any).latency}ms</p>
            )}
            <p><strong>创建时间:</strong> {new Date(server.created_at).toLocaleString()}</p>
          </div>
        ),
        width: 600,
      });
    } catch (error) {
      message.error('获取服务器详情失败');
    }
  };

  const handleDeleteServer = async (id: number) => {
    try {
      await serverApi.deleteServer(id);
      setServers(servers.filter(s => s.id !== id));
      message.success('服务器删除成功');
    } catch (error) {
      message.error('删除服务器失败');
    }
  };

  const handleToggleMonitoring = async (server: Server) => {
    try {
      if (server.monitoring_enabled) {
        await serverApi.stopMonitoring(server.id);
      } else {
        await serverApi.startMonitoring(server.id);
      }

      const updatedServers = servers.map(s =>
        s.id === server.id
          ? { ...s, monitoring_enabled: !s.monitoring_enabled }
          : s
      );
      setServers(updatedServers);
      message.success(
        `${server.name} 监控已${server.monitoring_enabled ? '停止' : '启动'}`
      );
    } catch (error) {
      message.error('切换监控状态失败');
    }
  };

  const handleExecuteCommand = (server: Server) => {
    setSelectedServer(server);
    commandForm.resetFields();
    setCommandModalVisible(true);
  };

  const handleCommandOk = async () => {
    try {
      const values = await commandForm.validateFields();
      const result = await serverApi.executeCommand(selectedServer!.id, values);

      Modal.info({
        title: '命令执行结果',
        content: (
          <div>
            <p><strong>退出码:</strong> {(result as any).exit_code}</p>
            <p><strong>执行时间:</strong> {(result as any).duration}秒</p>
            <div style={{ marginTop: 16 }}>
              <strong>输出:</strong>
              <pre style={{
                background: '#f5f5f5',
                padding: 8,
                borderRadius: 4,
                maxHeight: 200,
                overflow: 'auto'
              }}>
                {(result as any).stdout}
              </pre>
            </div>
            {(result as any).stderr && (
              <div style={{ marginTop: 16 }}>
                <strong>错误:</strong>
                <pre style={{
                  background: '#fff2f0',
                  padding: 8,
                  borderRadius: 4,
                  maxHeight: 200,
                  overflow: 'auto',
                  color: 'red'
                }}>
                  {(result as any).stderr}
                </pre>
              </div>
            )}
          </div>
        ),
        width: 800,
      });

      setCommandModalVisible(false);
      commandForm.resetFields();
    } catch (error) {
      message.error('命令执行失败');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingServer) {
        // 更新服务器
        await serverApi.updateServer(editingServer.id, values);
        await loadServers(); // 重新加载列表
        message.success('服务器更新成功');
      } else {
        // 创建新服务器
        await serverApi.createServer(values);
        await loadServers(); // 重新加载列表
        message.success('服务器添加成功');
      }
      
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
      message.error(editingServer ? '更新服务器失败' : '添加服务器失败');
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    form.resetFields();
  };

  const onlineCount = servers.filter(s => s.status === 'online').length;
  const totalCount = servers.length;
  const monitoringCount = servers.filter(s => s.monitoring_enabled).length;

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总服务器数"
              value={totalCount}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线服务器"
              value={onlineCount}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="监控中"
              value={monitoringCount}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线率"
              value={totalCount > 0 ? Math.round((onlineCount / totalCount) * 100) : 0}
              suffix="%"
              valueStyle={{ color: onlineCount / totalCount > 0.8 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Card
        title="服务器列表"
        extra={
          <Space>
            <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>
              刷新
            </Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddServer}>
              添加服务器
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={servers}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
        />
      </Card>

      <Modal
        title={editingServer ? '编辑服务器' : '添加服务器'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            port: 22,
            auth_type: 'password',
            monitoring_enabled: true,
            alert_enabled: true,
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="服务器名称"
                name="name"
                rules={[{ required: true, message: '请输入服务器名称' }]}
              >
                <Input placeholder="请输入服务器名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="服务器地址"
                name="host"
                rules={[{ required: true, message: '请输入服务器地址' }]}
              >
                <Input placeholder="请输入IP地址或域名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="端口"
                name="port"
                rules={[{ required: true, message: '请输入端口号' }]}
              >
                <Input type="number" placeholder="22" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="用户名"
                name="username"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input placeholder="root" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="认证方式"
                name="auth_type"
                rules={[{ required: true, message: '请选择认证方式' }]}
              >
                <Select>
                  <Option value="password">密码认证</Option>
                  <Option value="key">密钥认证</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="描述"
            name="description"
          >
            <Input.TextArea rows={3} placeholder="请输入服务器描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="启用监控"
                name="monitoring_enabled"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="启用告警"
                name="alert_enabled"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      <Modal
        title={`执行命令 - ${selectedServer?.name}`}
        open={commandModalVisible}
        onOk={handleCommandOk}
        onCancel={() => setCommandModalVisible(false)}
        width={800}
      >
        <Form
          form={commandForm}
          layout="vertical"
        >
          <Form.Item
            label="命令"
            name="command"
            rules={[{ required: true, message: '请输入要执行的命令' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="例如: ls -la"
            />
          </Form.Item>

          <Form.Item
            label="工作目录"
            name="working_dir"
          >
            <Input placeholder="例如: /home/<USER>" />
          </Form.Item>

          <Form.Item
            label="环境变量"
            name="env_vars"
          >
            <Input.TextArea
              rows={2}
              placeholder="例如: PATH=/usr/bin:$PATH"
            />
          </Form.Item>

          <Form.Item
            label="超时时间(秒)"
            name="timeout"
            initialValue={300}
          >
            <Input type="number" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ServerManagement;
