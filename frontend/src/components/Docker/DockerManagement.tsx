import React, { useState, useEffect } from 'react';
import {
  Tabs,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Statistic,
  Progress,
} from 'antd';
import { dockerApi, serverApi } from '../../services/api';
import {
  PlusOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { TabPane } = Tabs;
const { Option } = Select;

interface DockerImage {
  repository: string;
  tag: string;
  image_id: string;
  created: string;
  size: string;
}

interface DockerContainer {
  container_id: string;
  name: string;
  image: string;
  status: string;
  ports: string;
  created: string;
}

interface ContainerStats {
  container_id: string;
  cpu_usage_percent: number;
  memory_usage: string;
  memory_limit: string;
  memory_usage_percent: number;
  network_io: string;
  block_io: string;
}

interface GPUStats {
  gpu_id: number;
  name: string;
  utilization: number;
  memory_used: number;
  memory_total: number;
}

const DockerManagement: React.FC = () => {
  const [images, setImages] = useState<DockerImage[]>([]);
  const [containers, setContainers] = useState<DockerContainer[]>([]);
  const [gpuStats, setGpuStats] = useState<GPUStats[]>([]);
  const [servers, setServers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedServer, setSelectedServer] = useState<number>(1);

  // 加载服务器列表
  const loadServers = async () => {
    try {
      const data = await serverApi.getServers();
      setServers(data);
      if (data.length > 0 && !selectedServer) {
        setSelectedServer(data[0].id);
      }
    } catch (error) {
      console.error('加载服务器列表失败:', error);
    }
  };

  // 加载Docker数据
  const loadDockerData = async () => {
    if (!selectedServer) return;

    try {
      setLoading(true);

      // 并行加载镜像、容器和GPU统计
      const [imagesData, containersData, gpuData] = await Promise.all([
        dockerApi.getImages(selectedServer),
        dockerApi.getContainers(selectedServer),
        dockerApi.getGpuStats(selectedServer)
      ]);

      setImages(imagesData);
      setContainers(containersData);
      setGpuStats(gpuData);
    } catch (error) {
      console.error('加载Docker数据失败:', error);
      message.error('加载Docker数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadServers();
  }, []);

  useEffect(() => {
    loadDockerData();
  }, [selectedServer]);

  const imageColumns: ColumnsType<DockerImage> = [
    {
      title: '镜像仓库',
      dataIndex: 'repository',
      key: 'repository',
    },
    {
      title: '标签',
      dataIndex: 'tag',
      key: 'tag',
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '镜像ID',
      dataIndex: 'image_id',
      key: 'image_id',
      render: (text) => <code>{text.substring(0, 12)}</code>,
    },
    {
      title: '创建时间',
      dataIndex: 'created',
      key: 'created',
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Popconfirm
            title="确定要删除这个镜像吗？"
            onConfirm={() => handleDeleteImage(record.image_id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const containerColumns: ColumnsType<DockerContainer> = [
    {
      title: '容器名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '镜像',
      dataIndex: 'image',
      key: 'image',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        const isRunning = text.includes('Up');
        return (
          <Tag color={isRunning ? 'green' : 'red'}>
            {isRunning ? '运行中' : '已停止'}
          </Tag>
        );
      },
    },
    {
      title: '端口映射',
      dataIndex: 'ports',
      key: 'ports',
    },
    {
      title: '创建时间',
      dataIndex: 'created',
      key: 'created',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        const isRunning = record.status.includes('Up');
        return (
          <Space size="middle">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewLogs(record.container_id)}
            >
              日志
            </Button>
            <Button
              type="link"
              icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={() => isRunning ? handleStopContainer(record.container_id) : handleStartContainer(record.container_id)}
            >
              {isRunning ? '停止' : '启动'}
            </Button>
            <Button
              type="link"
              icon={<ReloadOutlined />}
              onClick={() => handleRestartContainer(record.container_id)}
            >
              重启
            </Button>
            <Popconfirm
              title="确定要删除这个容器吗？"
              onConfirm={() => handleDeleteContainer(record.container_id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const handlePullImage = () => {
    let imageName = '';

    Modal.confirm({
      title: '拉取镜像',
      content: (
        <Input
          placeholder="例如: nginx:latest"
          onChange={(e) => imageName = e.target.value}
        />
      ),
      onOk: async () => {
        if (!imageName.trim()) {
          message.error('请输入镜像名称');
          return;
        }

        try {
          await dockerApi.pullImage(selectedServer, imageName);
          message.success('镜像拉取成功');
          loadDockerData(); // 重新加载数据
        } catch (error) {
          message.error('镜像拉取失败');
        }
      },
    });
  };

  const handleDeleteImage = async (imageId: string) => {
    try {
      await dockerApi.deleteImage(selectedServer, imageId);
      message.success('镜像删除成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('镜像删除失败');
    }
  };

  const handleStartContainer = async (containerId: string) => {
    try {
      await dockerApi.startContainer(selectedServer, containerId);
      message.success('容器启动成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器启动失败');
    }
  };

  const handleStopContainer = async (containerId: string) => {
    try {
      await dockerApi.stopContainer(selectedServer, containerId);
      message.success('容器停止成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器停止失败');
    }
  };

  const handleRestartContainer = async (containerId: string) => {
    try {
      await dockerApi.restartContainer(selectedServer, containerId);
      message.success('容器重启成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器重启失败');
    }
  };

  const handleDeleteContainer = async (containerId: string) => {
    try {
      await dockerApi.deleteContainer(selectedServer, containerId);
      message.success('容器删除成功');
      loadDockerData(); // 重新加载数据
    } catch (error) {
      message.error('容器删除失败');
    }
  };

  const handleViewLogs = async (containerId: string) => {
    try {
      const logs = await dockerApi.getContainerLogs(selectedServer, containerId);

      Modal.info({
        title: `容器日志 - ${containerId.substring(0, 12)}`,
        content: (
          <div style={{ maxHeight: 400, overflow: 'auto' }}>
            <pre style={{ fontSize: 12, whiteSpace: 'pre-wrap' }}>
              {(logs as any).logs || '暂无日志'}
            </pre>
          </div>
        ),
        width: 800,
      });
    } catch (error) {
      message.error('获取容器日志失败');
    }
  };

  const runningContainers = containers.filter(c => c.status.includes('Up')).length;
  const totalContainers = containers.length;

  return (
    <div>
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <strong>选择服务器:</strong>
          </Col>
          <Col span={8}>
            <Select
              value={selectedServer}
              onChange={setSelectedServer}
              style={{ width: '100%' }}
              placeholder="请选择服务器"
            >
              {servers.map(server => (
                <Select.Option key={server.id} value={server.id}>
                  {server.name} ({server.host})
                </Select.Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Card>

      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总镜像数"
              value={images.length}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总容器数"
              value={totalContainers}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中容器"
              value={runningContainers}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="容器运行率"
              value={totalContainers > 0 ? Math.round((runningContainers / totalContainers) * 100) : 0}
              suffix="%"
              valueStyle={{ color: runningContainers / totalContainers > 0.8 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="containers">
        <TabPane tab="容器管理" key="containers">
          <Card
            title="容器列表"
            extra={
              <Space>
                <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>
                  刷新
                </Button>
                <Button type="primary" icon={<PlusOutlined />}>
                  创建容器
                </Button>
              </Space>
            }
          >
            <Table
              columns={containerColumns}
              dataSource={containers}
              rowKey="container_id"
              loading={loading}
            />
          </Card>
        </TabPane>

        <TabPane tab="镜像管理" key="images">
          <Card
            title="镜像列表"
            extra={
              <Space>
                <Button icon={<ReloadOutlined />} onClick={() => setLoading(true)}>
                  刷新
                </Button>
                <Button type="primary" icon={<DownloadOutlined />} onClick={handlePullImage}>
                  拉取镜像
                </Button>
              </Space>
            }
          >
            <Table
              columns={imageColumns}
              dataSource={images}
              rowKey="image_id"
              loading={loading}
            />
          </Card>
        </TabPane>

        <TabPane tab="GPU监控" key="gpu">
          <Row gutter={16}>
            {gpuStats.map((gpu) => (
              <Col span={12} key={gpu.gpu_id} style={{ marginBottom: 16 }}>
                <Card title={`GPU ${gpu.gpu_id} - ${gpu.name}`}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <div style={{ marginBottom: 16 }}>
                        <div>GPU使用率</div>
                        <Progress
                          percent={gpu.utilization}
                          status={gpu.utilization > 80 ? 'exception' : 'normal'}
                        />
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{ marginBottom: 16 }}>
                        <div>显存使用率</div>
                        <Progress
                          percent={Math.round((gpu.memory_used / gpu.memory_total) * 100)}
                          status={gpu.memory_used / gpu.memory_total > 0.8 ? 'exception' : 'normal'}
                        />
                      </div>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="已用显存"
                        value={gpu.memory_used}
                        suffix="MB"
                        valueStyle={{ fontSize: 14 }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总显存"
                        value={gpu.memory_total}
                        suffix="MB"
                        valueStyle={{ fontSize: 14 }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default DockerManagement;
