#!/usr/bin/env python3
"""
简化的后端服务器，提供基本的API端点
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime
import random

app = FastAPI(
    title="设备管理系统API",
    version="1.0.0",
    description="设备管理系统 - 管理多台远端服务器的综合运维平台"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
mock_servers = [
    {
        "id": 1,
        "name": "Web服务器01",
        "host": "*************",
        "port": 22,
        "username": "root",
        "auth_type": "password",
        "description": "主要Web服务器",
        "tags": ["web", "production"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": 2,
        "name": "数据库服务器",
        "host": "*************",
        "port": 22,
        "username": "admin",
        "auth_type": "key",
        "description": "MySQL数据库服务器",
        "tags": ["database", "production"],
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "设备管理系统API",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat() + "Z",
        "services": {
            "api": "running"
        }
    }

# 服务器管理API
@app.get("/api/v1/servers")
async def get_servers():
    """获取服务器列表"""
    return mock_servers

@app.post("/api/v1/servers")
async def create_server(server_data: dict):
    """创建服务器"""
    new_server = {
        "id": len(mock_servers) + 1,
        **server_data,
        "monitoring_enabled": True,
        "alert_enabled": True,
        "is_active": True,
        "created_at": datetime.now().isoformat() + "Z",
        "updated_at": datetime.now().isoformat() + "Z"
    }
    mock_servers.append(new_server)
    return new_server

@app.post("/api/v1/servers/{server_id}/test-connection")
async def test_connection(server_id: int):
    """测试服务器连接"""
    return {
        "success": True,
        "message": "连接成功",
        "latency": random.randint(10, 100)
    }

# 脚本管理API
@app.get("/api/v1/scripts")
async def get_scripts():
    """获取脚本列表"""
    return [
        {
            "id": 1,
            "name": "系统信息收集",
            "description": "收集服务器基本系统信息",
            "content": "#!/bin/bash\nuname -a\ndf -h\nfree -m",
            "category": "系统监控",
            "tags": ["系统", "监控"],
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/scripts/executions")
async def get_executions():
    """获取执行记录"""
    return [
        {
            "id": 1,
            "script_id": 1,
            "server_id": 1,
            "status": "success",
            "output": "Linux server01 5.4.0-74-generic #83-Ubuntu",
            "error": "",
            "started_at": "2024-01-01T10:00:00Z",
            "finished_at": "2024-01-01T10:00:05Z"
        }
    ]

# 监控API
@app.get("/api/v1/monitoring/servers")
async def get_server_monitoring():
    """获取服务器监控状态"""
    return [
        {
            "id": 1,
            "name": "Web服务器01",
            "host": "*************",
            "status": random.choice(["online", "warning", "offline"]),
            "cpu_usage": round(random.uniform(10, 90), 2),
            "memory_usage": round(random.uniform(20, 85), 2),
            "disk_usage": round(random.uniform(30, 80), 2),
            "last_update": datetime.now().isoformat() + "Z"
        },
        {
            "id": 2,
            "name": "数据库服务器",
            "host": "*************",
            "status": "online",
            "cpu_usage": round(random.uniform(15, 75), 2),
            "memory_usage": round(random.uniform(40, 90), 2),
            "disk_usage": round(random.uniform(25, 70), 2),
            "last_update": datetime.now().isoformat() + "Z"
        }
    ]

@app.get("/api/v1/monitoring/alerts")
async def get_alerts():
    """获取告警列表"""
    return [
        {
            "id": 1,
            "server_id": 1,
            "server_name": "Web服务器01",
            "type": "cpu",
            "severity": "high",
            "message": "CPU使用率超过85%",
            "status": "active",
            "created_at": "2024-01-01T10:30:00Z",
            "acknowledged_at": None,
            "resolved_at": None
        }
    ]

# Docker API
@app.get("/api/v1/docker/{server_id}/images")
async def get_docker_images(server_id: int):
    """获取Docker镜像列表"""
    return [
        {
            "id": "sha256:abc123",
            "repository": "nginx",
            "tag": "latest",
            "size": "133MB",
            "created": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/docker/{server_id}/containers")
async def get_docker_containers(server_id: int):
    """获取Docker容器列表"""
    return [
        {
            "id": "container123",
            "name": "nginx-web",
            "image": "nginx:latest",
            "status": "running",
            "ports": ["80:80", "443:443"],
            "created": "2024-01-01T00:00:00Z"
        }
    ]

@app.get("/api/v1/docker/{server_id}/gpu-stats")
async def get_gpu_stats(server_id: int):
    """获取GPU统计信息"""
    return [
        {
            "id": 0,
            "name": "NVIDIA GeForce RTX 3080",
            "utilization": round(random.uniform(0, 100), 2),
            "memory_used": round(random.uniform(1000, 8000), 2),
            "memory_total": 8192,
            "temperature": round(random.uniform(40, 80), 2)
        }
    ]

if __name__ == "__main__":
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
