"""
应用配置模块
"""
import os
from typing import Optional, List
from pydantic import BaseModel, field_validator


class Settings(BaseModel):
    """应用配置类"""
    
    # 应用基础配置
    APP_NAME: str = "Device Management System"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"

    # API配置
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    # 数据库配置
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "mysql+pymysql://root:password@localhost:3306/device_management"
    )

    # Redis配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    # SSH配置
    SSH_TIMEOUT: int = 30
    SSH_KEEPALIVE_INTERVAL: int = 60
    SSH_MAX_CONNECTIONS: int = 100
    
    # Docker配置
    DOCKER_TIMEOUT: int = 300
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    UPLOAD_DIR: str = "/tmp/uploads"
    
    # 监控配置
    MONITORING_INTERVAL: int = 30  # seconds
    ALERT_THRESHOLDS: dict = {
        "cpu_usage": 80,
        "memory_usage": 85,
        "disk_usage": 90,
        "gpu_memory_usage": 90
    }
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "app.log")
    
    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080"
    ]
    
    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = 30
    WS_MAX_CONNECTIONS: int = 1000
    
    model_config = {
        "case_sensitive": True,
        "env_file": ".env"
    }


# 全局配置实例
settings = Settings()
