# 设备管理系统 (Device Management System)

一个用于管理多台远端服务器的综合运维平台，支持通过SSH隧道管理Docker容器、执行远程脚本和监控设备状态。

## 系统架构

```
+-------------------+       +-------------------+       +-------------------+
| 本地客户端        | <--SSH--> | SSH中继服务器     | <--SSH--> | 远端目标服务器   |
| (Web/CLI)         |       | (跳板机)          |       | (Docker宿主机)    |
+-------------------+       +-------------------+       +-------------------+
```

## 核心功能

### 1. SSH隧道管理
- 自动建立SSH隧道连接（支持跳板机/直连）
- 支持密钥认证/密码认证
- 连接池管理（复用SSH连接）
- 心跳检测与自动重连

### 2. Docker管理
- 镜像管理：拉取/删除/列表
- 容器管理：启动/停止/重启/删除/日志查看
- 容器状态监控（CPU/内存/网络/显存）

### 3. 远程脚本管理
- 脚本上传/下载
- 实时编辑器（Web端）
- 脚本执行与结果捕获
- 远程文件上传、下载、删除

### 4. 设备状态监控
- 系统资源监控（CPU/内存/磁盘/网络/显存）
- 容器资源使用统计
- 自定义监控脚本执行
- 异常告警（阈值触发）

## 技术栈

### 后端
- **Python 3.9+**: 核心服务
- **FastAPI**: Web API框架
- **SQLAlchemy**: ORM
- **MySQL**: 数据库
- **Paramiko**: SSH客户端
- **WebSocket**: 实时通信

### 前端
- **React 18**: 前端框架
- **TypeScript**: 类型安全
- **Ant Design**: UI组件库
- **Socket.IO**: 实时通信

### 部署
- **Docker**: 容器化部署
- **Nginx**: 反向代理
- **Redis**: 缓存和会话管理

## 项目结构

```
device-management-system/
├── backend/                 # 后端服务
│   ├── app/                # 应用核心
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── tests/             # 测试用例
│   └── requirements.txt   # Python依赖
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   └── utils/         # 工具函数
│   └── package.json       # Node.js依赖
├── docker/                # Docker配置
├── docs/                  # 文档
└── scripts/               # 部署脚本
```

## 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd device-management-system
```

2. 启动后端服务
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

3. 启动前端服务
```bash
cd frontend
npm install
npm start
```

4. 访问系统
- Web界面: http://localhost:3000
- API文档: http://localhost:8000/docs

## 安全特性

- 基于角色的访问控制（RBAC）
- 操作审计日志
- SSH密钥管理和加密存储
- API认证和授权
- 数据传输加密

## 许可证

MIT License
